package ru.sbertroika.tkp3.pro.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

@Table("report_d0")
data class PaymentReport(

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: UUID? = null,

    /**
     * Идентификатор организации
     */
    @Column("org_id")
    var orgId: UUID? = null,

    /**
     * Дата и время создания записи
     */
    @Column("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var createdAt: ZonedDateTime? = null,

    /**
     * Идентификатор транзакции
     */
    @Column("trx_id")
    var trxId: UUID? = null,

    /**
     * Тип транзакции
     */
    @Column("trx_type")
    var trxType: String? = null,

    /**
     * Версия транзакции
     */
    @Column("trx_v")
    var trxV: String? = null,

    /**
     * Дата и время регистрации записи в системе
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    /**
     * Серийный номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null,

    /**
     * Дата и время фискальной операции
     */
    @Column("fiscal_datetime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var fiscalDatetime: ZonedDateTime? = null,

    /**
     * Номер фискального накопителя
     */
    @Column("fiscal_storage_number")
    var fiscalStorageNumber: String? = null,

    /**
     * Заводской номер ККТ
     */
    @Column("manufacturer_kkt_number")
    var manufacturerKktNumber: String? = null,

    /**
     * Регистрационный номер ККТ
     */
    @Column("kkt_registration_number")
    var kktRegistrationNumber: String? = null,

    /**
     * Широта места проведения операции
     */
    @Column("latitude")
    var latitude: Double? = null,

    /**
     * Долгота места проведения операции
     */
    @Column("longitude")
    var longitude: Double? = null,

    /**
     * Номер смены
     */
    @Column("shift_num")
    var shiftNum: Int? = null,

    /**
     * Фискальный документ
     */
    @Column("fiscal_doc")
    var fiscalDoc: String? = null,

    /**
     * Единый номер операции за смену
     */
    @Column("ern")
    var ern: Int? = null,

    /**
     * Табельный номер водителя
     */
    @Column("driver_personal_number")
    var driverPersonalNumber: String? = null,

    /**
     * ФИО водителя
     */
    @Column("driver_fio")
    var driverFio: String? = null,

    /**
     * Табельный номер кондуктора
     */
    @Column("conductor_personal_number")
    var conductorPersonalNumber: String? = null,

    /**
     * ФИО кондуктора
     */
    @Column("conductor_fio")
    var conductorFio: String? = null,

    /**
     * Табельный номер кассира
     */
    @Column("cashier_personal_number")
    var cashierPersonalNumber: String? = null,

    /**
     * ФИО кассира
     */
    @Column("cashier_fio")
    var cashierFio: String? = null,

    /**
     * ИНН организации
     */
    @Column("org_inn")
    var orgInn: String? = null,

    /**
     * Наименование организации
     */
    @Column("org_name")
    var orgName: String? = null,

    /**
     * Тип транспортного средства
     */
    @Column("vh_type")
    var vhType: String? = null,

    /**
     * Номер транспортного средства
     */
    @Column("vh_number")
    var vhNumber: String? = null,

    /**
     * Номер маршрута
     */
    @Column("route_number")
    var routeNumber: String? = null,

    /**
     * Наименование маршрута
     */
    @Column("route_name")
    var routeName: String? = null,

    /**
     * Наименование тарифа
     */
    @Column("tariff_name")
    var tariffName: String? = null,

    /**
     * Идентификатор билета
     */
    @Column("ticket_id")
    var ticketId: UUID? = null,

    /**
     * Серия билета
     */
    @Column("ticket_series")
    var ticketSeries: String? = null,

    /**
     * Номер билета
     */
    @Column("ticket_number")
    var ticketNumber: String? = null,

    /**
     * Станция отправления
     */
    @Column("stantion_from")
    var stantionFrom: String? = null,

    /**
     * Станция назначения
     */
    @Column("stantion_to")
    var stantionTo: String? = null,

    /**
     * Сумма операции
     */
    @Column("amount")
    var amount: Float? = null,

    /**
     * Итоговая сумма
     */
    @Column("final_amount")
    var finalAmount: Float? = null,

    /**
     * Сумма корректировки
     */
    @Column("adjustment_amount")
    var adjustmentAmount: Float? = null,

    /**
     * Способ оплаты
     */
    @Column("pay_method")
    var payMethod: String? = null,

    /**
     * Идентификатор карты
     */
    @Column("crd_id")
    var crdId: UUID? = null,

    /**
     * Маскированный номер карты
     */
    @Column("masked_pan")
    var maskedPan: String? = null,

    /**
     * Платежная система
     */
    @Column("pay_system")
    var paySystem: String? = null,

    /**
     * Наименование продукта
     */
    @Column("product_name")
    var productName: String? = null,

    /**
     * Идентификатор терминала
     */
    @Column("tid")
    var tid: String? = null,

    /**
     * Идентификатор транспортного средства
     */
    @Column("vh_id")
    var vhId: UUID? = null,

    /**
     * Идентификатор маршрута
     */
    @Column("route_id")
    var routeId: UUID? = null,

    /**
     * Идентификатор станции отправления
     */
    @Column("stantion_from_id")
    var stantionFromId: UUID? = null,

    /**
     * Идентификатор станции назначения
     */
    @Column("stantion_to_id")
    var stantionToId: UUID? = null,

    /**
     * UID транспортной карты
     */
    @Column("card_uid")
    var cardUid: String? = null
) 