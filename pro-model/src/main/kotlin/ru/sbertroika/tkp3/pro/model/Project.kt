package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import ru.sbertroika.tkp3.pro.model.ProjectStatus.*
import java.sql.Timestamp
import java.util.*

/**
 * Составной первичный ключ для таблицы проектов
 * @param pId Уникальный идентификатор проекта
 * @param pVersion Версия записи проекта
 */
data class ProjectPK(
    val pId: UUID? = null,
    val pVersion: Int? = null
)

@Table("project")
data class Project(

    /** Уникальный идентификатор проекта */
    @Id
    @Column("pr_id")
    var id: UUID? = null,

    /** Версия записи проекта для поддержки версионирования */
    @Column("pr_version")
    var version: Int? = null,

    /** Дата и время создания версии */
    @Column("pr_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    @Column("pr_version_created_by")
    var versionCreatedBy: UUID? = null,

    /** Наименование проекта */
    @Column("pr_name")
    var name: String? = null,

    /** Статус проекта (ACTIVE, DRAFT, COMPLETED и т.д.) */
    @Column("pr_status")
    var status: ProjectStatus? = null,

    /** Идентификатор связанного договора (UUID) */
    @Column("pr_contract_id")
    var contractId: UUID? = null,

    /** Описание проекта */
    @Column("pr_description")
    var description: String? = null,

    /** Порядковый номер проекта в системе СТ */
    @Column("p_index")
    var index: Int? = null,

    /** Теги проекта в формате JSON */
    @Column("tags")
    var tags: String? = null
)

/**
 * Операторская организация
 */
data class OperatorOrganization(
    val id: UUID? = null,
    val name: String? = null,
    val role: String? = null
)

/**
 * Статусы проекта СберТройка ПРО
 *
 * Определяет возможные состояния проекта в системе.
 *
 * @property TEST Тестовый проект
 * @property DEMO Демонстрационный проект
 * @property ACTIVE Активный проект
 * @property ARCHIVE Архивный проект
 * @property NSI_CONFLICT Проект с конфликтом в нормативно-справочной информации
 * @property DISABLED Отключенный проект
 * @property BLOCKED Заблокированный проект
 * @property IS_DELETED Удаленный проект (логическое удаление)
 * @property DRAFT Черновик проекта
 * @property EXPIRING Истекающий проект
 * @property COMPLETED Завершенный проект
 */
enum class ProjectStatus {
    TEST, DEMO, ACTIVE, ARCHIVE, NSI_CONFLICT, DISABLED, BLOCKED, IS_DELETED, DRAFT, EXPIRING, COMPLETED
}