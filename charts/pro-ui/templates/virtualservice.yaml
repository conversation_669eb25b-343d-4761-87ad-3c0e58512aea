kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pro-ui
  namespace: {{ .Values.backendNamespaces.pro | default "pro" }}
spec:
  hosts:
    - {{ required "gateway.host is required" .Values.gateway.host }}
  gateways:
    - istio-ingressgateway/pro-ui-gateway
  http:
    - match:
        - uri:
            prefix: /api/pro/v1/
      route:
        - destination:
            host: pro-controller.{{ .Values.backendNamespaces.pro | default "pro" }}.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /api/abt/v1/
      route:
        - destination:
            host: abt-controller.{{ .Values.backendNamespaces.abt | default "abt" }}.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /api/tms/v1/
      route:
        - destination:
            host: tms-controller.{{ .Values.backendNamespaces.tms | default "tms" }}.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /api/pasiv/v1/
      route:
        - destination:
            host: pasiv-gate-private.{{ .Values.backendNamespaces.pasiv | default "pasiv" }}.svc.cluster.local
            port:
              number: 8080
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: pro-ui.{{ .Values.backendNamespaces.pro | default "pro" }}.svc.cluster.local
            port:
              number: 80
status: {}
