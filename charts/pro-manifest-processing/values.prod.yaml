# Default values for pro-manifest-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: pro-manifest-processing

replicaCount: 1

image:
  repository: pro-manifest-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000

resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi

env:
  system_user_id: "a223116c-49b4-43eb-8a47-c76a68a4225d"
  client:
    logging:
      enable: true
  kafka:
    servers: "kafka1-prod-new.tkp2.prod:9092;kafka2-prod-new.tkp2.prod:9092;kafka3-prod-new.tkp2.prod:9092"
  zookeeper:
    nodes: "kafka-zoo-01.st.corp:2181,kafka-zoo-02.st.corp:2181,kafka-zoo-03.st.corp:2181"
  service:
    abt_gate: "abt-gate.abt-prod.svc.cluster.local:5000"
    pasiv_gate: "pasiv-gate.pasiv-prod.svc.cluster.local:5000"
