# Default values for pro-manifest-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: pro-manifest-processing

replicaCount: 1

image:
  repository: pro-manifest-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

namespace: ''

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  system_user_id: "a223116c-49b4-43eb-8a47-c76a68a4225d"
  client:
    logging:
      enable: true
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
  zookeeper:
    nodes: "***********:2181,**********:2181,***********:2181"
  service:
    abt_gate: "abt-gate.abt.svc.cluster.local:5000"
    pasiv_gate: "pasiv-gate.pasiv.svc.cluster.local:5000"


livenessProbe:
  enabled: false
  path: /actuator/health/liveness
  port: 8080
  initialDelaySeconds: 10
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  enabled: false
  path: /actuator/health/readiness
  port: 8080
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1