apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "pro-manifest-processing.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "pro-manifest-processing.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "pro-manifest-processing.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "pro-manifest-processing.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "pro-manifest-processing.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "pro-manifest-processing.jar"]
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          {{- end }}
          env:
            - name: CLIENT_LOGGING_ENABLE
              value: {{ ternary "true" "false" .Values.env.client.logging.enable | quote }}
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
            - name: ZOOKEEPER_NODES
              value: {{ .Values.env.zookeeper.nodes }}
            - name: ABT_GATEWAY_URL
              value: {{ .Values.env.service.abt_gate }}
            - name: PASIV_GATEWAY_URL
              value: {{ .Values.env.service.pasiv_gate }}
            - name: SYSTEM_USER_ID
              value: {{ .Values.env.system_user_id }}
            - name: R2DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: r2url
            - name: S3_URL
              valueFrom:
                secretKeyRef:
                  name: pro
                  key: s3Url
            - name: S3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: pro
                  key: s3Bucket
            - name: S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: pro
                  key: s3AccessKey
            - name: S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: pro
                  key: s3SecretKey
            - name: ZOOKEEPER_NAMESPACE
              valueFrom:
                secretKeyRef:
                  name: pro
                  key: zooNamespace
            - name: RABBITMQ_HOST
              valueFrom:
                secretKeyRef:
                  name: rabbitmq
                  key: host
            - name: RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq
                  key: username
            - name: RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq
                  key: password
            - name: RABBITMQ_VHOST
              valueFrom:
                secretKeyRef:
                  name: rabbitmq
                  key: vhost
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
