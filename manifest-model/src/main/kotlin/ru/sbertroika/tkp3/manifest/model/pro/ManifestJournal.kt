package ru.sbertroika.tkp3.manifest.model.pro

import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

data class ManifestJournal(
    val jobId: UUID,
    val recordAt: ZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC")),
    val type: JournalOperationType,
    val projectId: UUID? = null,
    val projectVersion: Int? = null,
    val status: OperationStatus = OperationStatus.SUCCESS,
    val errors: List<String?> = emptyList(),
    val manifestDate: LocalDate? = null,
    val manifestVersion: Int? = null
)

enum class JournalOperationType {
    GEN_DAY_MANIFEST_JOB, GEN_DAY_MANIFEST_RESULT, CHANGE_MANIFEST_UPDATE_JOB, CHANGE_MANIFEST_RESULT,
}

enum class OperationStatus {
    SUCCESS, FAIL, NEW_JOB, RETRY_JOB, PROCESS_JOB
}