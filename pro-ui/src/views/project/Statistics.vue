<template>
    <div class="statistics-page">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Статистика</h1>
                <p class="text-gray-600 mt-2">Анализ и статистика платежных операций проекта</p>
            </div>
            <div class="flex gap-3">
                <Button
                    label="Обновить"
                    icon="pi pi-refresh"
                    @click="loadStats"
                    :loading="loading"
                    class="p-button-outlined"
                />
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    @click="exportStatistics"
                    class="p-button-outlined"
                />
            </div>
        </div>

        <!-- Статистика платежей -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">Статистика платежей</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="pi pi-wallet text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Всего платежей</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <i v-if="loading" class="pi pi-spin pi-spinner mr-2"></i>
                            {{ stats.totalPayments }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="pi pi-dollar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Общая сумма</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <i v-if="loading" class="pi pi-spin pi-spinner mr-2"></i>
                            {{ formatCurrency(stats.totalAmount) }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="pi pi-building text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Организаций</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <i v-if="loading" class="pi pi-spin pi-spinner mr-2"></i>
                            {{ stats.organizationsCount }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="pi pi-route text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Маршрутов</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <i v-if="loading" class="pi pi-spin pi-spinner mr-2"></i>
                            {{ stats.routesCount }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Дополнительная статистика -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Статистика по способам оплаты -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <h3 class="text-lg font-semibold mb-4">Статистика по способам оплаты</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <i class="pi pi-credit-card text-blue-600 mr-2"></i>
                            <span>Банковские карты</span>
                        </div>
                        <span class="font-semibold">{{ stats.cardPayments || 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <i class="pi pi-money-bill text-green-600 mr-2"></i>
                            <span>Наличные</span>
                        </div>
                        <span class="font-semibold">{{ stats.cashPayments || 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <i class="pi pi-mobile text-purple-600 mr-2"></i>
                            <span>Мобильные платежи</span>
                        </div>
                        <span class="font-semibold">{{ stats.mobilePayments || 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <i class="pi pi-qrcode text-orange-600 mr-2"></i>
                            <span>QR-код</span>
                        </div>
                        <span class="font-semibold">{{ stats.qrPayments || 0 }}</span>
                    </div>
                </div>
            </div>

            <!-- Статистика по периодам -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <h3 class="text-lg font-semibold mb-4">Статистика по периодам</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span>Сегодня</span>
                        <span class="font-semibold">{{ formatCurrency(stats.todayAmount || 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span>Вчера</span>
                        <span class="font-semibold">{{ formatCurrency(stats.yesterdayAmount || 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span>За неделю</span>
                        <span class="font-semibold">{{ formatCurrency(stats.weekAmount || 0) }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span>За месяц</span>
                        <span class="font-semibold">{{ formatCurrency(stats.monthAmount || 0) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Графики и диаграммы (заглушка) -->
        <div class="mt-6">
            <div class="bg-white p-6 rounded-lg shadow border">
                <h3 class="text-lg font-semibold mb-4">Графики и диаграммы</h3>
                <div class="text-center p-8">
                    <i class="pi pi-chart-bar text-4xl text-gray-400 mb-3"></i>
                    <p class="text-gray-600">Модуль графиков и диаграмм в разработке</p>
                    <p class="text-sm text-gray-500 mt-2">Здесь будут отображаться графики динамики платежей, распределения по способам оплаты и другие аналитические данные</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, ref} from 'vue';
import {useRoute} from 'vue-router';
import {PaymentService} from '@/service/PaymentService';

const route = useRoute();
const projectId = route.params.projectId;

// Состояние
const loading = ref(false);
const stats = ref({
    totalPayments: 0,
    totalAmount: 0,
    organizationsCount: 0,
    routesCount: 0,
    cardPayments: 0,
    cashPayments: 0,
    mobilePayments: 0,
    qrPayments: 0,
    todayAmount: 0,
    yesterdayAmount: 0,
    weekAmount: 0,
    monthAmount: 0
});

// Методы
const exportStatistics = () => {
    console.log('Экспорт статистики...');
    // Здесь будет логика экспорта статистики
};

const formatCurrency = (amount) => {
    if (!amount) return '0.00 ₽';
    // Конвертируем копейки в рубли
    const rubles = amount / 100;
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(rubles);
};

// Загрузка статистики
const loadStats = async () => {
    try {
        loading.value = true;

        // Загружаем все данные асинхронно одновременно
        const [basicStats, methodStats, periodStats] = await Promise.all([
            PaymentService.getPaymentStats(projectId),
            PaymentService.getPaymentMethodStats(projectId),
            PaymentService.getPaymentPeriodStats(projectId)
        ]);

        // Объединяем данные из всех источников
        const basicData = basicStats?.data || basicStats || {};
        const methodData = methodStats?.data || methodStats || {};
        const periodData = periodStats?.data || periodStats || {};

        stats.value = {
            ...stats.value,
            ...basicData,
            ...methodData,
            ...periodData
        };

    } catch (error) {
        console.error('Ошибка загрузки статистики:', error);
        // В случае ошибки используем мок данные
        stats.value = {
            totalPayments: 1247,
            totalAmount: 456789,
            organizationsCount: 4,
            routesCount: 8,
            cardPayments: 856,
            cashPayments: 234,
            mobilePayments: 157,
            qrPayments: 0,
            todayAmount: 12345,
            yesterdayAmount: 15678,
            weekAmount: 89012,
            monthAmount: 345678
        };
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    loadStats();
});
</script>

<style scoped>
.statistics-page {
    padding: 1.5rem;
}
</style>
