<script setup>
import {useRoute, useRouter} from 'vue-router';
import {onMounted, ref} from 'vue';
import {proApiClient} from '@/service/ApiClient';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;
const productId = route.params.productId;

const isEdit = ref(!!productId);
const loading = ref(false);
const saving = ref(false);
const error = ref(null);

const product = ref({
    name: '',
    status: 'ACTIVE',
    tags: ''
});

const statusOptions = [
    { label: 'Активный', value: 'ACTIVE' },
    { label: 'Отключен', value: 'DISABLED' },
    { label: 'Заблокирован', value: 'BLOCKED' }
];

onMounted(() => {
    if (isEdit.value) {
        loadProduct();
    }
});

const loadProduct = async () => {
    try {
        loading.value = true;
        error.value = null;

        const response = await proApiClient.get(`/api/pro/v1/products/${productId}`);
        console.log('Загруженный продукт:', response);

        if (response.data) {
            product.value = {
                name: response.data.name || '',
                status: response.data.status || 'ACTIVE',
                tags: response.data.tags || ''
            };
        }
    } catch (err) {
        console.error('Ошибка загрузки продукта:', err);
        error.value = err.message || 'Ошибка загрузки продукта';
    } finally {
        loading.value = false;
    }
};

const saveProduct = async () => {
    try {
        saving.value = true;
        error.value = null;

        const productData = {
            name: product.value.name,
            status: product.value.status,
            tags: product.value.tags
        };

        let response;
        if (isEdit.value) {
            response = await proApiClient.put(`/api/pro/v1/products/${productId}`, productData);
        } else {
            response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/products`, productData);
        }

        console.log('Сохраненный продукт:', response);

        // Перенаправляем на список продуктов
        router.push(`/pro/${projectId}/nsi/product`);
    } catch (err) {
        console.error('Ошибка сохранения продукта:', err);
        error.value = err.message || 'Ошибка сохранения продукта';
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectId}/nsi/product`);
};

const validateForm = () => {
    if (!product.value.name || product.value.name.trim() === '') {
        error.value = 'Название продукта обязательно для заполнения';
        return false;
    }
    return true;
};

const handleSubmit = () => {
    if (validateForm()) {
        saveProduct();
    }
};
</script>

<template>
    <div class="product-form-container">
        <!-- Заголовок -->
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold text-primary m-0">
                    {{ isEdit ? 'Редактирование продукта' : 'Создание продукта' }}
                </h1>
                <p class="text-color-secondary m-0 mt-1">
                    {{ isEdit ? 'Изменение данных продукта' : 'Добавление нового продукта в проект' }}
                </p>
            </div>
            <div class="flex gap-2">
                <Button
                    label="Отмена"
                    icon="pi pi-times"
                    outlined
                    @click="cancel"
                />
                <Button
                    label="Сохранить"
                    icon="pi pi-check"
                    :loading="saving"
                    @click="handleSubmit"
                />
            </div>
        </div>

        <!-- Форма -->
        <div class="card">
            <div v-if="loading" class="flex justify-content-center p-4">
                <ProgressSpinner />
            </div>

            <form v-else @submit.prevent="handleSubmit" class="p-4">
                <div class="grid">
                    <!-- Название продукта -->
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="name" class="block text-sm font-medium mb-2">
                                Название продукта <span class="text-red-500">*</span>
                            </label>
                            <InputText
                                id="name"
                                v-model="product.name"
                                placeholder="Введите название продукта"
                                class="w-full"
                                :class="{ 'p-invalid': error && !product.name }"
                            />
                            <small v-if="error && !product.name" class="p-error">
                                Название продукта обязательно для заполнения
                            </small>
                        </div>
                    </div>

                    <!-- Статус -->
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="status" class="block text-sm font-medium mb-2">
                                Статус
                            </label>
                            <Dropdown
                                id="status"
                                v-model="product.status"
                                :options="statusOptions"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите статус"
                                class="w-full"
                            />
                        </div>
                    </div>

                    <!-- Теги -->
                    <div class="col-12">
                        <div class="field">
                            <label for="tags" class="block text-sm font-medium mb-2">
                                Теги
                            </label>
                            <Textarea
                                id="tags"
                                v-model="product.tags"
                                placeholder="Введите теги (через запятую)"
                                rows="3"
                                class="w-full"
                            />
                            <small class="text-color-secondary">
                                Теги помогают классифицировать и группировать продукты
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4 pt-4 border-top-1 surface-border">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                    />
                    <Button
                        label="Сохранить"
                        icon="pi pi-check"
                        :loading="saving"
                        @click="handleSubmit"
                    />
                </div>
            </form>
        </div>

        <!-- Сообщение об ошибке -->
        <Toast v-if="error" severity="error" :life="5000" />
    </div>
</template>

<style scoped>
.product-form-container {
    padding: 1rem;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.field {
    margin-bottom: 1.5rem;
}

.p-invalid {
    border-color: #f44336;
}

.p-error {
    color: #f44336;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.border-top-1 {
    border-top: 1px solid;
}

.surface-border {
    border-color: #e5e7eb;
}
</style>
