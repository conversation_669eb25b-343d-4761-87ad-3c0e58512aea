<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {useConfirm} from 'primevue/useconfirm';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import AutoComplete from 'primevue/autocomplete';
import Tag from 'primevue/tag';
import ProgressSpinner from 'primevue/progressspinner';
import ConfirmDialog from 'primevue/confirmdialog';
import {VehicleService} from '@/service/VehicleService';
import {OrganizationService} from '@/service/OrganizationService';
import {useAuthStore} from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const confirm = useConfirm();

const projectId = route.params.projectId;

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

// Состояние
const loading = ref(false);
const vehicles = ref([]);
const organizations = ref([]);
const filteredOrganizations = ref([]);
const organizationSearchLoading = ref(false);

// Фильтры
const filters = ref({
    search: '',
    type: null,
    status: null,
    organizationId: null
});

// Пагинация
const pagination = ref({
    currentPage: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
});

// Опции для фильтров
const typeOptions = computed(() => [
    { label: 'Все типы', value: null },
    ...VehicleService.getVehicleTypeOptions()
]);

const statusOptions = computed(() => [
    { label: 'Все статусы', value: null },
    ...VehicleService.getVehicleStatusOptions()
]);

const organizationOptions = computed(() => [
    { label: 'Все организации', value: null },
    ...organizations.value.map(org => ({
        label: org.name,
        value: org.id
    }))
]);

onMounted(async () => {
    await loadOrganizations();
    await loadVehicles();
});

const loadOrganizations = async () => {
    try {
        const response = await OrganizationService.getOrganizationsByProject(projectId);
        organizations.value = response.data || [];
        
        // Инициализируем отфильтрованный список организаций
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
        filteredOrganizations.value = [];
    }
};

const searchOrganizations = async (event) => {
    const query = event.query;
    if (!query || query.length < 2) {
        // Проверяем, что organizations.value существует и является массивом
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
        return;
    }

    try {
        organizationSearchLoading.value = true;
        const data = await OrganizationService.searchOrganizationsByName(query, 0, 20);
        filteredOrganizations.value = data || [];
    } catch (error) {
        console.error('Ошибка поиска организаций:', error);
        filteredOrganizations.value = [];
    } finally {
        organizationSearchLoading.value = false;
    }
};

const loadVehicles = async () => {
    loading.value = true;
    try {
        const response = await VehicleService.getVehicles(
            projectId,
            filters.value,
            pagination.value.currentPage,
            pagination.value.size
        );

        // VehicleService.getVehicles возвращает данные напрямую в формате {content, totalElements, ...}
        if (response && response.content) {
            vehicles.value = response.content || [];
            pagination.value = {
                currentPage: response.currentPage || 0,
                size: response.size || 10,
                totalElements: response.totalElements || 0,
                totalPages: response.totalPages || 0
            };
        } else {
            vehicles.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки транспортных средств:', error);
    } finally {
        loading.value = false;
    }
};

const onFilterChange = () => {
    pagination.value.currentPage = 0;
    loadVehicles();
};

const onPageChange = (event) => {
    pagination.value.currentPage = event.page;
    loadVehicles();
};

const navigateToCreate = () => {
    router.push({ name: 'pro_project_nsi_vehicle_create', params: { projectId } });
};

const viewVehicle = (vehicleId) => {
    router.push({ name: 'pro_project_nsi_vehicle_detail', params: { projectId, vehicleId } });
};

const editVehicle = (vehicleId) => {
    router.push({ name: 'pro_project_nsi_vehicle_edit', params: { projectId, vehicleId } });
};

const deleteVehicle = (vehicleId) => {
    confirm.require({
        message: 'Вы уверены, что хотите удалить это транспортное средство?',
        header: 'Подтверждение удаления',
        icon: 'pi pi-exclamation-triangle',
        accept: () => performDelete(vehicleId),
        reject: () => {}
    });
};

const performDelete = async (vehicleId) => {
    try {
        const response = await VehicleService.deleteVehicle(vehicleId, 'current-user-id');
        if (response.success) {
            await loadVehicles();
        }
    } catch (error) {
        console.error('Ошибка удаления транспортного средства:', error);
    }
};

const getVehicleTypeLabel = (type) => {
    return VehicleService.getVehicleTypeLabel(type);
};

const getVehicleStatusLabel = (status) => {
    return VehicleService.getVehicleStatusLabel(status);
};

const getTypeSeverity = (type) => {
    const severities = {
        'BUS': 'info',
        'TRAM': 'warning',
        'TROLLEYBUS': 'success',
        'METRO': 'danger'
    };
    return severities[type] || 'secondary';
};

const getStatusSeverity = (status) => {
    const severities = {
        'ACTIVE': 'success',
        'DISABLED': 'warning',
        'BLOCKED': 'danger',
        'IS_DELETED': 'secondary'
    };
    return severities[status] || 'secondary';
};

const getOrganizationName = (organizationId) => {
    const organization = organizations.value.find(org => org.id === organizationId);
    return organization ? organization.name : 'Не указана';
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU');
};
</script>

<template>
    <div class="vehicle-list">

        <!-- Заголовок -->
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Транспортные средства</h2>
            <div class="flex gap-2 ml-auto">
                <Button
                    label="Добавить ТС"
                    icon="pi pi-plus"
                    @click="navigateToCreate"
                />
                <Button
                    v-if="showAllFeatures"
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    v-if="showAllFeatures"
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <div class="grid">
                <div class="col-12 md:col-3">
                    <div class="field">
                        <label for="search" class="font-medium">Поиск</label>
                        <InputText
                            id="search"
                            v-model="filters.search"
                            placeholder="Номер ТС..."
                            class="w-full"
                            @input="onFilterChange"
                        />
                    </div>
                </div>
                <div class="col-12 md:col-3">
                    <div class="field">
                        <label for="type" class="font-medium">Тип ТС</label>
                        <Dropdown
                            id="type"
                            v-model="filters.type"
                            :options="typeOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Все типы"
                            class="w-full"
                            @change="onFilterChange"
                        />
                    </div>
                </div>
                <div class="col-12 md:col-3" v-if="showAllFeatures">
                    <div class="field">
                        <label for="status" class="font-medium">Статус</label>
                        <Dropdown
                            id="status"
                            v-model="filters.status"
                            :options="statusOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Все статусы"
                            class="w-full"
                            @change="onFilterChange"
                        />
                    </div>
                </div>
                <div class="col-12 md:col-3" v-if="showAllFeatures">
                    <div class="field">
                        <label for="organization" class="font-medium">Организация</label>
                        <AutoComplete
                            id="organization"
                            v-model="filters.organizationId"
                            :suggestions="filteredOrganizations"
                            @complete="searchOrganizations"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Начните вводить название организации..."
                            class="w-full"
                            :loading="organizationSearchLoading"
                            :delay="300"
                            :minLength="2"
                            :dropdown="true"
                            :forceSelection="true"
                            @change="onFilterChange"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable
                :value="vehicles"
                :loading="loading"
                :paginator="true"
                :rows="pagination.size"
                :totalRecords="pagination.totalElements"
                :lazy="true"
                @page="onPageChange"
                responsiveLayout="scroll"
                :pt="{
                    table: { style: 'min-width: 50rem' }
                }"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-car text-2xl text-color-secondary mb-2"></i>
                        <p class="text-color-secondary">Транспортные средства не найдены</p>
                        <small class="text-color-secondary">
                            Попробуйте изменить фильтры или добавьте новое транспортное средство
                        </small>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <ProgressSpinner />
                        <p class="mt-2">Загрузка транспортных средств...</p>
                    </div>
                </template>

                <Column field="number" header="Номер ТС" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="font-semibold">{{ data.number }}</div>
                    </template>
                </Column>

                <Column field="type" header="Тип" style="width: 120px">
                    <template #body="{ data }">
                        <Tag :value="getVehicleTypeLabel(data.type)" :severity="getTypeSeverity(data.type)" />
                    </template>
                </Column>

                <Column field="status" header="Статус" style="width: 120px">
                    <template #body="{ data }">
                        <Tag :value="getVehicleStatusLabel(data.status)" :severity="getStatusSeverity(data.status)" />
                    </template>
                </Column>

                <Column field="organizationId" header="Организация" style="min-width: 200px">
                    <template #body="{ data }">
                        <span>{{ getOrganizationName(data.organizationId) }}</span>
                    </template>
                </Column>

                <Column field="versionCreatedAt" header="Дата создания" style="width: 150px">
                    <template #body="{ data }">
                        <span class="text-sm">{{ formatDate(data.versionCreatedAt) }}</span>
                    </template>
                </Column>

                <Column header="Действия" style="width: 150px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewVehicle(data.id)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editVehicle(data.id)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteVehicle(data.id)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- Диалог подтверждения удаления -->
        <ConfirmDialog />
    </div>
</template>

<style scoped>
.vehicle-list {
    padding: 1rem;
}
</style>
