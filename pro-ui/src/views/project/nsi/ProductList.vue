<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {FilterMatchMode} from '@primevue/core/api';
import {proApiClient} from '@/service/ApiClient';
import {useAuthStore} from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

const products = ref([]);
const loading = ref(true);
const error = ref(null);
const pagination = ref({
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
});

// Фильтры для API
const apiFilters = ref({
    name: null,
    status: null
});

// Фильтры для DataTable (локальные)
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadProducts();
});

const loadProducts = async (page = 0) => {
    try {
        loading.value = true;
        error.value = null;

        const params = {
            page: page,
            size: pagination.value.size
        };

        if (apiFilters.value.name) params.name = apiFilters.value.name;
        if (apiFilters.value.status) params.status = apiFilters.value.status;

        const response = await proApiClient.get(`/api/pro/v1/projects/${projectId}/products`, params);
        console.log('Загруженные продукты:', response);

        if (response.data && response.data.content) {
            products.value = response.data.content;
            pagination.value = {
                page: response.data.pagination.page || page,
                size: response.data.pagination.size || pagination.value.size,
                totalElements: response.data.pagination.totalElements || 0,
                totalPages: response.data.pagination.totalPages || 0
            };
        } else {
            products.value = [];
        }
    } catch (err) {
        console.error('Ошибка загрузки продуктов:', err);
        error.value = err.message || 'Ошибка загрузки продуктов';
        products.value = [];
    } finally {
        loading.value = false;
    }
};

const onPageChange = (event) => {
    loadProducts(event.page);
};

const applyFilters = () => {
    // Обновляем API фильтры из локальных фильтров
    apiFilters.value = {
        name: filters.value.name?.value || null,
        status: filters.value.status?.value || null
    };
    loadProducts(0); // Сбрасываем на первую страницу
};

const addProduct = () => {
    router.push(`/pro/${projectId}/nsi/product/create`);
};

const editProduct = (product) => {
    router.push(`/pro/${projectId}/nsi/product/${product.id}/edit`);
};

const deleteProduct = async (product) => {
    try {
        await proApiClient.delete(`/api/pro/v1/products/${product.id}`);
        await loadProducts(pagination.value.page);
    } catch (err) {
        console.error('Ошибка удаления продукта:', err);
        error.value = err.message || 'Ошибка удаления продукта';
    }
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'ACTIVE': return 'success';
        case 'DISABLED': return 'warning';
        case 'BLOCKED': return 'danger';
        case 'IS_DELETED': return 'secondary';
        default: return 'info';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'ACTIVE': return 'Активный';
        case 'DISABLED': return 'Отключен';
        case 'BLOCKED': return 'Заблокирован';
        case 'IS_DELETED': return 'Удален';
        default: return status;
    }
};

const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('ru-RU', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const goBack = () => {
    router.push(`/pro/${projectId}`);
};
</script>

<template>
    <div class="product-list-container">
        <!-- Заголовок -->
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold text-primary m-0">Продукты и сервисы</h1>
            </div>
            <div class="flex gap-2 ml-auto">
                <Button
                    label="Добавить продукт"
                    icon="pi pi-plus"
                    @click="addProduct"
                />
            </div>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4" v-if="showAllFeatures">
            <div class="flex flex-column gap-3">
                <div class="flex gap-3">
                    <div class="flex-1">
                        <label for="nameFilter" class="block text-sm font-medium mb-2">Название</label>
                        <InputText
                            id="nameFilter"
                            v-model="filters.name.value"
                            placeholder="Поиск по названию..."
                            class="w-full"
                        />
                    </div>
                    <div class="flex-1">
                        <label for="statusFilter" class="block text-sm font-medium mb-2">Статус</label>
                        <Dropdown
                            id="statusFilter"
                            v-model="filters.status.value"
                            :options="[
                                { label: 'Все', value: null },
                                { label: 'Активный', value: 'ACTIVE' },
                                { label: 'Отключен', value: 'DISABLED' },
                                { label: 'Заблокирован', value: 'BLOCKED' }
                            ]"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите статус"
                            class="w-full"
                        />
                    </div>
                    <div class="flex align-items-end">
                        <Button
                            label="Применить фильтры"
                            icon="pi pi-filter"
                            @click="applyFilters"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Таблица -->
        <div class="card">
            <DataTable
                :value="products"
                :loading="loading"
                :paginator="true"
                :rows="pagination.size"
                :totalRecords="pagination.totalElements"
                :lazy="true"
                :filters="filters"
                filterDisplay="menu"
                :globalFilterFields="['name', 'status']"
                dataKey="id"
                :rowsPerPageOptions="[10, 20, 50]"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="Показано {first} - {last} из {totalRecords} записей"
                @page="onPageChange"
                responsiveLayout="scroll"
                class="p-datatable-sm"
            >
                <template #header  v-if="showAllFeatures">
                    <div class="flex justify-content-between align-items-center">
                        <h5 class="m-0">Список продуктов</h5>
                        <span class="p-input-icon-left">
                            <i class="pi pi-search" />
                            <InputText
                                v-model="filters.global.value"
                                placeholder="Поиск..."
                                class="p-inputtext-sm"
                            />
                        </span>
                    </div>
                </template>

                <Column field="name" header="Название" sortable>
                    <template #body="{ data }">
                        <div class="font-medium">{{ data.name }}</div>
                    </template>
                </Column>

                <Column field="status" header="Статус" sortable>
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column field="versionCreatedAt" header="Дата создания" sortable>
                    <template #body="{ data }">
                        <span class="text-sm">{{ formatDate(data.versionCreatedAt) }}</span>
                    </template>
                </Column>

                <Column field="tags" header="Теги">
                    <template #body="{ data }">
                        <span class="text-sm text-color-secondary">{{ data.tags || '-' }}</span>
                    </template>
                </Column>

                <Column header="Действия" :exportable="false" style="min-width: 8rem">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                outlined
                                rounded
                                size="small"
                                @click="editProduct(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                outlined
                                rounded
                                severity="danger"
                                size="small"
                                @click="deleteProduct(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- Сообщение об ошибке -->
        <Toast v-if="error" severity="error" :life="5000" />
    </div>
</template>

<style scoped>
.product-list-container {
    padding: 1rem;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.5rem;
    font-size: 0.875rem;
}

.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.5rem;
    font-size: 0.875rem;
}
</style>
