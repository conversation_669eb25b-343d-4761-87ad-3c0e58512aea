<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref, watch} from 'vue';
import {FilterMatchMode} from '@primevue/core/api';
import {DriverService} from '@/service/DriverService';
import {OrganizationService} from '@/service/OrganizationService';
import {useAuthStore} from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

const drivers = ref([]);
const loading = ref(true);
const organizations = ref([]);
const pagination = ref({
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
});
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    personnelNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    fullName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    organizationId: { value: null, matchMode: FilterMatchMode.EQUALS },
    cardPan: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        personnelNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        fullName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        organizationId: { value: null, matchMode: FilterMatchMode.EQUALS },
        cardPan: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

// Автоматический поиск при вводе более 2 символов
watch(() => filters.value.global.value, (newValue) => {
    if (newValue && newValue.length >= 2) {
        // Сбрасываем страницу при поиске
        pagination.value.page = 0;
        // Добавляем небольшую задержку для предотвращения частых запросов
        setTimeout(() => {
            loadDrivers();
        }, 300);
    } else if (newValue === null || newValue === '') {
        // При очистке поиска также перезагружаем
        pagination.value.page = 0;
        loadDrivers();
    }
});

onMounted(() => {
    loadOrganizations();
    loadDrivers();
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const loadDrivers = async () => {
    try {
        loading.value = true;
        
        // Подготавливаем параметры для API
        const apiFilters = {};
        if (filters.value.global.value && filters.value.global.value.length >= 2) {
            apiFilters.fullName = filters.value.global.value;
        }
        if (filters.value.personnelNumber.value) {
            apiFilters.personalNumber = filters.value.personnelNumber.value;
        }
        if (filters.value.organizationId.value) {
            apiFilters.organizationId = filters.value.organizationId.value;
        }
        if (filters.value.role && filters.value.role.value) {
            apiFilters.role = filters.value.role.value;
        }

        const data = await DriverService.getDriversByProject(projectId, apiFilters, {
            page: pagination.value.page,
            size: pagination.value.size
        });

        console.log('=== Данные с сервера ===');
        console.log('Полный ответ:', data);
        console.log('Content:', data.content);
        if (data.content && data.content.length > 0) {
            console.log('Первый сотрудник:', data.content[0]);
            console.log('Поля первого сотрудника:', Object.keys(data.content[0]));
        }
        console.log('========================');

        // API возвращает объект с пагинацией
        drivers.value = data.content || [];
        pagination.value.totalElements = data.totalElements || 0;
        pagination.value.totalPages = data.totalPages || 0;
    } catch (error) {
        console.error('Ошибка загрузки сотрудников:', error);
        drivers.value = [];
        pagination.value.totalElements = 0;
        pagination.value.totalPages = 0;
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
    pagination.value.page = 0; // Сбрасываем на первую страницу
    loadDrivers();
};

const refreshDrivers = () => {
    pagination.value.page = 0; // Сброс на первую страницу
    loadDrivers();
};

const onPageChange = (event) => {
    console.log('onPageChange вызван с event:', event);
    // Правильно обрабатываем событие пагинации
    pagination.value.page = event.page;
    pagination.value.size = event.rows;
    console.log('Новые значения пагинации:', pagination.value);
    loadDrivers();
};

const createDriver = () => {
    router.push(`/pro/${projectId}/nsi/driver/create`);
};

const editDriver = (driver) => {
    // Используем profileId вместо id, так как API возвращает идентификатор в поле profileId
    const employeeId = driver.profileId || driver.id;
    router.push(`/pro/${projectId}/nsi/driver/${employeeId}/edit`);
};

const deleteDriver = async (driver) => {
    try {
        const employeeId = driver.profileId || driver.id;
        await DriverService.deleteDriver(employeeId);
        // Обновляем список после удаления
        refreshDrivers();
    } catch (error) {
        console.error('Ошибка при удалении сотрудника:', error);
    }
};

const importFromFile = () => {
    console.log('Загрузить сотрудников из файла для проекта:', projectId);
};

const exportData = () => {
    console.log('Экспорт сотрудников проекта:', projectId);
};

const maskCardPan = (pan) => {
    if (!pan || pan.length < 4) return pan;
    return '**** **** **** ' + pan.slice(-4);
};

const maskPinCode = (pin) => {
    return pin ? '****' : '';
};

const getOrganizationName = (organizationId) => {
    const org = organizations.value.find(o => o.id === organizationId);
    return org ? org.name : 'Неизвестная организация';
};

const getFullName = (driver) => {
    const parts = [driver.surname, driver.name, driver.middleName].filter(Boolean);
    return parts.join(' ');
};

const getInitials = (driver) => {
    const surname = driver.surname || '';
    const name = driver.name || '';
    return (surname.charAt(0) + name.charAt(0)).toUpperCase();
};
</script>

<template>
    <div class="driver-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Сотрудники</h2>
            <div class="flex gap-2 ml-auto">
                <Button
                    label="Добавить сотрудника"
                    icon="pi pi-plus"
                    @click="createDriver"
                />
                <Button
                    v-if="showAllFeatures"
                    label="Загрузить из файла"
                    icon="pi pi-upload"
                    outlined
                    @click="importFromFile"
                />
                <Button
                    v-if="showAllFeatures"
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="drivers"
                :paginator="true"
                :rows="pagination.size"
                :totalRecords="pagination.totalElements"
                :lazy="true"
                dataKey="profileId"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['personalNumber', 'surname', 'name', 'middleName']"
                showGridlines
                responsiveLayout="scroll"
                @page="onPageChange"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Сотрудники не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="personalNumber" header="Табельный номер" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold">{{ data.personalNumber }}</span>
                    </template>
                </Column>

                <Column field="surname" header="ФИО" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ФИО"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <Avatar
                                :label="getInitials(data)"
                                size="normal"
                                shape="circle"
                                class="mr-2"
                            />
                            <span>{{ getFullName(data) }}</span>
                        </div>
                    </template>
                </Column>

                <Column field="organizationId" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="organizations"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Выберите организацию"
                            showClear
                            class="w-full"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ getOrganizationName(data.organizationId) }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Пин-код" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-lock mr-2 text-color-secondary"></i>
                            <span class="font-mono">{{ maskPinCode(data.pinCode) }}</span>
                        </div>
                    </template>
                </Column>

                <Column field="cardPan" header="PAN служебной карты" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по PAN"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-credit-card mr-2 text-color-secondary"></i>
                            <span class="font-mono">{{ maskCardPan(data.cardPan) }}</span>
                        </div>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editDriver(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteDriver(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.driver-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
