<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {DriverService} from '@/service/DriverService';
import {OrganizationService} from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;
const driverId = route.params.driverId;
const isEdit = computed(() => !!driverId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);
const filteredOrganizations = ref([]);
const organizationSearchLoading = ref(false);

// Роли сотрудников
const roles = ref([
    { label: 'Водитель', value: 'driver' },
    { label: 'Администратор', value: 'admin' },
    { label: 'Кассир', value: 'cashier' },
    { label: 'Инкассатор', value: 'collector' },
    { label: 'Кондуктор', value: 'conductor' },
    { label: 'Контролер', value: 'controller' }
]);

// Форма данных
const form = ref({
    name: '',
    surname: '',
    middleName: '',
    role: 'driver',
    personalNumber: '',
    pinCode: '',
    organizationId: null,
    login: '',
    email: '',
    phone: ''
});

// Валидация
const errors = ref({});

// Вычисляемое свойство для отображения названия выбранной организации
const selectedOrganizationName = computed(() => {
    if (!form.value.organizationId) return '';

    // Ищем организацию в загруженном списке
    const organization = organizations.value.find(org => org.id === form.value.organizationId);
    if (organization) return organization.name;

    // Ищем в отфильтрованном списке
    const filteredOrg = filteredOrganizations.value.find(org => org.id === form.value.organizationId);
    if (filteredOrg) return filteredOrg.name;

    return '';
});

onMounted(async () => {
    await loadOrganizations();

    // Инициализируем отфильтрованный список организаций
    if (organizations.value && Array.isArray(organizations.value)) {
        filteredOrganizations.value = organizations.value.slice(0, 20);
    } else {
        filteredOrganizations.value = [];
    }

    if (isEdit.value) {
        await loadDriver();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations(0, 100); // Загружаем первые 100 организаций для начального списка
        // Проверяем, что data существует и является массивом
        if (data && Array.isArray(data)) {
            organizations.value = data;
        } else {
            console.warn('API вернул неожиданную структуру данных для организаций:', data);
            organizations.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const searchOrganizations = async (event) => {
    const query = event.query;
    if (!query || query.length < 2) {
        // Проверяем, что organizations.value существует и является массивом
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
        return;
    }

    try {
        organizationSearchLoading.value = true;
        const data = await OrganizationService.searchOrganizationsByName(query, 0, 20);
        filteredOrganizations.value = data || [];
    } catch (error) {
        console.error('Ошибка поиска организаций:', error);
        filteredOrganizations.value = [];
    } finally {
        organizationSearchLoading.value = false;
    }
};

const onOrganizationSelect = (event) => {
    if (event.value && event.value.id) {
        form.value.organizationId = event.value.id;
    }
};

const onOrganizationInput = (event) => {
    // Если пользователь изменил текст и он не соответствует выбранной организации, очищаем ID
    const inputValue = event.target.value;
    if (inputValue !== selectedOrganizationName.value) {
        form.value.organizationId = null;
    }
};

const loadDriver = async () => {
    try {
        loading.value = true;
        const driver = await DriverService.getDriverById(driverId);
        if (driver) {
            console.log('Загруженные данные сотрудника:', driver);
            
            // Обрабатываем ФИО - API может возвращать как fullName, так и отдельные поля
            let name = '';
            let surname = '';
            let middleName = '';
            
            if (driver.fullName) {
                // Если есть fullName, разбиваем его на части
                const nameParts = driver.fullName.split(' ');
                surname = nameParts[0] || '';
                name = nameParts[1] || '';
                middleName = nameParts[2] || '';
            } else {
                // Если нет fullName, используем отдельные поля
                name = driver.name || '';
                surname = driver.surname || '';
                middleName = driver.middleName || '';
            }
            
            form.value = {
                name: name,
                surname: surname,
                middleName: middleName,
                role: driver.role || 'driver',
                personalNumber: driver.personnelNumber || driver.personalNumber || '',
                pinCode: '', // Не загружаем PIN-код из соображений безопасности
                organizationId: driver.organizationId || null,
                login: driver.login || '',
                email: driver.email || '',
                phone: driver.phone || ''
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки сотрудника:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.name.trim()) {
        errors.value.name = 'Имя обязательно';
    }

    if (!form.value.surname.trim()) {
        errors.value.surname = 'Фамилия обязательна';
    }

    if (!form.value.role) {
        errors.value.role = 'Роль обязательна';
    }

    if (!form.value.pinCode.trim()) {
        errors.value.pinCode = 'PIN-код обязателен';
    } else if (form.value.pinCode.length < 4) {
        errors.value.pinCode = 'PIN-код должен содержать минимум 4 символа';
    }

    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна';
    }

    if (form.value.email && !isValidEmail(form.value.email)) {
        errors.value.email = 'Неверный формат email';
    }

    return Object.keys(errors.value).length === 0;
};

const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

const saveDriver = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        const driverData = {
            name: form.value.name,
            surname: form.value.surname,
            middleName: form.value.middleName,
            role: form.value.role,
            personalNumber: form.value.personalNumber,
            pinCode: form.value.pinCode,
            organizationId: form.value.organizationId,
            login: form.value.login,
            email: form.value.email,
            phone: form.value.phone
        };

        if (isEdit.value) {
            await DriverService.updateDriver(driverId, driverData);
            console.log('Сотрудник обновлен:', driverData);
        } else {
            await DriverService.createDriver(projectId, driverData);
            console.log('Сотрудник создан:', driverData);
        }

        router.push(`/pro/${projectId}/nsi/driver`);

    } catch (error) {
        console.error('Ошибка сохранения сотрудника:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectId}/nsi/driver`);
};

// Генерация логина по умолчанию
const generateLogin = () => {
    if (form.value.surname && form.value.name) {
        form.value.login = `${form.value.surname.toLowerCase()}.${form.value.name.toLowerCase()}`;
    }
};
</script>

<template>
    <div class="driver-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование сотрудника' : 'Создание сотрудника' }}
            </h2>
        </div>

        <div class="card">
            <form @submit.prevent="saveDriver" class="p-fluid">
                <div class="form-layout">
                    <!-- Левая часть - Поля формы -->
                    <div class="form-fields">
                        <h3 class="form-section-title">Основная информация</h3>

                        <!-- ФИО -->
                        <div class="form-row">
                            <label for="surname" class="form-label">Фамилия *</label>
                            <div class="form-input">
                                <InputText
                                    id="surname"
                                    v-model="form.surname"
                                    :class="{ 'p-invalid': errors.surname }"
                                    placeholder="Введите фамилию"
                                    @blur="generateLogin"
                                />
                                <small v-if="errors.surname" class="p-error">{{ errors.surname }}</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="name" class="form-label">Имя *</label>
                            <div class="form-input">
                                <InputText
                                    id="name"
                                    v-model="form.name"
                                    :class="{ 'p-invalid': errors.name }"
                                    placeholder="Введите имя"
                                    @blur="generateLogin"
                                />
                                <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="middleName" class="form-label">Отчество</label>
                            <div class="form-input">
                                <InputText
                                    id="middleName"
                                    v-model="form.middleName"
                                    placeholder="Введите отчество"
                                />
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="role" class="form-label">Роль *</label>
                            <div class="form-input">
                                <Dropdown
                                    id="role"
                                    v-model="form.role"
                                    :options="roles"
                                    optionLabel="label"
                                    optionValue="value"
                                    :class="{ 'p-invalid': errors.role }"
                                    placeholder="Выберите роль"
                                />
                                <small v-if="errors.role" class="p-error">{{ errors.role }}</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="personalNumber" class="form-label">Табельный номер</label>
                            <div class="form-input">
                                <InputText
                                    id="personalNumber"
                                    v-model="form.personalNumber"
                                    placeholder="Введите табельный номер"
                                />
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="pinCode" class="form-label">PIN-код *</label>
                            <div class="form-input">
                                <Password
                                    id="pinCode"
                                    v-model="form.pinCode"
                                    :class="{ 'p-invalid': errors.pinCode }"
                                    placeholder="Введите PIN-код"
                                    :feedback="false"
                                    toggleMask
                                />
                                <small v-if="errors.pinCode" class="p-error">{{ errors.pinCode }}</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="organizationId" class="form-label">Организация *</label>
                            <div class="form-input">
                                <AutoComplete
                                    id="organizationId"
                                    v-model="selectedOrganizationName"
                                    :suggestions="filteredOrganizations"
                                    @complete="searchOrganizations"
                                    @item-select="onOrganizationSelect"
                                    @input="onOrganizationInput"
                                    optionLabel="name"
                                    placeholder="Начните вводить название организации..."
                                    :class="{ 'p-invalid': errors.organizationId }"
                                    class="w-full"
                                    :loading="organizationSearchLoading"
                                    :delay="300"
                                    :minLength="2"
                                    :dropdown="true"
                                    :forceSelection="true"
                                />
                                <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                                <small v-else class="text-color-secondary">Введите минимум 2 символа для поиска</small>
                            </div>
                        </div>

                        <h3 class="form-section-title">Контактная информация</h3>

                        <div class="form-row">
                            <label for="login" class="form-label">Логин</label>
                            <div class="form-input">
                                <InputText
                                    id="login"
                                    v-model="form.login"
                                    placeholder="Введите логин"
                                />
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="email" class="form-label">Email</label>
                            <div class="form-input">
                                <InputText
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    :class="{ 'p-invalid': errors.email }"
                                    placeholder="Введите email"
                                />
                                <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="phone" class="form-label">Телефон</label>
                            <div class="form-input">
                                <InputText
                                    id="phone"
                                    v-model="form.phone"
                                    placeholder="Введите телефон"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Правая часть - Информационная панель -->
                    <div class="form-controls">
                        <div class="info-panel">
                            <h3 class="info-panel-title">
                                <i class="pi pi-info-circle mr-2"></i>
                                Информация
                            </h3>
                            <div class="info-list">
                                <div class="info-item">
                                    <i class="pi pi-lock text-blue-600"></i>
                                    <span>PIN-код будет зашифрован</span>
                                </div>
                                <div class="info-item">
                                    <i class="pi pi-user text-green-600"></i>
                                    <span>Пользователь будет создан в Keycloak</span>
                                </div>
                                <div class="info-item">
                                    <i class="pi pi-database text-purple-600"></i>
                                    <span>Запись будет создана в TMS</span>
                                </div>
                                <div class="info-item">
                                    <i class="pi pi-shield text-orange-600"></i>
                                    <span>Будет назначена соответствующая роль</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Кнопки действий внизу -->
                <div class="form-actions">
                    <Button
                        type="button"
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Обновить' : 'Создать'"
                        :icon="isEdit ? 'pi pi-check' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
    </div>
</template>

<style scoped>
.driver-form {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.card {
    flex: 1;
    margin: 0 !important;
    padding: 1rem !important;
}

/* Основная структура формы */
.form-layout {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Левая часть - поля формы */
.form-fields {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Заголовки секций */
.form-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.form-section-title:first-child {
    margin-top: 0;
}

/* Строки полей */
.form-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

/* Лейблы полей */
.form-label {
    min-width: 150px;
    font-weight: 600;
    color: var(--text-color);
    text-align: right;
}

/* Поля ввода */
.form-input {
    flex: 1;
    min-width: 250px;
}

/* Правая часть - информационная панель */
.form-controls {
    width: 320px;
    flex-shrink: 0;
}

/* Информационная панель */
.info-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-panel-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color-secondary);
}

.info-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

/* Кнопки действий внизу */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
}

/* Адаптивность */
@media (max-width: 1024px) {
    .form-layout {
        flex-direction: column;
        gap: 1.5rem;
    }

    .form-controls {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .form-label {
        min-width: auto;
        text-align: left;
    }

    .form-input {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .driver-form {
        padding: 0.5rem;
    }

    .card {
        padding: 0.75rem !important;
    }

    .form-section-title {
        font-size: 1rem;
    }

    .info-panel {
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }
}
</style>
