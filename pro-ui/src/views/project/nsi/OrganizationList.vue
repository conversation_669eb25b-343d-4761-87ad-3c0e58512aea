<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {FilterMatchMode} from '@primevue/core/api';
import {ProjectService} from '@/service/ProjectService';
import {useAuthStore} from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

const organizations = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    shortName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
    kpp: { value: null, matchMode: FilterMatchMode.CONTAINS },
    ogrn: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const roleLabels = {
    operator: 'Оператор',
    carrier: 'Перевозчик',
    processing_center: 'Процессинговый центр',
    bank: 'Банк',
    payment_system: 'Платежная система',
    contractor: 'Подрядчик',
    supplier: 'Поставщик'
};

const roleColors = {
    operator: 'primary',
    carrier: 'success',
    processing_center: 'info',
    bank: 'warning',
    payment_system: 'secondary',
    contractor: 'help',
    supplier: 'contrast'
};

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        shortName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        inn: { value: null, matchMode: FilterMatchMode.CONTAINS },
        kpp: { value: null, matchMode: FilterMatchMode.CONTAINS },
        ogrn: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};

onMounted(() => {
    loadOrganizations();
});

const loadOrganizations = async () => {
    try {
        loading.value = true;
        const data = await ProjectService.getProjectOrganizations(projectId);
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций проекта:', error);
        organizations.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const refreshOrganizations = () => {
    loadOrganizations();
};

const viewOrganization = (organization) => {
    router.push(`/organizations/${organization.id}`);
};

const getRoleLabel = (role) => {
    return roleLabels[role] || role;
};

const getRoleColor = (role) => {
    return roleColors[role] || 'secondary';
};

const exportData = () => {
    console.log('Экспорт организаций проекта:', projectId);
    // Здесь будет логика экспорта данных
};

const getSyncIcon = (syncStatus) => {
    const icons = {
        'synced': 'pi pi-check-circle',
        'pending': 'pi pi-clock',
        'error': 'pi pi-times-circle',
        'never': 'pi pi-minus-circle'
    };
    return icons[syncStatus] || 'pi pi-question-circle';
};

const getSyncColor = (syncStatus) => {
    const colors = {
        'synced': '#22c55e',
        'pending': '#f59e0b',
        'error': '#ef4444',
        'never': '#6b7280'
    };
    return colors[syncStatus] || '#6b7280';
};

const getSyncLabel = (syncStatus) => {
    const labels = {
        'synced': 'Синхронизировано',
        'pending': 'Ожидает',
        'error': 'Ошибка',
        'never': 'Не синхронизировано'
    };
    return labels[syncStatus] || 'Неизвестно';
};
</script>

<template>
    <div class="organization-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-xl font-semibold m-0">Организации проекта</h2>
                <p class="text-sm text-color-secondary mt-1">
                    Список организаций участвующих в проекте согласно договору из 1С.
                    Состав и роли организаций не подлежат редактированию.
                </p>
            </div>
            <div class="flex gap-2">
                <Button
                    v-if="showAllFeatures"
                    label="Экспорт"
                    icon="pi pi-download"
                    outlined
                    @click="exportData"
                />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-refresh"
                        label="Обновить"
                        outlined
                        @click="refreshOrganizations"
                    />
                </div>
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="organizations"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['name', 'shortName', 'inn', 'kpp', 'ogrn']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Организации не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="name" header="Наименование" :sortable="false" style="min-width: 300px">
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.name }}</div>
                            <div class="text-sm text-color-secondary mt-1">{{ data.shortName }}</div>
                        </div>
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по наименованию"
                        />
                    </template>
                </Column>

                <Column field="inn" header="ИНН" :sortable="false" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ИНН"
                        />
                    </template>
                </Column>

                <Column field="kpp" header="КПП" :sortable="false" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по КПП"
                        />
                    </template>
                </Column>

                <Column field="ogrn" header="ОГРН" :sortable="false" style="min-width: 140px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по ОГРН"
                        />
                    </template>
                </Column>

                <Column field="fioDirector" header="Руководитель" style="min-width: 200px">
                    <template #body="{ data }">
                        <div v-if="data.fioDirector">
                            <div class="font-medium">{{ data.fioDirector }}</div>
                            <div class="text-xs text-color-secondary">Генеральный директор</div>
                        </div>
                        <span v-else class="text-color-secondary">—</span>
                    </template>
                </Column>

                <Column field="isDeleted" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <Tag
                            :value="data.isDeleted ? 'Удалена' : 'Активна'"
                            :severity="data.isDeleted ? 'danger' : 'success'"
                        />
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 120px" v-if="false">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewOrganization(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.organization-list {
    padding: 0;
}

.table-card {
    background: var(--surface-card);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
}
</style>
