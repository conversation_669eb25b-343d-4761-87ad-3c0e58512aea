<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {VehicleService} from '@/service/VehicleService';
import {OrganizationService} from '@/service/OrganizationService';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import AutoComplete from 'primevue/autocomplete';

const route = useRoute();
const router = useRouter();

const projectId = route.params.projectId;
const vehicleId = route.params.vehicleId;
const isEdit = computed(() => !!vehicleId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);
const filteredOrganizations = ref([]);
const organizationSearchLoading = ref(false);

// Форма данных - только необходимые поля
const form = ref({
    number: '', // Государственный номер
    organizationId: null, // Организация
    type: null, // Тип Т/С
    status: 'ACTIVE' // Статус по умолчанию
});

// Валидация
const errors = ref({});

// Переменная для отображения названия выбранной организации
const selectedOrganizationName = ref('');

// Функция для обновления названия организации по ID
const updateOrganizationName = () => {
    if (!form.value.organizationId) {
        selectedOrganizationName.value = '';
        return;
    }

    // Ищем организацию в загруженном списке
    const organization = organizations.value.find(org => org.id === form.value.organizationId);
    if (organization) {
        selectedOrganizationName.value = organization.name;
        return;
    }

    // Ищем в отфильтрованном списке
    const filteredOrg = filteredOrganizations.value.find(org => org.id === form.value.organizationId);
    if (filteredOrg) {
        selectedOrganizationName.value = filteredOrg.name;
        return;
    }

    selectedOrganizationName.value = '';
};

// Опции для типа Т/С
const vehicleTypeOptions = ref([
    { label: 'Автобус', value: 'BUS' },
    { label: 'Трамвай', value: 'TRAM' },
    { label: 'Троллейбус', value: 'TROLLEYBUS' },
    { label: 'Метро', value: 'METRO' }
]);

onMounted(async () => {
    await loadOrganizations();

    // Инициализируем отфильтрованный список организаций
    if (organizations.value && Array.isArray(organizations.value)) {
        filteredOrganizations.value = organizations.value.slice(0, 20);
    } else {
        filteredOrganizations.value = [];
    }

    if (isEdit.value) {
        await loadVehicle();
        // Обновляем название организации после загрузки данных транспортного средства
        updateOrganizationName();
    } else {
        // Для нового транспортного средства обновляем название организации
        updateOrganizationName();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations(0, 100); // Загружаем первые 100 организаций для начального списка
        
        // Проверяем, что data существует и является массивом
        if (data && Array.isArray(data)) {
            organizations.value = data;
        } else {
            console.warn('API вернул неожиданную структуру данных для организаций:', data);
            organizations.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const searchOrganizations = async (event) => {
    const query = event.query;
    if (!query || query.length < 2) {
        // Проверяем, что organizations.value существует и является массивом
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
        return;
    }

    try {
        organizationSearchLoading.value = true;
        const data = await OrganizationService.searchOrganizationsByName(query, 0, 20);
        filteredOrganizations.value = data || [];
    } catch (error) {
        console.error('Ошибка поиска организаций:', error);
        filteredOrganizations.value = [];
    } finally {
        organizationSearchLoading.value = false;
    }
};

const onOrganizationSelect = (event) => {
    if (event.value && event.value.id) {
        form.value.organizationId = event.value.id;
    }
};

const onOrganizationInput = (event) => {
    // Если пользователь изменил текст и он не соответствует выбранной организации, очищаем ID
    const inputValue = event.target.value;
    if (inputValue !== selectedOrganizationName.value) {
        form.value.organizationId = null;
    }
};

const loadVehicle = async () => {
    try {
        loading.value = true;
        const vehicle = await VehicleService.getVehicleById(vehicleId);
        if (vehicle) {
            form.value = {
                number: vehicle.number || '',
                organizationId: vehicle.organizationId || null,
                type: vehicle.type || null,
                status: vehicle.status || 'ACTIVE'
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки транспортного средства:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.number.trim()) {
        errors.value.number = 'Государственный номер обязателен для заполнения';
    }

    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна для выбора';
    }

    if (!form.value.type) {
        errors.value.type = 'Тип транспортного средства обязателен для выбора';
    }

    return Object.keys(errors.value).length === 0;
};

const saveVehicle = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        // Подготавливаем данные для сохранения
        const vehicleData = {
            number: form.value.number,
            organizationId: form.value.organizationId,
            type: form.value.type,
            status: form.value.status,
            projectId: projectId // Добавляем ID проекта
        };

        if (isEdit.value) {
            await VehicleService.updateVehicle(vehicleId, vehicleData);
            console.log('Транспортное средство обновлено:', vehicleData);
        } else {
            await VehicleService.createVehicle(vehicleData);
            console.log('Транспортное средство создано:', vehicleData);
        }

        router.push(`/pro/${projectId}/nsi/vehicle`);

    } catch (error) {
        console.error('Ошибка сохранения транспортного средства:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectId}/nsi/vehicle`);
};

const onOrganizationChange = () => {
    delete errors.value.organizationId;
};

const onTypeChange = () => {
    delete errors.value.type;
};

// Форматирование государственного номера
const formatRegistrationNumber = (value) => {
    return value.toUpperCase().replace(/[^А-Я0-9]/g, '');
};

const onRegistrationNumberInput = (event) => {
    const formatted = formatRegistrationNumber(event.target.value);
    form.value.number = formatted;
    event.target.value = formatted;
};
</script>

<template>
    <div class="vehicle-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование транспортного средства' : 'Создание транспортного средства' }}
            </h2>
            <div class="flex gap-2 ml-auto">
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
            </div>
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveVehicle">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="number" class="font-medium">Государственный номер *</label>
                            <InputText
                                id="number"
                                v-model="form.number"
                                :class="{ 'p-invalid': errors.number }"
                                placeholder="А123БВ777"
                                class="w-full font-mono"
                            />
                            <small v-if="errors.number" class="p-error">{{ errors.number }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="type" class="font-medium">Тип Т/С *</label>
                            <Dropdown
                                id="type"
                                v-model="form.type"
                                :options="vehicleTypeOptions"
                                optionLabel="label"
                                optionValue="value"
                                :class="{ 'p-invalid': errors.type }"
                                placeholder="Выберите тип Т/С"
                                class="w-full"
                                @change="onTypeChange"
                            />
                            <small v-if="errors.type" class="p-error">{{ errors.type }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <AutoComplete
                                id="organizationId"
                                v-model="selectedOrganizationName"
                                :suggestions="filteredOrganizations"
                                @complete="searchOrganizations"
                                @item-select="onOrganizationSelect"
                                @input="onOrganizationInput"
                                optionLabel="name"
                                placeholder="Начните вводить название организации..."
                                :class="{ 'p-invalid': errors.organizationId }"
                                class="w-full"
                                :loading="organizationSearchLoading"
                                :delay="300"
                                :minLength="2"
                                :dropdown="true"
                                :forceSelection="true"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                            <small v-else class="text-color-secondary">Введите минимум 2 символа для поиска</small>
                        </div>
                    </div>
                </div>

                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'"
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>

        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных транспортного средства...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.vehicle-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
