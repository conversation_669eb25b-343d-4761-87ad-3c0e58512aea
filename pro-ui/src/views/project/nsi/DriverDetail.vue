<script setup>
import {useRoute, useRouter} from 'vue-router';
import {onMounted, ref} from 'vue';
import {DriverService} from '@/service/DriverService';
import {OrganizationService} from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;
const driverId = route.params.driverId;

const loading = ref(true);
const driver = ref(null);
const organization = ref(null);

// Роли сотрудников
const roleLabels = {
    driver: 'Водитель',
    admin: 'Администратор',
    cashier: 'Кассир',
    collector: 'Инкассатор',
    conductor: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>тор',
    controller: 'Контролер'
};

onMounted(async () => {
    await loadDriver();
});

const loadDriver = async () => {
    try {
        loading.value = true;
        const data = await DriverService.getDriverById(driverId);
        driver.value = data;

        if (data.organizationId) {
            await loadOrganization(data.organizationId);
        }
    } catch (error) {
        console.error('Ошибка загрузки сотрудника:', error);
    } finally {
        loading.value = false;
    }
};

const loadOrganization = async (organizationId) => {
    try {
        const organizations = await OrganizationService.getOrganizations();
        organization.value = organizations.find(org => org.id === organizationId);
    } catch (error) {
        console.error('Ошибка загрузки организации:', error);
    }
};

const goBack = () => {
    router.push(`/pro/${projectCode}/nsi/driver`);
};

const editDriver = () => {
    router.push(`/pro/${projectCode}/nsi/driver/${driverId}/edit`);
};

const deleteDriver = async () => {
    if (confirm(`Вы уверены, что хотите удалить сотрудника "${driver.value.fullName}"?`)) {
        try {
            await DriverService.deleteDriver(driverId);
            console.log('Сотрудник удален:', driverId);
            router.push(`/pro/${projectCode}/nsi/driver`);
        } catch (error) {
            console.error('Ошибка удаления сотрудника:', error);
        }
    }
};

const maskCardPan = (pan) => {
    if (!pan || pan.length < 4) return pan;
    return '**** **** **** ' + pan.slice(-4);
};

const maskPinCode = (pin) => {
    return pin ? '****' : '';
};

const getRoleLabel = (role) => {
    return roleLabels[role] || role;
};

const getRoleSeverity = (role) => {
    switch (role) {
        case 'admin': return 'danger';
        case 'driver': return 'success';
        case 'cashier': return 'info';
        case 'collector': return 'warning';
        case 'conductor': return 'help';
        case 'controller': return 'contrast';
        default: return 'secondary';
    }
};
</script>

<template>
    <div class="driver-detail">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Информация о сотруднике</h2>
            <div class="flex gap-2">
                <Button
                    label="Назад"
                    icon="pi pi-arrow-left"
                    outlined
                    @click="goBack"
                />
                <Button
                    label="Редактировать"
                    icon="pi pi-pencil"
                    @click="editDriver"
                />
                <Button
                    label="Удалить"
                    icon="pi pi-trash"
                    severity="danger"
                    outlined
                    @click="deleteDriver"
                />
            </div>
        </div>

        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных сотрудника...</p>
            </div>
        </div>

        <div v-else-if="driver" class="grid">
            <!-- Основная информация -->
            <div class="col-12 lg:col-8">
                <div class="card">
                    <h3 class="text-lg font-semibold mb-4">Основная информация</h3>

                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">ФИО</label>
                                <div class="flex align-items-center mt-2">
                                    <Avatar
                                        :label="driver.fullName.split(' ').map(n => n[0]).join('')"
                                        size="large"
                                        shape="circle"
                                        class="mr-3"
                                    />
                                    <span class="text-xl">{{ driver.fullName }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Табельный номер</label>
                                <div class="mt-2">
                                    <span class="text-xl font-mono">{{ driver.personnelNumber || 'Не указан' }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Роль</label>
                                <div class="mt-2">
                                    <Tag
                                        :value="getRoleLabel(driver.role)"
                                        :severity="getRoleSeverity(driver.role)"
                                    />
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Организация</label>
                                <div class="mt-2">
                                    <div class="flex align-items-center">
                                        <i class="pi pi-building mr-2 text-color-secondary"></i>
                                        <span>{{ organization ? organization.name : driver.organizationName || 'Не указана' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Контактная информация -->
                <div class="card mt-4">
                    <h3 class="text-lg font-semibold mb-4">Контактная информация</h3>

                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Логин</label>
                                <div class="mt-2">
                                    <span class="font-mono">{{ driver.login || 'Не указан' }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Email</label>
                                <div class="mt-2">
                                    <span>{{ driver.email || 'Не указан' }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">Телефон</label>
                                <div class="mt-2">
                                    <span>{{ driver.phone || 'Не указан' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Данные доступа -->
                <div class="card mt-4">
                    <h3 class="text-lg font-semibold mb-4">Данные доступа</h3>

                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">PIN-код</label>
                                <div class="mt-2">
                                    <div class="flex align-items-center">
                                        <i class="pi pi-lock mr-2 text-color-secondary"></i>
                                        <span class="font-mono">{{ maskPinCode(driver.pinCode) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 md:col-6">
                            <div class="field">
                                <label class="font-bold text-color-secondary">PAN служебной карты</label>
                                <div class="mt-2">
                                    <div class="flex align-items-center">
                                        <i class="pi pi-credit-card mr-2 text-color-secondary"></i>
                                        <span class="font-mono">{{ maskCardPan(driver.cardPan) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Боковая панель -->
            <div class="col-12 lg:col-4">
                <div class="card bg-blue-50 border-blue-200">
                    <h3 class="text-lg font-semibold mb-3">Системная информация</h3>
                    <div class="space-y-3">
                        <div class="flex align-items-center">
                            <i class="pi pi-user text-blue-600 mr-2"></i>
                            <span class="text-sm">Пользователь создан в Keycloak</span>
                        </div>
                        <div class="flex align-items-center">
                            <i class="pi pi-database text-blue-600 mr-2"></i>
                            <span class="text-sm">Запись создана в TMS</span>
                        </div>
                        <div class="flex align-items-center">
                            <i class="pi pi-shield text-blue-600 mr-2"></i>
                            <span class="text-sm">PIN-код зашифрован</span>
                        </div>
                    </div>
                </div>

                <div class="card mt-4 bg-green-50 border-green-200">
                    <h3 class="text-lg font-semibold mb-3">Быстрые действия</h3>
                    <div class="space-y-2">
                        <Button
                            label="Сменить PIN-код"
                            icon="pi pi-key"
                            size="small"
                            outlined
                            class="w-full"
                        />
                        <Button
                            label="Заблокировать доступ"
                            icon="pi pi-ban"
                            size="small"
                            severity="warning"
                            outlined
                            class="w-full"
                        />
                        <Button
                            label="Сбросить пароль"
                            icon="pi pi-refresh"
                            size="small"
                            severity="info"
                            outlined
                            class="w-full"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-3"></i>
                <p>Сотрудник не найден</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.driver-detail {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.card {
    margin: 0 !important;
    padding: 1rem !important;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
