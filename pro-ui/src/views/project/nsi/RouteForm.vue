<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {RouteService} from '@/service/RouteService';
import {StationService} from '@/service/StationService';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Paginator from 'primevue/paginator';
import InputText from 'primevue/inputtext';
import RadioButton from 'primevue/radiobutton';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.projectId;
const routeId = route.params.routeId;
const isEdit = computed(() => !!routeId);

const loading = ref(false);
const saving = ref(false);
const availableStations = ref([]);

// Форма данных
const form = ref({
    number: '',
    name: '',
    isCircular: false,
    stations: []
});

// Валидация
const errors = ref({});

// Редактирование остановок
const editingRows = ref([]);
const stationOptions = ref([]);

// Модальное окно добавления остановки
const showAddStationModal = ref(false);
const selectedStation = ref(null);
const stationSuggestions = ref([]);
const loadingStations = ref(false);

// Новые переменные для таблицы станций
const stationsTable = ref([]);
const loadingStationsTable = ref(false);
const searchQuery = ref('');
const selectedStationId = ref(null);
const pagination = ref({
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
});

// Состояния загрузки для операций с остановками
const loadingAddStation = ref(false);
const loadingRemoveStation = ref(false);
const loadingMoveStation = ref(false);

onMounted(async () => {
    await loadAvailableStations();
    if (isEdit.value) {
        await loadRoute();
    }
});

const loadAvailableStations = async () => {
    try {
        const response = await StationService.getStationsByProject(projectCode);
        // Получаем массив остановок из ответа
        const stations = response.content || response || [];
        availableStations.value = stations;
        stationOptions.value = stations.map(station => ({
            label: station.name,
            value: station.id
        }));
    } catch (error) {
        console.error('Ошибка загрузки остановок:', error);
        availableStations.value = [];
        stationOptions.value = [];
    }
};

const loadRoute = async () => {
    try {
        loading.value = true;
        const routeData = await RouteService.getRouteWithStations(routeId);
        if (routeData) {
            form.value = { ...routeData };
        }
    } catch (error) {
        console.error('Ошибка загрузки маршрута:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.number.trim()) {
        errors.value.number = 'Номер маршрута обязателен для заполнения';
    }

    if (!form.value.name.trim()) {
        errors.value.name = 'Название маршрута обязательно для заполнения';
    }

    return Object.keys(errors.value).length === 0;
};

const saveRoute = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        if (isEdit.value) {
            await RouteService.updateRoute(routeId, form.value);
            console.log('Маршрут обновлен:', form.value);
        } else {
            // Добавляем projectId в данные маршрута
            const routeData = { ...form.value, projectId: projectCode };
            await RouteService.createRoute(routeData);
            console.log('Маршрут создан:', routeData);
        }

        router.push(`/pro/${projectCode}/nsi/route`);

    } catch (error) {
        console.error('Ошибка сохранения маршрута:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/nsi/route`);
};

// Функции для работы с остановками маршрута
const addStation = async () => {
    showAddStationModal.value = true;
    selectedStation.value = null;
    selectedStationId.value = null;
    searchQuery.value = '';
    pagination.value.page = 0;
    await loadStationsTable();
};

const loadStationsTable = async () => {
    try {
        loadingStationsTable.value = true;

        // Получаем все остановки проекта с фильтрацией и пагинацией
        const response = await StationService.getStationsByProject(
            projectCode,
            pagination.value.page,
            pagination.value.size,
            { name: searchQuery.value || null }
        );

        // Исключаем уже добавленные остановки
        const existingStationIds = form.value.stations.map(s => s.stationId);
        const availableStations = (response.content || []).filter(station =>
            !existingStationIds.includes(station.id)
        );

        stationsTable.value = availableStations;
        pagination.value = {
            page: response.pagination?.page || 0,
            size: response.pagination?.size || 10,
            totalElements: response.pagination?.totalElements || 0,
            totalPages: response.pagination?.totalPages || 0
        };
    } catch (error) {
        console.error('Ошибка загрузки станций для таблицы:', error);
        stationsTable.value = [];
    } finally {
        loadingStationsTable.value = false;
    }
};

const onSearchChange = async () => {
    pagination.value.page = 0;
    await loadStationsTable();
};

const onPageChange = async (event) => {
    // Вычисляем номер страницы из first и rows
    const newPage = Math.floor(event.first / event.rows);
    pagination.value.page = newPage;
    await loadStationsTable();
};

const selectStation = (station) => {
    selectedStationId.value = station.id;
    selectedStation.value = station;
};

const formatLocation = (station) => {
    const parts = [];

    if (station.country && station.country.trim()) {
        parts.push(station.country.trim());
    }

    if (station.region && station.region.trim()) {
        parts.push(station.region.trim());
    }

    if (station.city && station.city.trim()) {
        parts.push(station.city.trim());
    }

    return parts.join(', ');
};

const searchStations = async (event) => {
    try {
        loadingStations.value = true;
        const query = event.query;

        if (query.length < 2) {
            stationSuggestions.value = [];
            return;
        }

        // Получаем все остановки проекта и фильтруем по запросу
        const response = await StationService.getStationsByProject(projectCode);
        const allStations = response.content || response || [];
        const filteredStations = allStations.filter(station =>
            station.name.toLowerCase().includes(query.toLowerCase()) ||
            station.latinName?.toLowerCase().includes(query.toLowerCase()) ||
            station.city?.toLowerCase().includes(query.toLowerCase())
        );

        // Исключаем уже добавленные остановки
        const existingStationIds = form.value.stations.map(s => s.stationId);
        const availableStations = filteredStations.filter(station =>
            !existingStationIds.includes(station.id)
        );

        stationSuggestions.value = availableStations;
    } catch (error) {
        console.error('Ошибка поиска остановок:', error);
        stationSuggestions.value = [];
    } finally {
        loadingStations.value = false;
    }
};

const confirmAddStation = async () => {
    if (!selectedStation.value) {
        return;
    }

    try {
        loadingAddStation.value = true;

        // Если это редактирование существующего маршрута, добавляем остановку через API
        if (isEdit.value && routeId) {
            await RouteService.addStationToRoute(routeId, selectedStation.value.id);
            console.log('Остановка добавлена к маршруту через API');

            // Перезагружаем данные маршрута для получения обновленного списка остановок
            await loadRoute();
        } else {
            // Для нового маршрута добавляем остановку локально
            const newPosition = form.value.stations.length > 0
                ? Math.max(...form.value.stations.map(s => s.position || 0)) + 1
                : 1;

            form.value.stations.push({
                routeStationId: null,
                routeId: null,
                stationId: selectedStation.value.id,
                position: newPosition,
                stationName: selectedStation.value.name,
                stationNumber: selectedStation.value.number || '',
                stationStatus: selectedStation.value.status,
                latitude: selectedStation.value.latitude,
                longitude: selectedStation.value.longitude,
                isDeleted: false,
                version: 1,
                versionCreatedAt: null,
                versionCreatedBy: null,
                tags: null
            });
        }

        // Закрываем модальное окно
        showAddStationModal.value = false;
        selectedStation.value = null;
        selectedStationId.value = null;
        searchQuery.value = '';

    } catch (error) {
        console.error('Ошибка добавления остановки к маршруту:', error);
        // Можно добавить уведомление об ошибке
    } finally {
        loadingAddStation.value = false;
    }
};

const cancelAddStation = () => {
    showAddStationModal.value = false;
    selectedStation.value = null;
    selectedStationId.value = null;
    searchQuery.value = '';
    stationsTable.value = [];
};

const removeStation = async (index) => {
    const stationToRemove = form.value.stations[index];

    try {
        // Если это редактирование существующего маршрута и у остановки есть ID, удаляем через API
        if (isEdit.value && routeId && stationToRemove.routeStationId) {
            await RouteService.removeStationFromRoute(stationToRemove.routeStationId);
            console.log('Остановка удалена из маршрута через API');

            // Перезагружаем данные маршрута
            await loadRoute();
        } else {
            // Для нового маршрута или остановки без ID удаляем локально
            form.value.stations.splice(index, 1);
            // Пересчитываем порядок
            form.value.stations.forEach((station, idx) => {
                station.position = idx + 1;
            });
        }
    } catch (error) {
        console.error('Ошибка удаления остановки из маршрута:', error);
        // Можно добавить уведомление об ошибке
    }
};

const moveStationUp = async (index) => {
    if (index > 0) {
        try {
            // Если это редактирование существующего маршрута, обновляем позиции через API
            if (isEdit.value && routeId) {
                const temp = form.value.stations[index];
                form.value.stations[index] = form.value.stations[index - 1];
                form.value.stations[index - 1] = temp;

                // Обновляем порядок
                form.value.stations[index].position = index + 1;
                form.value.stations[index - 1].position = index;

                // Создаем объект с позициями для отправки на сервер
                const stationPositions = {};
                form.value.stations.forEach(station => {
                    if (station.routeStationId) {
                        stationPositions[station.routeStationId] = station.position;
                    }
                });

                await RouteService.updateStationPositions(routeId, stationPositions);
                console.log('Позиции остановок обновлены через API');
            } else {
                // Для нового маршрута обновляем локально
                const temp = form.value.stations[index];
                form.value.stations[index] = form.value.stations[index - 1];
                form.value.stations[index - 1] = temp;

                // Обновляем порядок
                form.value.stations[index].position = index + 1;
                form.value.stations[index - 1].position = index;
            }
        } catch (error) {
            console.error('Ошибка перемещения остановки вверх:', error);
            // Можно добавить уведомление об ошибке
        }
    }
};

const moveStationDown = async (index) => {
    if (index < form.value.stations.length - 1) {
        try {
            // Если это редактирование существующего маршрута, обновляем позиции через API
            if (isEdit.value && routeId) {
                const temp = form.value.stations[index];
                form.value.stations[index] = form.value.stations[index + 1];
                form.value.stations[index + 1] = temp;

                // Обновляем порядок
                form.value.stations[index].position = index + 1;
                form.value.stations[index + 1].position = index + 2;

                // Создаем объект с позициями для отправки на сервер
                const stationPositions = {};
                form.value.stations.forEach(station => {
                    if (station.routeStationId) {
                        stationPositions[station.routeStationId] = station.position;
                    }
                });

                await RouteService.updateStationPositions(routeId, stationPositions);
                console.log('Позиции остановок обновлены через API');
            } else {
                // Для нового маршрута обновляем локально
                const temp = form.value.stations[index];
                form.value.stations[index] = form.value.stations[index + 1];
                form.value.stations[index + 1] = temp;

                // Обновляем порядок
                form.value.stations[index].position = index + 1;
                form.value.stations[index + 1].position = index + 2;
            }
        } catch (error) {
            console.error('Ошибка перемещения остановки вниз:', error);
            // Можно добавить уведомление об ошибке
        }
    }
};

const onRowEditSave = (event) => {
    const { newData, index } = event;
    const selectedStation = availableStations.value.find(s => s.id === newData.stationId);

    if (selectedStation) {
        form.value.stations[index] = {
            ...form.value.stations[index],
            stationId: selectedStation.id,
            stationName: selectedStation.name,
            stationNumber: selectedStation.number || ''
        };
    }
};

const onRowEditCancel = (event) => {
    // Отменяем изменения
};

const getStationName = (stationId) => {
    const station = availableStations.value.find(s => s.id === stationId);
    return station ? station.name : '';
};
</script>

<template>
    <div class="route-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование маршрута' : 'Создание маршрута' }}
            </h2>
            <div class="flex gap-2 ml-auto">
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
            </div>
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveRoute">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="number" class="font-medium">Номер маршрута *</label>
                            <InputText
                                id="number"
                                v-model="form.number"
                                :class="{ 'p-invalid': errors.number }"
                                placeholder="Например: 101, 102К"
                                class="w-full"
                            />
                            <small v-if="errors.number" class="p-error">{{ errors.number }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-8">
                        <div class="field">
                            <label for="name" class="font-medium">Название маршрута *</label>
                            <InputText
                                id="name"
                                v-model="form.name"
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Введите название маршрута"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="field">
                            <div class="flex align-items-center">
                                <Checkbox
                                    id="isCircular"
                                    v-model="form.isCircular"
                                    :binary="true"
                                />
                                <label for="isCircular" class="ml-2 font-medium">
                                    Кольцевой маршрут
                                </label>
                            </div>
                            <small class="text-color-secondary">
                                Отметьте, если маршрут является кольцевым (возвращается к начальной остановке)
                            </small>
                        </div>
                    </div>

                    <!-- Остановки маршрута -->
                    <div class="col-12" v-if="isEdit">
                        <div class="flex justify-content-between align-items-center mb-3 mt-4">
                            <h2 class="text-lg font-medium m-0">Остановки маршрута</h2>
                            <div class="flex gap-2 ml-auto">
                            <Button
                                label="Добавить остановку"
                                icon="pi pi-plus"
                                size="small"
                                @click="addStation"
                            />
                            </div>
                        </div>

                        <DataTable
                            :value="form.stations"
                            editMode="row"
                            v-model:editingRows="editingRows"
                            @row-edit-save="onRowEditSave"
                            @row-edit-cancel="onRowEditCancel"
                            responsiveLayout="scroll"
                            :pt="{
                                table: { style: 'min-width: 50rem' }
                            }"
                        >
                            <template #empty>
                                <div class="text-center p-4">
                                    <i class="pi pi-info-circle text-2xl text-color-secondary mb-2"></i>
                                    <p class="text-color-secondary">Остановки не добавлены</p>
                                    <small class="text-color-secondary">
                                        Нажмите "Добавить остановку" для добавления остановок в маршрут
                                    </small>
                                </div>
                            </template>

                            <Column field="position" header="Порядок" style="width: 100px">
                                <template #body="{ data }">
                                    <span class="font-semibold">{{ data.position }}</span>
                                </template>
                            </Column>

                            <Column field="stationName" header="Остановка" style="min-width: 300px">
                                <template #body="{ data }">
                                    <div>
                                        <div class="font-semibold">{{ data.stationName }}</div>
                                    </div>
                                </template>
                                <template #editor="{ data, field }">
                                    <Dropdown
                                        v-model="data.stationId"
                                        :options="stationOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Выберите остановку"
                                        class="w-full"
                                        @change="data.stationName = getStationName(data.stationId)"
                                    />
                                </template>
                            </Column>

                            <Column header="Действия" style="width: 200px">
                                <template #body="{ data, index }">
                                    <div class="flex gap-1">
                                        <Button
                                            icon="pi pi-arrow-up"
                                            size="small"
                                            text
                                            :disabled="index === 0"
                                            @click="moveStationUp(index)"
                                            v-tooltip.top="'Переместить вверх'"
                                        />
                                        <Button
                                            icon="pi pi-arrow-down"
                                            size="small"
                                            text
                                            :disabled="index === form.stations.length - 1"
                                            @click="moveStationDown(index)"
                                            v-tooltip.top="'Переместить вниз'"
                                        />
                                        <Button
                                            icon="pi pi-pencil"
                                            size="small"
                                            text
                                            @click="editingRows[index] = true"
                                            v-tooltip.top="'Редактировать'"
                                        />
                                        <Button
                                            icon="pi pi-trash"
                                            size="small"
                                            text
                                            severity="danger"
                                            @click="removeStation(index)"
                                            v-tooltip.top="'Удалить'"
                                        />
                                    </div>
                                </template>
                                <template #editor="{ data, index }">
                                    <div class="flex gap-1">
                                        <Button
                                            icon="pi pi-check"
                                            size="small"
                                            text
                                            severity="success"
                                            @click="editingRows[index] = false"
                                            v-tooltip.top="'Сохранить'"
                                        />
                                        <Button
                                            icon="pi pi-times"
                                            size="small"
                                            text
                                            severity="secondary"
                                            @click="editingRows[index] = false"
                                            v-tooltip.top="'Отмена'"
                                        />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>
                    </div>
                </div>

                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'"
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>

        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных маршрута...</p>
            </div>
        </div>

        <!-- Модальное окно добавления остановки -->
        <Dialog
            v-model:visible="showAddStationModal"
            modal
            header="Добавить остановку в маршрут"
            :style="{ width: '70rem' }"
            :closable="true"
            @hide="cancelAddStation"
        >
            <div class="p-4">
                <!-- Поиск -->
                <div class="field mb-4">
                    <label for="stationSearch" class="font-medium mb-2">Поиск остановки</label>
                    <div class="flex align-items-center gap-2">
                        <InputText
                            id="stationSearch"
                            v-model="searchQuery"
                            placeholder="Введите название остановки для поиска"
                            @keyup.enter="onSearchChange"
                            class="flex-1"
                        />
                        <Button
                            icon="pi pi-search"
                            @click="onSearchChange"
                            :loading="loadingStationsTable"
                        />
                    </div>
                    <small class="text-color-secondary">
                        Введите название остановки для фильтрации списка
                    </small>
                </div>



                <!-- Таблица станций -->
                <div class="field">
                    <DataTable
                        :value="stationsTable"
                        :loading="loadingStationsTable"
                        :paginator="false"
                        :rows="pagination.size"
                        :totalRecords="pagination.totalElements"
                        :lazy="true"
                        class="w-full"
                        stripedRows
                        showGridlines
                        responsiveLayout="scroll"
                        :scrollable="true"
                        scrollHeight="400px"
                    >
                        <Column field="name" header="Название остановки" sortable>
                            <template #body="{ data }">
                                <div class="flex flex-column gap-1">
                                    <div class="font-semibold">
                                        {{ data.name }}
                                        <span v-if="data.latinName" class="text-sm text-color-secondary">
                                            ({{ data.latinName }})
                                        </span>
                                    </div>
                                    <div v-if="formatLocation(data)" class="text-xs text-color-secondary mt-1">
                                        {{ formatLocation(data) }}
                                    </div>
                                </div>
                            </template>
                        </Column>

                        <Column header="Выбор" :style="{ width: '80px' }">
                            <template #body="{ data }">
                                <div class="flex justify-content-center">
                                    <RadioButton
                                        :value="data.id"
                                        v-model="selectedStationId"
                                        @change="selectStation(data)"
                                        :inputId="'station-' + data.id"
                                    />
                                </div>
                            </template>
                        </Column>
                    </DataTable>

                    <!-- Пагинация -->
                    <div class="mt-3">
                        <Paginator
                            :first="pagination.page * pagination.size"
                            :rows="pagination.size"
                            :totalRecords="pagination.totalElements"
                            @page="onPageChange"
                            :rowsPerPageOptions="[5, 10, 20, 50]"
                            template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
                        />
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="flex justify-content-end gap-2">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancelAddStation"
                    />
                    <Button
                        label="Добавить"
                        icon="pi pi-plus"
                        :disabled="!selectedStation || loadingAddStation"
                        :loading="loadingAddStation"
                        @click="confirmAddStation"
                    />
                </div>
            </template>
        </Dialog>
    </div>
</template>

<style scoped>
.route-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
