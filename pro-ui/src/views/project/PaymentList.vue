<script setup>
import {ref} from 'vue';
import {useRoute} from 'vue-router';
import PaymentTable from '@/components/PaymentTable.vue';

const route = useRoute();
const projectId = route.params.projectId;

// Состояние
const reportsDialogVisible = ref(false);
</script>

<template>
    <div class="payment-list-page">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Платежи</h1>
            </div>
            <!-- Кнопки скрыты -->
            <div class="flex gap-3" style="display: none;">
                <Button
                    label="Экспорт"
                    icon="pi pi-download"
                    @click="exportPayments"
                    class="p-button-outlined"
                />
                <Button
                    label="Отчеты"
                    icon="pi pi-chart-bar"
                    @click="openReports"
                    class="p-button-secondary"
                />
            </div>
        </div>



        <!-- Основная таблица -->
        <PaymentTable />

        <!-- Модальное окно отчетов -->
        <Dialog
            v-model:visible="reportsDialogVisible"
            modal
            header="Отчеты по платежам"
            :style="{ width: '600px' }"
        >
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('daily')">
                        <div class="flex items-center">
                            <i class="pi pi-calendar text-2xl text-blue-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">Ежедневный отчет</h3>
                                <p class="text-sm text-gray-600">Статистика по дням</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('organization')">
                        <div class="flex items-center">
                            <i class="pi pi-building text-2xl text-green-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По организациям</h3>
                                <p class="text-sm text-gray-600">Анализ по перевозчикам</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('route')">
                        <div class="flex items-center">
                            <i class="pi pi-route text-2xl text-orange-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По маршрутам</h3>
                                <p class="text-sm text-gray-600">Статистика маршрутов</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('payment_method')">
                        <div class="flex items-center">
                            <i class="pi pi-credit-card text-2xl text-purple-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По способам оплаты</h3>
                                <p class="text-sm text-gray-600">Анализ платежных методов</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<style scoped>
.payment-list-page {
    padding: 1.5rem;
}
</style>
