<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { ContractService } from '@/service/ContractService';

const router = useRouter();

const contracts = ref([]);
const loading = ref(true);
const syncing = ref(false);
const syncingId = ref(null);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    number: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: { value: null, matchMode: FilterMatchMode.CONTAINS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const statuses = ref([
    { label: 'Активный', value: 'active' },
    { label: 'Черновик', value: 'draft' },
    { label: 'Истекает', value: 'expiring' },
    { label: 'Завершен', value: 'completed' },
    { label: 'Расторгнут', value: 'terminated' }
]);

const syncStatuses = ref([
    { label: 'Синхронизирован', value: 'synced' },
    { label: 'Ожидает синхронизации', value: 'pending' },
    { label: 'Ошибка синхронизации', value: 'error' },
    { label: 'Не синхронизировался', value: 'never' }
]);

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        number: { value: null, matchMode: FilterMatchMode.CONTAINS },
        name: { value: null, matchMode: FilterMatchMode.CONTAINS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadContracts();
});

const loadContracts = async () => {
    try {
        loading.value = true;
        const data = await ContractService.getContracts();
        contracts.value = data;
    } catch (error) {
        console.error('Ошибка загрузки договоров:', error);
        contracts.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const createContract = () => {
    router.push('/contracts/create');
};

const editContract = (contract) => {
    router.push(`/contracts/${contract.id}/edit`);
};

const viewContract = (contract) => {
    router.push(`/contracts/${contract.id}`);
};

const deleteContract = async (contract) => {
    if (confirm(`Вы уверены, что хотите удалить договор "${contract.number}"?`)) {
        try {
            await ContractService.deleteContract(contract.id);
            console.log('Договор удален:', contract.id);
            await loadContracts();
        } catch (error) {
            console.error('Ошибка удаления договора:', error);
        }
    }
};

const syncContract = async (contract) => {
    try {
        syncingId.value = contract.id;
        const result = await ContractService.syncContract(contract.id);

        if (result.success) {
            const contractItem = contracts.value.find(c => c.id === contract.id);
            if (contractItem) {
                contractItem.syncStatus = 'synced';
                contractItem.lastSyncDate = result.syncDate;
            }
            console.log('Синхронизация успешна:', result.message);
        } else {
            console.error('Ошибка синхронизации:', result.message);
        }
    } catch (error) {
        console.error('Ошибка синхронизации:', error);
    } finally {
        syncingId.value = null;
    }
};

const syncAllContracts = async () => {
    try {
        syncing.value = true;
        const result = await ContractService.syncAllContracts();

        console.log('Массовая синхронизация завершена:', result.message);
        await loadContracts();
    } catch (error) {
        console.error('Ошибка массовой синхронизации:', error);
    } finally {
        syncing.value = false;
    }
};

const importFromFile = () => {
    console.log('Загрузить договоры из файла');
};

const exportData = () => {
    console.log('Экспорт всех договоров');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'secondary';
        case 'expiring': return 'warning';
        case 'completed': return 'info';
        case 'terminated': return 'danger';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expiring': return 'Истекает';
        case 'completed': return 'Завершен';
        case 'terminated': return 'Расторгнут';
        default: return 'Неизвестно';
    }
};

const getSyncStatusSeverity = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'success';
        case 'pending': return 'warning';
        case 'error': return 'danger';
        case 'never': return 'secondary';
        default: return 'secondary';
    }
};

const getSyncStatusLabel = (syncStatus) => {
    switch (syncStatus) {
        case 'synced': return 'Синхронизирован';
        case 'pending': return 'Ожидает';
        case 'error': return 'Ошибка';
        case 'never': return 'Не синхронизировался';
        default: return 'Неизвестно';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const getContractTypeLabel = (type) => {
    switch (type) {
        case 'system_rules': return 'Правила системы';
        case 'development_agreement': return 'Договор разработки';
        case 'service_agreement': return 'Договор услуг';
        case 'supply_agreement': return 'Договор поставки';
        default: return 'Неизвестно';
    }
};

const getRoleIcon = (role) => {
    switch (role) {
        case 'operator': return 'pi pi-star';
        case 'carrier': return 'pi pi-car';
        case 'processing_center': return 'pi pi-cog';
        case 'customer': return 'pi pi-user';
        case 'contractor': return 'pi pi-briefcase';
        default: return 'pi pi-circle';
    }
};

const getRoleColor = (role) => {
    switch (role) {
        case 'operator': return '#3b82f6';
        case 'carrier': return '#f59e0b';
        case 'processing_center': return '#10b981';
        case 'customer': return '#8b5cf6';
        case 'contractor': return '#ef4444';
        default: return '#6b7280';
    }
};

const getRoleLabel = (role) => {
    switch (role) {
        case 'operator': return 'Оператор';
        case 'carrier': return 'Перевозчик';
        case 'processing_center': return 'Процессинг';
        case 'customer': return 'Заказчик';
        case 'contractor': return 'Подрядчик';
        default: return 'Участник';
    }
};

const getRoleSeverity = (role) => {
    switch (role) {
        case 'operator': return 'info';
        case 'carrier': return 'warning';
        case 'processing_center': return 'success';
        case 'customer': return 'secondary';
        case 'contractor': return 'danger';
        default: return 'secondary';
    }
};
</script>

<template>
    <div class="contract-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Все договоры</h1>
            <div class="flex gap-2">
<!--                <Button-->
<!--                    label="Синхронизация с 1С"-->
<!--                    icon="pi pi-refresh"-->
<!--                    outlined-->
<!--                    :loading="syncing"-->
<!--                    @click="syncAllContracts"-->
<!--                />-->
<!--                <Button-->
<!--                    label="Загрузить из файла"-->
<!--                    icon="pi pi-upload"-->
<!--                    outlined-->
<!--                    @click="importFromFile"-->
<!--                />-->
<!--                <Button-->
<!--                    label="Экспорт"-->
<!--                    icon="pi pi-download"-->
<!--                    outlined-->
<!--                    @click="exportData"-->
<!--                />-->
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
<!--                <Button-->
<!--                    label="Создать"-->
<!--                    icon="pi pi-plus"-->
<!--                    @click="createContract"-->
<!--                />-->
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText
                            v-model="filters.global.value"
                            placeholder="Поиск по всем полям"
                        />
                    </IconField>
                </div>
            </div>

            <DataTable
                :value="contracts"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['number', 'contractName', 'projectName', 'externalId1C']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Договоры не найдены</p>
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="number" header="Номер договора" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold font-mono">{{ data.number }}</span>
                    </template>
                </Column>

                <Column header="Даты договора" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="text-sm">
                            <div class="mb-1">
                                <span class="text-color-secondary">Заключение:</span> {{ formatDate(data.startDate) }}
                            </div>
                            <div>
                                <span class="text-color-secondary">Завершение:</span> {{ formatDate(data.endDate) }}
                            </div>
                        </div>
                    </template>
                </Column>

<!--                <Column header="Синхронизация с 1С" style="min-width: 180px">-->
<!--                    <template #body="{ data }">-->
<!--                        <div>-->
<!--                            <Tag-->
<!--                                :value="getSyncStatusLabel(data.syncStatus)"-->
<!--                                :severity="getSyncStatusSeverity(data.syncStatus)"-->
<!--                                class="mb-1"-->
<!--                            />-->
<!--                            <div class="text-xs">-->
<!--                                <div class="text-color-secondary">ID: {{ data.externalId1C || 'Не синхронизирован' }}</div>-->
<!--                                <div class="text-color-secondary">-->
<!--                                    {{ formatDate(data.lastSyncDate) }}-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </template>-->
<!--                </Column>-->

<!--                <Column header="Действия" style="min-width: 180px">-->
<!--                    <template #body="{ data }">-->
<!--                        <div class="flex gap-1">-->
<!--                            <Button-->
<!--                                icon="pi pi-refresh"-->
<!--                                size="small"-->
<!--                                text-->
<!--                                :loading="syncingId === data.id"-->
<!--                                @click="syncContract(data)"-->
<!--                                v-tooltip.top="'Синхронизировать с 1С'"-->
<!--                            />-->
<!--                            <Button-->
<!--                                icon="pi pi-eye"-->
<!--                                size="small"-->
<!--                                text-->
<!--                                @click="viewContract(data)"-->
<!--                                v-tooltip.top="'Просмотр'"-->
<!--                            />-->
<!--                            <Button-->
<!--                                icon="pi pi-pencil"-->
<!--                                size="small"-->
<!--                                text-->
<!--                                @click="editContract(data)"-->
<!--                                v-tooltip.top="'Редактировать'"-->
<!--                            />-->
<!--                            <Button-->
<!--                                icon="pi pi-trash"-->
<!--                                size="small"-->
<!--                                text-->
<!--                                severity="danger"-->
<!--                                @click="deleteContract(data)"-->
<!--                                v-tooltip.top="'Удалить'"-->
<!--                            />-->
<!--                        </div>-->
<!--                    </template>-->
<!--                </Column>-->
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.contract-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}

.space-y-1 > * + * {
    margin-top: 0.25rem;
}
</style>
