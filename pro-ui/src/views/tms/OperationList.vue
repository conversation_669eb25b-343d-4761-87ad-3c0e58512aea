<template>
    <div class="operation-list-container">
        <!-- Заголовок -->
        <div class="card mb-4">
            <div class="flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-2xl font-bold mb-2">Журнал операций терминалов</h1>
                </div>
            </div>
        </div>

        <!-- Фильтры -->
        <div class="card mb-4">
            <h3 class="text-lg font-semibold mb-3">Фильтры</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 border rounded-lg bg-gray-50">
                <div v-if="showProjectFilter" class="flex flex-col">
                    <label for="project" class="block text-sm font-medium mb-2">Проект</label>
                    <Dropdown
                        id="project"
                        v-model="filters.projectId"
                        :options="projects"
                        option-label="name"
                        option-value="id"
                        placeholder="Выберите проект"
                        class="w-full"
                        :show-clear="true"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="terminalSerial" class="block text-sm font-medium mb-2">Номер терминала</label>
                    <InputText
                        id="terminalSerial"
                        v-model="filters.terminalSerial"
                        placeholder="Введите номер терминала"
                        class="w-full"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="eventTypes" class="block text-sm font-medium mb-2">Тип события</label>
                    <MultiSelect
                        id="eventTypes"
                        v-model="filters.eventTypes"
                        :options="eventTypes"
                        option-label="description"
                        option-value="type"
                        placeholder="Выберите типы событий"
                        class="w-full"
                        :show-clear="true"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="shiftNum" class="block text-sm font-medium mb-2">Номер смены</label>
                    <InputNumber
                        id="shiftNum"
                        v-model="filters.shiftNum"
                        placeholder="Введите номер смены"
                        class="w-full"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="ern" class="block text-sm font-medium mb-2">ЕРН</label>
                    <InputNumber
                        id="ern"
                        v-model="filters.ern"
                        placeholder="Введите ЕРН"
                        class="w-full"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="createdAtFrom" class="block text-sm font-medium mb-2">Начальная дата</label>
                    <Calendar
                        id="createdAtFrom"
                        v-model="filters.createdAtFrom"
                        show-time
                        show-icon
                        placeholder="Выберите дату"
                        class="w-full"
                    />
                </div>
                <div class="flex flex-col">
                    <label for="createdAtTo" class="block text-sm font-medium mb-2">Конечная дата</label>
                    <Calendar
                        id="createdAtTo"
                        v-model="filters.createdAtTo"
                        show-time
                        show-icon
                        placeholder="Выберите дату"
                        class="w-full"
                    />
                </div>
                <div class="flex items-end gap-2">
                    <Button
                        label="Применить"
                        icon="pi pi-check"
                        @click="applyFilters"
                        class="p-button-success"
                    />
                    <Button
                        label="Сбросить"
                        icon="pi pi-refresh"
                        @click="clearFilters"
                        class="p-button-outlined"
                    />
                </div>
            </div>
        </div>

        <!-- Таблица операций -->
        <div class="card">
            <DataTable
                :value="operations"
                :loading="loading"
                :paginator="true"
                :rows="pagination.limit"
                :total-records="pagination.totalCount"
                :lazy="true"
                :rows-per-page-options="[10, 25, 50, 100]"
                @page="onPageChange"
                :sort-field="sortField"
                :sort-order="sortOrder"
                @sort="onSort"
                striped-rows
                show-gridlines
                responsive-layout="scroll"
                class="p-datatable-sm"
            >
                <Column field="eventType" header="Тип события" :sortable="true" style="min-width: 200px">
                    <template #body="{ data }">
                        <Tag :value="getEventTypeDescription(data.eventType)" :severity="getEventTypeSeverity(data.eventType)" />
                    </template>
                </Column>

                <Column field="createdAt" header="Дата/время" :sortable="true" style="min-width: 180px">
                    <template #body="{ data }">
                        <span v-if="data.createdAt">{{ formatDateTime(data.createdAt) }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="terminalSerial" header="Номер терминала" :sortable="true" style="min-width: 150px">
                    <template #body="{ data }">
                        <span v-if="data.terminalSerial">{{ data.terminalSerial }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="ern" header="ЕРН" :sortable="true" style="min-width: 100px">
                    <template #body="{ data }">
                        <span v-if="data.ern">{{ data.ern }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="shiftNum" header="Номер смены" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <span v-if="data.shiftNum">{{ data.shiftNum }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="stopListVersion" header="Версия стоп-листа" style="min-width: 150px">
                    <template #body="{ data }">
                        <span v-if="data.stopListVersion">{{ data.stopListVersion }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="stopListUpdate" header="Дата обновления стоп-листа" style="min-width: 200px">
                    <template #body="{ data }">
                        <span v-if="data.stopListUpdate">{{ formatDateTime(data.stopListUpdate) }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="errorCode" header="Код ошибки" style="min-width: 120px">
                    <template #body="{ data }">
                        <span v-if="data.errorCode" class="text-red-500">{{ data.errorCode }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="errorMessage" header="Сообщение об ошибке" style="min-width: 200px">
                    <template #body="{ data }">
                        <span v-if="data.errorMessage" class="text-red-500">{{ data.errorMessage }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>

                <Column field="value" header="Данные события" style="min-width: 200px">
                    <template #body="{ data }">
                        <span v-if="data.value" class="text-sm">{{ data.value }}</span>
                        <span v-else>-</span>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<script setup>
import {computed, onMounted, reactive, ref} from 'vue';
import {useToast} from 'primevue/usetoast';
import {JournalService} from '@/service/JournalService';
import {ProjectService} from '@/service/ProjectService';
import {useAuthStore} from '@/stores/auth';

const toast = useToast();
const authStore = useAuthStore();

// Состояние
const loading = ref(false);
const operations = ref([]);
const eventTypes = ref([]);
const projects = ref([]);
const pagination = reactive({
    page: 0,
    limit: 25,
    totalCount: 0,
    totalPage: 0
});

const sortField = ref('createdAt');
const sortOrder = ref(-1);

// Проверяем, нужно ли показывать фильтр проекта
const showProjectFilter = computed(() => {
    return !authStore.hasProjectId;
});

// Фильтры
const filters = reactive({
    projectId: authStore.hasProjectId ? authStore.getProjectId : null,
    terminalSerial: '',
    eventTypes: [],
    createdAtFrom: null,
    createdAtTo: null,
    shiftNum: null,
    ern: null
});

// Загрузка данных
const loadOperations = async () => {
    loading.value = true;
    try {
        // Преобразуем числовое значение sortOrder в строковое для API
        const sortOrderString = sortOrder.value === 1 ? 'asc' : 'desc';

        const params = {
            ...filters,
            page: pagination.page,
            size: pagination.limit,
            sortField: sortField.value,
            sortOrder: sortOrderString
        };

        const response = await JournalService.getJournalList(params);
        operations.value = response.events || [];
        pagination.totalCount = response.pagination?.totalCount || 0;
        pagination.totalPage = response.pagination?.totalPage || 0;
    } catch (error) {
        console.error('Ошибка загрузки операций:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить операции',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const loadEventTypes = async () => {
    try {
        eventTypes.value = await JournalService.getEventTypes();
    } catch (error) {
        console.error('Ошибка загрузки типов событий:', error);
    }
};

const loadProjects = async () => {
    // Загружаем проекты только если фильтр проекта видим
    if (!showProjectFilter.value) {
        return;
    }

    try {
        projects.value = await ProjectService.getProjects();
    } catch (error) {
        console.error('Ошибка загрузки проектов:', error);
    }
};

// Обработчики событий
const applyFilters = () => {
    pagination.page = 0;
    loadOperations();
};

const clearFilters = () => {
    Object.assign(filters, {
        projectId: authStore.hasProjectId ? authStore.getProjectId : null,
        terminalSerial: '',
        eventTypes: [],
        createdAtFrom: null,
        createdAtTo: null,
        shiftNum: null,
        ern: null
    });
    pagination.page = 0;
    loadOperations();
};

const onPageChange = (event) => {
    pagination.page = event.page;
    loadOperations();
};

const onSort = (event) => {
    sortField.value = event.sortField;
    sortOrder.value = event.sortOrder;
    loadOperations();
};

// Утилиты
const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';
    // Если dateTime - это число (timestamp в секундах), умножаем на 1000 для получения миллисекунд
    const timestamp = typeof dateTime === 'number' ? dateTime * 1000 : dateTime;
    const date = new Date(timestamp);
    return date.toLocaleString('ru-RU', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

const getEventTypeDescription = (eventType) => {
    const eventTypeObj = eventTypes.value.find(et => et.type === eventType);
    return eventTypeObj?.description || eventType;
};

const getEventTypeSeverity = (eventType) => {
    // Определяем цвет тега в зависимости от типа события
    if (eventType?.includes('SUCCESS')) return 'success';
    if (eventType?.includes('FAIL') || eventType?.includes('ERROR')) return 'danger';
    if (eventType?.includes('PAY')) return 'info';
    return 'warning';
};

// Инициализация
onMounted(async () => {
    await Promise.all([
        loadEventTypes(),
        loadProjects()
    ]);
    await loadOperations();
});
</script>

<style scoped>
.operation-list-container {
    padding: 1rem;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.grid {
    display: grid;
    gap: 1rem;
}

.col-12 { grid-column: span 12; }
.col-6 { grid-column: span 6; }
.col-3 { grid-column: span 3; }

@media (max-width: 768px) {
    .col-md-6 { grid-column: span 6; }
    .col-md-12 { grid-column: span 12; }
}

@media (max-width: 1024px) {
    .col-lg-3 { grid-column: span 3; }
    .col-lg-6 { grid-column: span 6; }
    .col-lg-12 { grid-column: span 12; }
}
</style>
