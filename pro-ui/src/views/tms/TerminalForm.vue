<script setup>
import {computed, onMounted, ref} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {TerminalService} from '@/service/TerminalService';
import {OrganizationService} from '@/service/OrganizationService';
import {ProjectService} from '@/service/ProjectService';
import {TerminalTypeService} from '@/service/TerminalTypeService';
import {TerminalProfileService} from '@/service/TerminalProfileService';

const route = useRoute();
const router = useRouter();

const terminalId = route.params.terminalId;
const isEdit = computed(() => !!terminalId);

const loading = ref(false);
const saving = ref(false);
const organizations = ref([]);
const projects = ref([]);
const terminalTypes = ref([]);
const terminalProfiles = ref([]);
const filteredOrganizations = ref([]);
const organizationSearchLoading = ref(false);

// Статусы терминалов
const terminalStatuses = ref([
    { label: 'Активный', value: 0 },
    { label: 'Отключен', value: 1 },
    { label: 'Заблокирован', value: 2 },
    { label: 'Удален', value: 3 }
]);

const form = ref({
    serialNumber: '',
    title: '',
    status: 0, // По умолчанию активный
    versionPO: '',
    typeId: null,
    profileId: null,
    wifiMac: '',
    bluetoothMac: '',
    ethernetMac: '',
    tid: '',
    index: null,
    projectId: null,
    organizationId: null
});

const errors = ref({});

// Вычисляемое свойство для отображения названия выбранной организации
const selectedOrganizationName = computed(() => {
    if (!form.value.organizationId) return '';
    
    // Ищем организацию в загруженном списке
    const organization = organizations.value.find(org => org.id === form.value.organizationId);
    if (organization) return organization.name;
    
    // Ищем в отфильтрованном списке
    const filteredOrg = filteredOrganizations.value.find(org => org.id === form.value.organizationId);
    if (filteredOrg) return filteredOrg.name;
    
    return '';
});

onMounted(async () => {
    await Promise.all([
        loadOrganizations(),
        loadProjects(),
        loadTerminalTypes(),
        loadTerminalProfiles()
    ]);

    // Инициализируем отфильтрованный список организаций
    if (organizations.value && Array.isArray(organizations.value)) {
        filteredOrganizations.value = organizations.value.slice(0, 20);
    } else {
        filteredOrganizations.value = [];
    }

    if (isEdit.value) {
        await loadTerminal();
    }
});

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations(0, 100); // Загружаем первые 100 организаций для начального списка
        // Проверяем, что data существует и является массивом
        if (data && Array.isArray(data)) {
            organizations.value = data;
        } else {
            console.warn('API вернул неожиданную структуру данных для организаций:', data);
            organizations.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
    }
};

const searchOrganizations = async (event) => {
    const query = event.query;
    if (!query || query.length < 2) {
        // Проверяем, что organizations.value существует и является массивом
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
        return;
    }

    try {
        organizationSearchLoading.value = true;
        const data = await OrganizationService.searchOrganizationsByName(query, 0, 20);
        filteredOrganizations.value = data || [];
    } catch (error) {
        console.error('Ошибка поиска организаций:', error);
        filteredOrganizations.value = [];
    } finally {
        organizationSearchLoading.value = false;
    }
};

const onOrganizationSelect = (event) => {
    if (event.value && event.value.id) {
        form.value.organizationId = event.value.id;
    }
};

const onOrganizationInput = (event) => {
    // Если пользователь изменил текст и он не соответствует выбранной организации, очищаем ID
    const inputValue = event.target.value;
    if (inputValue !== selectedOrganizationName.value) {
        form.value.organizationId = null;
    }
};

const loadProjects = async () => {
    try {
        const data = await ProjectService.getProjects();
        projects.value = data;
    } catch (error) {
        console.error('Ошибка загрузки проектов:', error);
    }
};

const loadTerminalTypes = async () => {
    try {
        const data = await TerminalTypeService.getTerminalTypes();
        terminalTypes.value = data;

        // Устанавливаем тип по умолчанию, если это создание нового терминала
        if (!isEdit.value) {
            const defaultType = data.find(type => type.name === 'aQsi5f');
            if (defaultType) {
                form.value.typeId = defaultType.id;
                console.log('Установлен тип по умолчанию:', defaultType.name, 'ID:', defaultType.id);
            } else {
                console.log('Тип терминала "aQsi5f" не найден в списке');
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки типов терминалов:', error);
    }
};

const loadTerminalProfiles = async () => {
    try {
        const data = await TerminalProfileService.getActiveTerminalProfiles();
        terminalProfiles.value = data;

        // Устанавливаем профиль по умолчанию, если это создание нового терминала
        if (!isEdit.value) {
            const defaultProfile = data.find(profile => profile.name.includes('Базовая'));
            if (defaultProfile) {
                form.value.profileId = defaultProfile.id;
                console.log('Установлен профиль по умолчанию:', defaultProfile.name, 'ID:', defaultProfile.id);
            } else {
                console.log('Профиль с "Базовая" в названии не найден в списке');
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки профилей терминалов:', error);
    }
};

const loadTerminal = async () => {
    try {
        loading.value = true;
        const terminal = await TerminalService.getTerminalById(terminalId);
        if (terminal) {
            // Маппинг полей из API в форму
            form.value = {
                serialNumber: terminal.serialNumber || '',
                title: terminal.title || '',
                status: terminal.status || 0,
                versionPO: terminal.versionPO || '',
                typeId: terminal.typeId || null,
                profileId: terminal.profileId || null,
                wifiMac: terminal.wifiMac || '',
                bluetoothMac: terminal.bluetoothMac || '',
                ethernetMac: terminal.ethernetMac || '',
                tid: terminal.tid || '',
                index: terminal.index || null,
                projectId: terminal.projectId || null,
                organizationId: terminal.organizationId || null
            };
        }
    } catch (error) {
        console.error('Ошибка загрузки терминала:', error);
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.serialNumber.trim()) {
        errors.value.serialNumber = 'Серийный номер обязателен';
    }

    if (!form.value.title.trim()) {
        errors.value.title = 'Название терминала обязательно';
    }

    if (form.value.status === null || form.value.status === undefined) {
        errors.value.status = 'Статус обязателен';
    }

    if (!form.value.projectId) {
        errors.value.projectId = 'Проект обязателен';
    }

    if (!form.value.organizationId) {
        errors.value.organizationId = 'Организация обязательна';
    }

    if (!form.value.typeId) {
        errors.value.typeId = 'Тип терминала обязателен';
    }

    if (!form.value.profileId) {
        errors.value.profileId = 'Профиль терминала обязателен';
    }

    // Валидация MAC адресов
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;

    if (form.value.wifiMac && !macRegex.test(form.value.wifiMac)) {
        errors.value.wifiMac = 'Неверный формат MAC адреса';
    }

    if (form.value.bluetoothMac && !macRegex.test(form.value.bluetoothMac)) {
        errors.value.bluetoothMac = 'Неверный формат MAC адреса';
    }

    if (form.value.ethernetMac && !macRegex.test(form.value.ethernetMac)) {
        errors.value.ethernetMac = 'Неверный формат MAC адреса';
    }

    return Object.keys(errors.value).length === 0;
};

const saveTerminal = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        // Подготовка данных для отправки
        const terminalData = {
            serialNumber: form.value.serialNumber.trim(),
            title: form.value.title.trim(),
            status: form.value.status,
            versionPO: null, // Не отправляем, заполняется системой
            typeId: form.value.typeId,
            profileId: form.value.profileId,
            wifiMac: form.value.wifiMac.trim() || null,
            bluetoothMac: form.value.bluetoothMac.trim() || null,
            ethernetMac: form.value.ethernetMac.trim() || null,
            tid: form.value.tid.trim() || null,
            index: null, // Не отправляем, заполняется системой
            projectId: form.value.projectId,
            organizationId: form.value.organizationId
        };

        if (isEdit.value) {
            await TerminalService.updateTerminal(terminalId, terminalData);
        } else {
            await TerminalService.createTerminal(terminalData);
        }

        router.push('/tms/terminals');

    } catch (error) {
        console.error('Ошибка сохранения терминала:', error);
        // Можно добавить уведомление об ошибке
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/tms/terminals');
};

const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toISOString().slice(0, 16);
};
</script>

<template>
    <div class="terminal-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование терминала' : 'Создание терминала' }}
            </h1>
            <div class="flex gap-2 ml-auto">
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
            </div>
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveTerminal">
                <div class="grid">
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="serialNumber" class="font-medium">Серийный номер *</label>
                            <InputText
                                id="serialNumber"
                                v-model="form.serialNumber"
                                :class="{ 'p-invalid': errors.serialNumber }"
                                placeholder="TRM-MSK-001"
                                class="w-full"
                            />
                            <small v-if="errors.serialNumber" class="p-error">{{ errors.serialNumber }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="title" class="font-medium">Название терминала *</label>
                            <InputText
                                id="title"
                                v-model="form.title"
                                :class="{ 'p-invalid': errors.title }"
                                placeholder="Терминал на автобусе №101"
                                class="w-full"
                            />
                            <small v-if="errors.title" class="p-error">{{ errors.title }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="status" class="font-medium">Статус *</label>
                            <Dropdown
                                id="status"
                                v-model="form.status"
                                :options="terminalStatuses"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Выберите статус"
                                :class="{ 'p-invalid': errors.status }"
                                class="w-full"
                            />
                            <small v-if="errors.status" class="p-error">{{ errors.status }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="tid" class="font-medium">TID</label>
                            <InputText
                                id="tid"
                                v-model="form.tid"
                                placeholder="TID терминала"
                                class="w-full"
                            />
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="projectId" class="font-medium">Проект *</label>
                            <Dropdown
                                id="projectId"
                                v-model="form.projectId"
                                :options="projects"
                                optionLabel="name"
                                optionValue="id"
                                placeholder="Выберите проект"
                                :class="{ 'p-invalid': errors.projectId }"
                                class="w-full"
                            />
                            <small v-if="errors.projectId" class="p-error">{{ errors.projectId }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="organizationId" class="font-medium">Организация *</label>
                            <AutoComplete
                                id="organizationId"
                                v-model="selectedOrganizationName"
                                :suggestions="filteredOrganizations"
                                @complete="searchOrganizations"
                                @item-select="onOrganizationSelect"
                                @input="onOrganizationInput"
                                optionLabel="name"
                                placeholder="Начните вводить название организации..."
                                :class="{ 'p-invalid': errors.organizationId }"
                                class="w-full"
                                :loading="organizationSearchLoading"
                                :delay="300"
                                :minLength="2"
                                :dropdown="true"
                                :forceSelection="true"
                            />
                            <small v-if="errors.organizationId" class="p-error">{{ errors.organizationId }}</small>
                            <small v-else class="text-color-secondary">Введите минимум 2 символа для поиска</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="typeId" class="font-medium">Тип терминала *</label>
                            <Dropdown
                                id="typeId"
                                v-model="form.typeId"
                                :options="terminalTypes"
                                optionLabel="name"
                                optionValue="id"
                                placeholder="Выберите тип терминала"
                                :class="{ 'p-invalid': errors.typeId }"
                                class="w-full"
                            />
                            <small v-if="errors.typeId" class="p-error">{{ errors.typeId }}</small>
                            <small v-else-if="!isEdit && form.typeId" class="text-blue-600">
                                Автоматически выбран тип по умолчанию
                            </small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="profileId" class="font-medium">Профиль терминала *</label>
                            <Dropdown
                                id="profileId"
                                v-model="form.profileId"
                                :options="terminalProfiles"
                                optionLabel="name"
                                optionValue="id"
                                placeholder="Выберите профиль терминала"
                                :class="{ 'p-invalid': errors.profileId }"
                                class="w-full"
                            />
                            <small v-if="errors.profileId" class="p-error">{{ errors.profileId }}</small>
                            <small v-else-if="!isEdit && form.profileId" class="text-blue-600">
                                Автоматически выбран профиль по умолчанию
                            </small>
                        </div>
                    </div>

                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Техническая информация</h3>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="versionPO" class="font-medium">Версия ПО</label>
                            <InputText
                                id="versionPO"
                                v-model="form.versionPO"
                                placeholder="2.1.15"
                                class="w-full"
                                :disabled="true"
                            />
                            <small class="text-color-secondary">Автоматически заполняется системой</small>
                        </div>
                    </div>

<!--                    <div class="col-12 md:col-6" v-if="!isEdit">-->
<!--                        <div class="field">-->
<!--                            <label for="index" class="font-medium">Индекс</label>-->
<!--                            <InputNumber-->
<!--                                id="index"-->
<!--                                v-model="form.index"-->
<!--                                placeholder="Автоматически"-->
<!--                                class="w-full"-->
<!--                                :disabled="true"-->
<!--                            />-->
<!--                            <small class="text-color-secondary">Автоматически присваивается при создании</small>-->
<!--                        </div>-->
<!--                    </div>-->

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="wifiMac" class="font-medium">WiFi MAC адрес</label>
                            <InputText
                                id="wifiMac"
                                v-model="form.wifiMac"
                                placeholder="00:11:22:33:44:55"
                                :class="{ 'p-invalid': errors.wifiMac }"
                                class="w-full"
                            />
                            <small v-if="errors.wifiMac" class="p-error">{{ errors.wifiMac }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="bluetoothMac" class="font-medium">Bluetooth MAC адрес</label>
                            <InputText
                                id="bluetoothMac"
                                v-model="form.bluetoothMac"
                                placeholder="00:11:22:33:44:55"
                                :class="{ 'p-invalid': errors.bluetoothMac }"
                                class="w-full"
                            />
                            <small v-if="errors.bluetoothMac" class="p-error">{{ errors.bluetoothMac }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="ethernetMac" class="font-medium">Ethernet MAC адрес</label>
                            <InputText
                                id="ethernetMac"
                                v-model="form.ethernetMac"
                                placeholder="00:11:22:33:44:55"
                                :class="{ 'p-invalid': errors.ethernetMac }"
                                class="w-full"
                            />
                            <small v-if="errors.ethernetMac" class="p-error">{{ errors.ethernetMac }}</small>
                        </div>
                    </div>
                </div>

                <div class="flex justify-content-end gap-2 mt-4">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'"
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>

        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных терминала...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.terminal-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
