<script setup>
import {onMounted, ref} from 'vue';
import {useRouter} from 'vue-router';
import {FilterMatchMode} from '@primevue/core/api';
import {TerminalService} from '@/service/TerminalService';
import {ProjectService} from '@/service/ProjectService';
import {OrganizationService} from '@/service/OrganizationService';
import AsyncText from '@/components/AsyncText.vue';

const router = useRouter();

const terminals = ref([]);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    serialNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    title: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const statuses = ref([
    { label: 'Активный', value: 0 },
    { label: 'Отключен', value: 1 },
    { label: 'Заблокирован', value: 2 },
    { label: 'Удален', value: 3 }
]);

// Фильтры для проекта и организации
const selectedProject = ref(null);
const selectedOrganization = ref(null);
const searchTerm = ref('');

// Кэш для названий проектов и организаций
const projectNames = ref(new Map());
const organizationNames = ref(new Map());

onMounted(() => {
    loadTerminals();
});

// Функция для получения названия проекта
const getProjectName = async (projectId) => {
    if (!projectId) return 'Не указан';

    if (projectNames.value.has(projectId)) {
        return projectNames.value.get(projectId);
    }

    try {
        // Используем локальные данные из ProjectService
        const projects = await ProjectService.getProjects();
        const project = projects.find(p => p.id === projectId);
        const name = project?.name || 'Неизвестный проект';
        projectNames.value.set(projectId, name);
        return name;
    } catch (error) {
        console.error('Ошибка получения названия проекта:', error);
        return 'Ошибка загрузки';
    }
};

// Функция для получения названия организации
const getOrganizationName = async (organizationId) => {
    if (!organizationId) return 'Не указана';

    if (organizationNames.value.has(organizationId)) {
        return organizationNames.value.get(organizationId);
    }

    try {
        // Загружаем организации из PASIV сервиса
        const organizations = await OrganizationService.getOrganizations();
        const organization = organizations.find(o => o.id === organizationId);
        const name = organization?.name || 'Неизвестная организация';
        organizationNames.value.set(organizationId, name);
        return name;
    } catch (error) {
        console.error('Ошибка получения названия организации:', error);
        return 'Ошибка загрузки';
    }
};

const loadTerminals = async () => {
    try {
        loading.value = true;
        const params = {
            search: searchTerm.value || undefined,
            projectId: selectedProject.value || undefined,
            organizationId: selectedOrganization.value || undefined,
            status: filters.value.status.value || undefined,
            page: 0,
            size: 100
        };
        const data = await TerminalService.getTerminals(params);
        terminals.value = data;
    } catch (error) {
        console.error('Ошибка загрузки терминалов:', error);
        terminals.value = [];
    } finally {
        loading.value = false;
    }
};

const createTerminal = () => {
    router.push('/tms/terminals/create');
};

const viewTerminal = (terminal) => {
    router.push(`/tms/terminals/${terminal.id}`);
};

const editTerminal = (terminal) => {
    router.push(`/tms/terminals/${terminal.id}/edit`);
};

const deleteTerminal = async (terminal) => {
    if (confirm(`Вы уверены, что хотите удалить терминал "${terminal.serialNumber}"?`)) {
        try {
            await TerminalService.deleteTerminal(terminal.id);
            console.log('Терминал удален:', terminal.id);
            await loadTerminals();
        } catch (error) {
            console.error('Ошибка удаления терминала:', error);
        }
    }
};

const restartTerminal = async (terminal) => {
    if (confirm(`Вы уверены, что хотите перезагрузить терминал "${terminal.serialNumber}"?`)) {
        try {
            const result = await TerminalService.restartTerminal(terminal.id);
            console.log('Терминал перезагружается:', result.message);
        } catch (error) {
            console.error('Ошибка перезагрузки терминала:', error);
        }
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 0: return 'success'; // Активный
        case 1: return 'danger';  // Отключен
        case 2: return 'warning'; // Заблокирован
        case 3: return 'secondary'; // Удален
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 0: return 'Активный';
        case 1: return 'Отключен';
        case 2: return 'Заблокирован';
        case 3: return 'Удален';
        default: return 'Неизвестно';
    }
};



const clearFilter = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        serialNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        title: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
    selectedProject.value = null;
    selectedOrganization.value = null;
    searchTerm.value = '';
    loadTerminals();
};
</script>

<template>
    <div class="terminal-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Терминалы</h1>
            <div class="flex gap-2 ml-auto">
            <Button
                label="Добавить терминал"
                icon="pi pi-plus"
                @click="createTerminal"
            />
            </div>
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button
                        type="button"
                        icon="pi pi-filter-slash"
                        label="Очистить"
                        outlined
                        @click="clearFilter"
                    />
                </div>
                <div class="flex gap-2 align-items-center">
                    <InputText
                        v-model="searchTerm"
                        placeholder="Поиск по серийному номеру, названию, TID"
                        @keyup.enter="loadTerminals"
                        style="width: 300px"
                    />
                    <Button
                        icon="pi pi-search"
                        @click="loadTerminals"
                        label="Найти"
                    />
                </div>
            </div>

            <DataTable
                :value="terminals"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['serialNumber', 'title', 'organizationName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-tablet text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Терминалы не найдены</p>
                        <Button
                            label="Добавить первый терминал"
                            icon="pi pi-plus"
                            class="mt-3"
                            @click="createTerminal"
                        />
                    </div>
                </template>

                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>

                <Column field="serialNumber" header="Серийный номер" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по номеру"
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.serialNumber }}</span>
                    </template>
                </Column>

                <Column field="title" header="Название" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по названию"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="font-semibold">{{ data.title }}</div>
                    </template>
                </Column>

                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown
                            v-model="filterModel.value"
                            :options="statuses"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Выберите статус"
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag
                            :value="getStatusLabel(data.status)"
                            :severity="getStatusSeverity(data.status)"
                        />
                    </template>
                </Column>

                <Column field="organizationId" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText
                            v-model="filterModel.value"
                            type="text"
                            placeholder="Поиск по организации"
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span v-if="data.organizationId">
                                <AsyncText :async-fn="() => getOrganizationName(data.organizationId)" />
                            </span>
                            <span v-else>Не указана</span>
                        </div>
                    </template>
                </Column>

                <Column field="tid" header="TID" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.tid || '-' }}</span>
                    </template>
                </Column>

                <Column header="Действия" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button
                                icon="pi pi-eye"
                                size="small"
                                text
                                @click="viewTerminal(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button
                                icon="pi pi-pencil"
                                size="small"
                                text
                                @click="editTerminal(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button
                                icon="pi pi-refresh"
                                size="small"
                                text
                                @click="restartTerminal(data)"
                                v-tooltip.top="'Перезагрузить'"
                            />
                            <Button
                                icon="pi pi-trash"
                                size="small"
                                text
                                severity="danger"
                                @click="deleteTerminal(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.terminal-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
