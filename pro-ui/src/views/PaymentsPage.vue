<template>
    <div class="payments-page">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Платежи</h1>
                <p class="text-gray-600 mt-2">Управление и анализ платежных операций проекта</p>
            </div>
            <div class="flex gap-3">
                <Button 
                    label="Экспорт" 
                    icon="pi pi-download" 
                    @click="exportPayments"
                    class="p-button-outlined"
                />
                <Button 
                    label="Отчеты" 
                    icon="pi pi-chart-bar" 
                    @click="openReports"
                    class="p-button-secondary"
                />
            </div>
        </div>

        <!-- Статистика -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="pi pi-wallet text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Всего платежей</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.totalPayments }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="pi pi-dollar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Общая сумма</p>
                        <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(stats.totalAmount) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="pi pi-building text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Организаций</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.organizationsCount }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow border">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="pi pi-route text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Маршрутов</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.routesCount }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Основная таблица -->
        <PaymentTable />

        <!-- Модальное окно отчетов -->
        <Dialog 
            v-model:visible="reportsDialogVisible" 
            modal 
            header="Отчеты по платежам"
            :style="{ width: '600px' }"
        >
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('daily')">
                        <div class="flex items-center">
                            <i class="pi pi-calendar text-2xl text-blue-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">Ежедневный отчет</h3>
                                <p class="text-sm text-gray-600">Статистика по дням</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('organization')">
                        <div class="flex items-center">
                            <i class="pi pi-building text-2xl text-green-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По организациям</h3>
                                <p class="text-sm text-gray-600">Анализ по перевозчикам</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('route')">
                        <div class="flex items-center">
                            <i class="pi pi-route text-2xl text-orange-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По маршрутам</h3>
                                <p class="text-sm text-gray-600">Статистика маршрутов</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50" @click="generateReport('payment_method')">
                        <div class="flex items-center">
                            <i class="pi pi-credit-card text-2xl text-purple-600 mr-3"></i>
                            <div>
                                <h3 class="font-semibold">По способам оплаты</h3>
                                <p class="text-sm text-gray-600">Анализ платежных методов</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import PaymentTable from '@/components/PaymentTable.vue';

const route = useRoute();
const projectId = route.params.projectId;

// Состояние
const reportsDialogVisible = ref(false);
const stats = ref({
    totalPayments: 0,
    totalAmount: 0,
    organizationsCount: 0,
    routesCount: 0
});

// Методы
const exportPayments = () => {
    console.log('Экспорт платежей...');
    // Здесь будет логика экспорта
};

const openReports = () => {
    reportsDialogVisible.value = true;
};

const generateReport = (type) => {
    console.log(`Генерация отчета: ${type}`);
    reportsDialogVisible.value = false;
    // Здесь будет логика генерации отчетов
};

const formatCurrency = (amount) => {
    if (!amount) return '0 ₽';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0
    }).format(amount);
};

// Загрузка статистики
const loadStats = () => {
    // Мок статистика
    stats.value = {
        totalPayments: 1247,
        totalAmount: 456789,
        organizationsCount: 4,
        routesCount: 8
    };
};

onMounted(() => {
    loadStats();
});
</script>

<style scoped>
.payments-page {
    padding: 1.5rem;
}
</style> 