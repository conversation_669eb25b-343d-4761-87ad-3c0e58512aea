<script setup>
import {useRoute, useRouter} from 'vue-router';
import {computed, onMounted, ref} from 'vue';
import {ProjectService} from '@/service/ProjectService';
import {useAuthStore} from '@/stores/auth';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

const loading = ref(true);
const project = ref(null);
const error = ref(null);
const updatingManifest = ref(false);

// Статистика проекта
const projectStats = ref({
    totalEntities: 0,
    activeEntities: 0
});

const dashboardStats = ref({
    organizations: 8,
    drivers: 12,
    vehicles: 10,
    routes: 8,
    stations: 12,
    manifestVersion: 1
});

const organizationStats = ref([
    { name: 'Городской транспорт', count: 3, color: '#3b82f6' },
    { name: 'Метрополитен', count: 2, color: '#10b981' },
    { name: 'Автобусный парк №1', count: 2, color: '#f59e0b' },
    { name: 'Мосгортранс', count: 2, color: '#8b5cf6' },
    { name: 'Другие', count: 3, color: '#6b7280' }
]);

const recentActivities = ref([
    {
        title: 'Добавлен новый водитель',
        description: 'Семенов Роман Олегович добавлен в систему',
        time: '2 часа назад',
        icon: 'pi pi-user-plus',
        color: '#10b981'
    },
    {
        title: 'Обновлен маршрут 102К',
        description: 'Изменен список остановок кольцевого маршрута',
        time: '4 часа назад',
        icon: 'pi pi-route',
        color: '#3b82f6'
    },
    {
        title: 'Транспорт отправлен на ТО',
        description: 'НефАЗ-5299 (М345НО777) отправлен на техобслуживание',
        time: '6 часов назад',
        icon: 'pi pi-wrench',
        color: '#f59e0b'
    },
    {
        title: 'Добавлена новая остановка',
        description: 'Площадь Победы добавлена в справочник',
        time: '1 день назад',
        icon: 'pi pi-map-marker',
        color: '#8b5cf6'
    }
]);

onMounted(async () => {
    await loadProject();
    if (project.value) {
        calculateStats();
        await loadProjectStats();
    }
});

const loadProject = async () => {
    try {
        loading.value = true;
        error.value = null;
        const data = await ProjectService.getProjectById(projectId);
        project.value = data;
    } catch (err) {
        console.error('Ошибка загрузки проекта:', err);
        error.value = err.message || 'Ошибка загрузки проекта';
    } finally {
        loading.value = false;
    }
};

const calculateStats = () => {
    if (!project.value) return;

    const total = dashboardStats.value.organizations + dashboardStats.value.drivers +
                  dashboardStats.value.vehicles + dashboardStats.value.routes +
                  dashboardStats.value.stations;
    projectStats.value.totalEntities = total;
    projectStats.value.activeEntities = Math.floor(total * 0.85); // 85% активных
};

const loadProjectStats = async () => {
    try {
        const stats = await ProjectService.getProjectStats(projectId);
        dashboardStats.value = {
            organizations: stats.organizations,
            drivers: stats.employees,
            vehicles: stats.vehicles,
            routes: stats.routes,
            stations: stats.stations,
            manifestVersion: stats.manifestVersion
        };
        calculateStats();
    } catch (error) {
        console.error('Ошибка загрузки статистики проекта:', error);
        // Оставляем значения по умолчанию при ошибке
    }
};

const formatDate = (date) => {
    if (!date) return 'Не указано';

    let dateObj;

    // Обрабатываем разные форматы даты
    if (typeof date === 'string') {
        dateObj = new Date(date);
    } else if (Array.isArray(date)) {
        // Если это массив (timestamp с бэкенда), создаем Date из него
        dateObj = new Date(date[0], date[1] - 1, date[2], date[3], date[4], date[5], date[6] || 0);
    } else if (date instanceof Date) {
        dateObj = date;
    } else {
        return 'Неверная дата';
    }

    // Проверяем, что дата валидна
    if (isNaN(dateObj.getTime())) {
        return 'Неверная дата';
    }

    return dateObj.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('ru-RU').format(number);
};

const navigateTo = (path) => {
    router.push(`/pro/${projectId}/nsi/${path}`);
};

const goBack = () => {
    router.push('/pro');
};

const editProject = () => {
    router.push(`/pro/${projectId}/edit`);
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'expiring': return 'danger';
        case 'completed': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активный';
        case 'draft': return 'Черновик';
        case 'expiring': return 'Истекает';
        case 'completed': return 'Завершен';
        default: return status;
    }
};

const openProjectSettings = () => {
    console.log('Открыть настройки проекта');
};

const toggleNavigation = () => {
    console.log('Переключить навигацию');
};

const menuItems = ref([
    {
        label: 'Общая информация',
        icon: 'pi pi-info-circle',
        command: () => router.push(`/pro/${projectId}`)
    },
    {
        label: 'НСИ',
        icon: 'pi pi-fw pi-table',
        items: [
            {
                label: 'Организации',
                icon: 'pi pi-fw pi-building',
                command: () => router.push(`/pro/${projectId}/nsi/organization`)
            },
            {
                label: 'Остановки',
                icon: 'pi pi-fw pi-map-marker',
                command: () => router.push(`/pro/${projectId}/nsi/station`)
            },
            {
                label: 'Маршруты',
                icon: 'pi pi-fw pi-route',
                command: () => router.push(`/pro/${projectId}/nsi/route`)
            },
            {
                label: 'Сотрудники',
                icon: 'pi pi-fw pi-user',
                command: () => router.push(`/pro/${projectId}/nsi/driver`)
            },
            {
                label: 'Транспортные средства',
                icon: 'pi pi-fw pi-car',
                command: () => router.push(`/pro/${projectId}/nsi/vehicle`)
            },
            {
                label: 'Тарифы',
                icon: 'pi pi-fw pi-dollar',
                command: () => router.push(`/pro/${projectId}/nsi/tariff`)
            },
            {
                label: 'Продукты и сервисы',
                icon: 'pi pi-fw pi-shopping-cart',
                command: () => router.push(`/pro/${projectId}/nsi/product`)
            }
        ]
    },
    {
        label: 'Финансы',
        icon: 'pi pi-fw pi-money-bill',
        items: [
            {
                label: 'Договор проекта',
                icon: 'pi pi-fw pi-file-edit',
                command: () => router.push(`/pro/${projectId}/finance/contract`)
            },
            {
                label: 'Способы оплаты',
                icon: 'pi pi-fw pi-credit-card',
                command: () => router.push(`/pro/${projectId}/finance/payment-methods`)
            },
            // Пункт "Движение средств" показывается только при showAllFeatures === true
            ...(showAllFeatures.value ? [{
                label: 'Движение средств',
                icon: 'pi pi-fw pi-chart-line',
                command: () => router.push(`/pro/${projectId}/finance/cash-flow`)
            }] : []),
            {
                label: 'Платежи',
                icon: 'pi pi-fw pi-wallet',
                command: () => router.push(`/pro/${projectId}/payments`)
            },
            {
                label: 'Статистика',
                icon: 'pi pi-fw pi-chart-bar',
                command: () => router.push(`/pro/${projectId}/statistics`)
            }
        ]
    },
    // Пункт "История изменений" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        label: 'История изменений',
        icon: 'pi pi-fw pi-history',
        command: () => router.push(`/pro/${projectId}/history`)
    }] : [])
]);

// Быстрые действия
const quickActions = ref([
    // Пункт "Редактировать проект" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        label: 'Редактировать проект',
        icon: 'pi pi-pencil',
        command: () => editProject()
    }] : []),
    // Пункт "Синхронизировать с 1С" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        label: 'Синхронизировать с 1С',
        icon: 'pi pi-sync',
        command: () => syncProject()
    }] : []),
    {
        label: 'Обновить справочники',
        icon: 'pi pi-refresh',
        command: () => updateManifest()
    },
    // Пункт "Экспорт данных" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        label: 'Экспорт данных',
        icon: 'pi pi-download',
        command: () => exportProject()
    }] : []),
    // Пункт "Настройки проекта" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        label: 'Настройки проекта',
        icon: 'pi pi-cog',
        command: () => openProjectSettings()
    }] : []),
    // Пункт "Архивировать" показывается только при showAllFeatures === true
    ...(showAllFeatures.value ? [{
        separator: true
    }, {
        label: 'Архивировать',
        icon: 'pi pi-archive',
        command: () => archiveProject()
    }] : [])
]);

const syncProject = () => {
    console.log('Синхронизация с 1С...');
};

const exportProject = () => {
    console.log('Экспорт данных проекта...');
};

const archiveProject = () => {
    if (confirm('Архивировать проект?')) {
        console.log('Архивирование проекта...');
    }
};

const updateManifest = async () => {
    try {
        updatingManifest.value = true;
        const result = await ProjectService.updateManifest(projectId);
        console.log('Справочники обновлены:', result);

        // Обновляем статистику из результата
        if (result.stats) {
            dashboardStats.value = {
                organizations: result.stats.organizations,
                drivers: result.stats.employees,
                vehicles: result.stats.vehicles,
                routes: result.stats.routes,
                stations: result.stats.stations,
                manifestVersion: result.stats.manifestVersion
            };
            calculateStats();
        }

        // Показываем уведомление об успехе
        alert(result.message || 'Справочники проекта успешно обновлены');
    } catch (error) {
        console.error('Ошибка обновления справочников:', error);
        alert('Ошибка обновления справочников: ' + error.message);
    } finally {
        updatingManifest.value = false;
    }
};

</script>

<template>
    <div class="project-container">
        <!-- Загрузчик обновления справочников -->
        <div v-if="updatingManifest" class="manifest-update-overlay">
            <div class="manifest-update-loader">
                <i class="pi pi-spin pi-refresh text-4xl text-primary mb-3"></i>
                <h3 class="text-lg font-semibold mb-2">Идет обновление справочников</h3>
                <p class="text-color-secondary">Пожалуйста, подождите...</p>
            </div>
        </div>

        <!-- Состояние загрузки -->
        <div v-if="loading" class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных проекта...</p>
            </div>
        </div>

        <!-- Ошибка загрузки -->
        <div v-else-if="error" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
                <h3 class="text-lg font-semibold mb-3 text-red-600">Ошибка загрузки</h3>
                <p class="text-color-secondary mb-4">{{ error }}</p>
                <div class="flex gap-2 justify-content-center">
                    <Button
                        label="Повторить"
                        icon="pi pi-refresh"
                        @click="loadProject"
                    />
                    <Button
                        label="Вернуться к списку"
                        icon="pi pi-arrow-left"
                        outlined
                        @click="goBack"
                    />
                </div>
            </div>
        </div>

        <!-- Проект не найден -->
        <div v-else-if="!project" class="card">
            <div class="text-center p-6">
                <i class="pi pi-exclamation-triangle text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Проект не найден</h3>
                <Button
                    label="Вернуться к списку"
                    icon="pi pi-arrow-left"
                    @click="goBack"
                />
            </div>
        </div>

        <!-- Содержимое проекта -->
        <div v-else>
            <!-- Заголовок проекта -->
        <div class="project-header mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-round">
            <div class="flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-3xl font-bold text-primary m-0 mb-2">
                        {{ project ? project.name : 'СберТройка ПРО' }}
                    </h1>
                    <p class="text-lg text-color-secondary m-0 font-mono">
                        {{ project ? project.code : `Проект: ${projectId}` }}
                    </p>
                    <div class="flex align-items-center mt-2">
                        <Tag
                            :value="project ? getStatusLabel(project.status) : 'Активный'"
                            :severity="project ? getStatusSeverity(project.status) : 'success'"
                            class="mr-2"
                        />
<!--                        <span class="text-sm text-color-secondary">-->
<!--                            Последнее обновление: {{ project ? formatDate(project.lastSyncDate) : formatDate(new Date()) }}-->
<!--                        </span>-->
                    </div>
                </div>
                <div class="flex align-items-center gap-3 ml-auto">
                    <!-- Статистика НСИ -->
                    <div class="flex align-items-center gap-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">{{ dashboardStats.organizations }}</div>
                            <div class="text-xs text-color-secondary">Организации</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">{{ dashboardStats.drivers }}</div>
                            <div class="text-xs text-color-secondary">Сотрудники</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">{{ dashboardStats.vehicles }}</div>
                            <div class="text-xs text-color-secondary">Транспорт</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-orange-600">{{ dashboardStats.routes }}</div>
                            <div class="text-xs text-color-secondary">Маршруты</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-cyan-600">{{ dashboardStats.stations }}</div>
                            <div class="text-xs text-color-secondary">Станции</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-indigo-600">{{ dashboardStats.manifestVersion }}</div>
                            <div class="text-xs text-color-secondary">Версия<br /> справочников</div>
                        </div>
                    </div>

                    <Divider layout="vertical" />

                    <!-- Быстрые действия -->
                    <div class="flex gap-2">
                        <Button
                            label="Назад к списку"
                            icon="pi pi-arrow-left"
                            outlined
                            size="small"
                            @click="goBack"
                        />
                        <SplitButton
                            label="Действия"
                            icon="pi pi-cog"
                            :model="quickActions"
                            size="small"
                        />
                    </div>
                </div>
            </div>
        </div>

        <Splitter style="height: calc(100vh - 140px)">
            <SplitterPanel :size="20" :minSize="15">
                <div class="navigation-panel h-full">
                    <div class="p-3">
                        <div class="flex align-items-center justify-content-between mb-3">
                            <h3 class="text-lg font-semibold m-0">Навигация</h3>
                            <Button
                                icon="pi pi-bars"
                                text
                                size="small"
                                @click="toggleNavigation"
                                v-tooltip.right="'Свернуть меню'"
                            />
                        </div>
                        <Menu :model="menuItems" class="w-full navigation-menu" />
                    </div>
                </div>
            </SplitterPanel>

            <SplitterPanel :size="80">
                <div class="content-panel h-full overflow-hidden">
                    <router-view v-if="$route.path !== `/pro/${projectId}`" />
                    <div v-else class="project-dashboard p-4">
                        <!-- Дашборд проекта -->
                        <div class="grid">
                            <!-- Информация о проекте -->
                            <div class="col-12 lg:col-8">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Информация о проекте</h3>
                                    <div class="grid" v-if="project">
                                        <div class="col-12 md:col-6">
                                            <div class="field">
                                                <label class="font-semibold text-color-secondary text-sm">Оператор</label>
                                                <p class="m-0">{{ project.operatorOrganization?.name || 'Не указан' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-12 md:col-6">
                                            <div class="field">
                                                <label class="font-semibold text-color-secondary text-sm">Договор</label>
                                                <p class="m-0 font-mono">{{ project.contractNumber }}</p>
                                                <small class="text-color-secondary">{{ project.contractName }}</small>
                                            </div>
                                        </div>
                                        <div class="col-12 md:col-6">
                                            <div class="field">
                                                <label class="font-semibold text-color-secondary text-sm">Период реализации</label>
                                                <p class="m-0">{{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}</p>
                                            </div>
                                        </div>
                                        <div class="col-12 md:col-6">
                                            <div class="field">
                                                <label class="font-semibold text-color-secondary text-sm">Руководитель</label>
                                                <p class="m-0">{{ project.manager || 'Не назначен' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Краткая статистика -->
                            <div class="col-12 lg:col-4" v-if="showAllFeatures">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Краткая статистика</h3>
                                    <div class="space-y-3">
                                        <div class="flex justify-content-between align-items-center">
                                            <span>Терминалы:</span>
                                            <span class="font-semibold">{{ project?.activeTerminals || 0 }}/{{ project?.terminals || 0 }}</span>
                                        </div>
                                        <div class="flex justify-content-between align-items-center">
                                            <span>Транспорт:</span>
                                            <span class="font-semibold">{{ project?.vehicles || 0 }}</span>
                                        </div>
                                        <div class="flex justify-content-between align-items-center">
                                            <span>Маршруты:</span>
                                            <span class="font-semibold">{{ project?.routes?.length || 0 }}</span>
                                        </div>
                                        <div class="flex justify-content-between align-items-center">
                                            <span>Транзакций/мес:</span>
                                            <span class="font-semibold">{{ formatNumber(project?.monthlyTransactions || 0) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Быстрые действия -->
                            <div class="col-12">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Быстрые действия</h3>
                                    <div class="grid">
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить сотрудника"
                                                icon="pi pi-user-plus"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('driver/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить транспорт"
                                                icon="pi pi-plus"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('vehicle/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Создать маршрут"
                                                icon="pi pi-map"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('route/create')"
                                            />
                                        </div>
                                        <div class="col-12 md:col-6 lg:col-3">
                                            <Button
                                                label="Добавить остановку"
                                                icon="pi pi-map-marker"
                                                class="w-full p-3"
                                                outlined
                                                @click="navigateTo('station/create')"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Последние изменения -->
                            <div class="col-12" v-if="showAllFeatures">
                                <div class="card">
                                    <div class="flex justify-content-between align-items-center mb-4">
                                        <h3 class="text-lg font-semibold m-0">Последние изменения</h3>
                                        <Button
                                            label="Вся история"
                                            icon="pi pi-history"
                                            text
                                            size="small"
                                            @click="router.push(`/pro/${projectId}/history`)"
                                        />
                                    </div>
                                    <div class="space-y-3">
                                        <div
                                            v-for="(item, index) in recentActivities.slice(0, 5)"
                                            :key="index"
                                            class="flex align-items-center p-3 border-round border-1 border-200"
                                        >
                                            <i :class="item.icon" :style="{ color: item.color }" class="mr-3 text-xl"></i>
                                            <div class="flex-1">
                                                <div class="font-medium">{{ item.title }}</div>
                                                <div class="text-sm text-color-secondary">{{ item.description }}</div>
                                            </div>
                                            <small class="text-color-secondary">{{ item.time }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Статистика по организациям -->
                            <div class="col-12 lg:col-4" v-if="showAllFeatures">
                                <div class="card">
                                    <h3 class="text-lg font-semibold mb-4">Распределение по организациям</h3>
                                    <div class="space-y-3">
                                        <div v-for="org in organizationStats" :key="org.name" class="flex align-items-center justify-content-between mb-3">
                                            <div class="flex align-items-center">
                                                <div class="w-1rem h-1rem border-round mr-2" :style="{ backgroundColor: org.color }"></div>
                                                <span class="text-sm">{{ org.name }}</span>
                                            </div>
                                            <span class="font-semibold">{{ org.count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </SplitterPanel>
        </Splitter>
        </div> <!-- Закрывающий div для v-else -->
    </div>
</template>

<style scoped>
.project-container {
    height: 100vh;
    margin: 0;
    padding: 1rem;
    background: #f8fafc;
    position: relative;
}

.manifest-update-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.manifest-update-loader {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
}

.project-header {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
    border: 1px solid #bfdbfe;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navigation-panel {
    background: white;
    border-right: 1px solid #e5e7eb;
}

.content-panel {
    background: white;
}

.stats-card {
    border: 1px solid;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-dashboard {
    height: 100%;
    overflow-y: auto;
}

:deep(.p-splitter) {
    height: 100% !important;
    border: none;
}

:deep(.p-splitter-panel) {
    overflow: auto;
}

:deep(.navigation-menu .p-menuitem-link) {
    border-radius: 6px;
    margin-bottom: 2px;
}

:deep(.navigation-menu .p-menuitem-link:hover) {
    background: #f3f4f6;
}

:deep(.p-timeline .p-timeline-event-content) {
    padding: 0.5rem 0;
}

:deep(.p-timeline .p-timeline-event-marker) {
    border: 2px solid #e5e7eb;
    background: white;
}

.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50 {
    --tw-gradient-from: #eff6ff;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0));
}

.to-indigo-50 {
    --tw-gradient-to: #eef2ff;
}

.border-blue-200 {
    border-color: #bfdbfe;
}

.border-green-200 {
    border-color: #bbf7d0;
}

.border-purple-200 {
    border-color: #e9d5ff;
}

.border-orange-200 {
    border-color: #fed7aa;
}

.bg-blue-50 {
    background-color: #eff6ff;
}

.bg-green-50 {
    background-color: #f0fdf4;
}

.bg-purple-50 {
    background-color: #faf5ff;
}

.bg-orange-50 {
    background-color: #fff7ed;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.bg-green-100 {
    background-color: #dcfce7;
}

.bg-purple-100 {
    background-color: #f3e8ff;
}

.bg-orange-100 {
    background-color: #ffedd5;
}

.text-blue-600 {
    color: #2563eb;
}

.text-blue-700 {
    color: #1d4ed8;
}

.text-green-600 {
    color: #16a34a;
}

.text-green-700 {
    color: #15803d;
}

.text-purple-600 {
    color: #9333ea;
}

.text-purple-700 {
    color: #7c3aed;
}

.text-orange-600 {
    color: #ea580c;
}

.text-orange-700 {
    color: #c2410c;
}

.text-green-500 {
    color: #22c55e;
}
</style>
