<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { ContractService } from '@/service/ContractService';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:visible', 'contract-selected']);

// Состояние компонента
const contracts = ref([]);
const selectedContract = ref(null);
const loading = ref(false);
const error = ref(null);
const searchTerm = ref('');

// Вычисляемые свойства
const filteredContracts = computed(() => {
    if (!searchTerm.value) return contracts.value;

    const term = searchTerm.value.toLowerCase();
    return contracts.value.filter(contract =>
        contract.number?.toLowerCase().includes(term) ||
        contract.contractName?.toLowerCase().includes(term) ||
        contract.organizationName?.toLowerCase().includes(term)
    );
});

const isVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// Методы
const loadContracts = async () => {
    try {
        loading.value = true;
        error.value = null;
        const data = await ContractService.getContractForPROProjectCreation();
        contracts.value = data || [];
    } catch (err) {
        console.error('Ошибка загрузки договоров:', err);
        error.value = err.message || 'Ошибка загрузки договоров';
    } finally {
        loading.value = false;
    }
};

const selectContract = () => {
    if (selectedContract.value) {
        emit('contract-selected', selectedContract.value);
        closeModal();
    }
};

const closeModal = () => {
    isVisible.value = false;
    selectedContract.value = null;
    searchTerm.value = '';
    error.value = null;
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const formatAmount = (amount) => {
    if (!amount) return '';
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Следим за изменением видимости
watch(() => props.visible, (visible) => {
    if (visible) {
        loadContracts();
    }
});
</script>

<template>
    <Dialog
        v-model:visible="isVisible"
        modal
        header="Выбор договора для создания проекта"
        :style="{ width: '80vw', maxWidth: '1000px' }"
        :closable="true"
    >
        <div class="contract-selection-content">
            <!-- Поиск -->
            <div class="mb-4">
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText
                        v-model="searchTerm"
                        placeholder="Поиск по номеру договора, названию или организации..."
                        class="w-full"
                    />
                </IconField>
            </div>

            <!-- Ошибка загрузки -->
            <div v-if="error" class="text-center p-4">
                <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
                <h3 class="text-lg font-semibold mb-3 text-red-600">Ошибка загрузки договоров</h3>
                <p class="text-color-secondary mb-4">{{ error }}</p>
                <Button
                    label="Повторить"
                    icon="pi pi-refresh"
                    @click="loadContracts"
                />
            </div>

            <!-- Загрузка -->
            <div v-else-if="loading" class="text-center p-6">
                <ProgressSpinner />
                <p class="mt-3 text-color-secondary">Загрузка договоров...</p>
            </div>

            <!-- Список договоров -->
            <div v-else-if="filteredContracts.length > 0" class="contract-list">
                <DataTable
                    v-model:selection="selectedContract"
                    :value="filteredContracts"
                    selectionMode="single"
                    dataKey="id"
                    :paginator="true"
                    :rows="10"
                    :rowHover="true"
                    showGridlines
                >
                    <Column selectionMode="single" headerStyle="width: 3rem"></Column>

                    <Column field="contractNumber" header="Номер договора" :sortable="true">
                        <template #body="{ data }">
                            <div>
                                <div class="font-mono font-semibold">{{ data.number }}</div>
                            </div>
                        </template>
                    </Column>

                    <Column field="validTo" header="Действует до" :sortable="true">
                        <template #body="{ data }">
                            <span>{{ formatDate(data.endDate) }}</span>
                        </template>
                    </Column>
                </DataTable>
            </div>

            <!-- Пустой список -->
            <div v-else class="text-center p-6">
                <i class="pi pi-inbox text-4xl text-color-secondary mb-3"></i>
                <h3 class="text-lg font-semibold mb-3">Договоры не найдены</h3>
                <p class="text-color-secondary">
                    {{ searchTerm ? 'По вашему запросу договоры не найдены' : 'Нет доступных договоров для создания проекта' }}
                </p>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-content-end gap-2">
                <Button
                    label="Отмена"
                    icon="pi pi-times"
                    text
                    @click="closeModal"
                />
                <Button
                    label="Выбрать"
                    icon="pi pi-check"
                    :disabled="!selectedContract"
                    @click="selectContract"
                />
            </div>
        </template>
    </Dialog>
</template>

<style scoped lang="scss">
.contract-selection-content {
    min-height: 400px;
}

.contract-list {
    max-height: 500px;
    overflow-y: auto;
}

:deep(.p-datatable) {
    .p-datatable-tbody > tr.p-highlight {
        background: var(--primary-50);
        color: var(--primary-900);
    }
}
</style>
