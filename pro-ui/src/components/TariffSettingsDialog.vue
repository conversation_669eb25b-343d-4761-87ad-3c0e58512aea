<template>
    <Dialog
        v-model:visible="visible"
        :header="dialogHeader"
        modal
        class="tariff-settings-dialog"
        :style="{ width: '80rem', maxWidth: '90vw' }"
        :closable="false"
    >
        <div class="tariff-settings-content">
            <!-- Информация о тарифе -->
            <div class="tariff-info-section mb-4">
                <div class="grid">
                    <div class="col-12 md:col-4">
                        <label class="block text-900 font-medium mb-2">Название тарифа</label>
                        <InputText
                            v-model="tariffInfo.name"
                            class="w-full"
                            placeholder="Введите название тарифа"
                        />
                    </div>
                    <div class="col-12 md:col-4">
                        <label class="block text-900 font-medium mb-2">Статус</label>
                        <Dropdown
                            v-model="tariffInfo.status"
                            :options="statusOptions"
                            option-label="label"
                            option-value="value"
                            placeholder="Выберите статус"
                            class="w-full"
                        />
                    </div>
                    <div class="col-12 md:col-4">
                        <label class="block text-900 font-medium mb-2">Тэги</label>
                        <InputText
                            v-model="tariffInfo.tags"
                            class="w-full"
                            placeholder="Введите тэги через запятую"
                        />
                    </div>
                </div>
            </div>

            <!-- Табы для способов оплаты -->
            <div class="payment-methods-section">
                <div class="flex justify-content-between align-items-center mb-3">
                    <h3 class="text-xl font-semibold m-0">Способы оплаты</h3>
                    <div class="flex gap-2 ml-auto">
                    <Button
                        label="Добавить способ оплаты"
                        icon="pi pi-plus"
                        @click="showAddPaymentMethodDialog"
                    />
                    </div>
                </div>

                <TabView v-model:activeIndex="activeTabIndex">
                    <TabPanel
                        v-for="(paymentMethod, index) in paymentMethods"
                        :key="paymentMethod.id"
                        :header="getPaymentMethodLabel(paymentMethod.type)"
                    >
                        <div class="payment-method-content">
                            <div class="flex justify-content-between align-items-center mb-3">
                                <h4 class="text-lg font-medium m-0">
                                    {{ getPaymentMethodLabel(paymentMethod.type) }}
                                </h4>
                                <div class="flex gap-2 ml-auto">
                                <Button
                                    label="Удалить"
                                    icon="pi pi-trash"
                                    severity="danger"
                                    size="small"
                                    @click="removePaymentMethod(index)"
                                />
                                </div>
                            </div>

                            <!-- Продукты для способа оплаты -->
                            <div class="products-section">
                                <div class="flex justify-content-between align-items-center mb-3">
                                    <h5 class="text-md font-medium m-0">Продукты</h5>
                                    <div class="flex gap-2 ml-auto">
                                    <Button
                                        label="Добавить продукт"
                                        icon="pi pi-plus"
                                        size="small"
                                        @click="showAddProductDialog(paymentMethod.id)"
                                    />
                                    </div>
                                </div>

                                <div v-if="paymentMethod.products.length === 0" class="no-products">
                                    <div class="text-center text-color-secondary p-4">
                                        <i class="pi pi-box text-3xl mb-2"></i>
                                        <div>Нет добавленных продуктов</div>
                                        <Button
                                            label="Добавить первый продукт"
                                            icon="pi pi-plus"
                                            size="small"
                                            class="mt-2"
                                            @click="showAddProductDialog(paymentMethod.id)"
                                        />
                                    </div>
                                </div>

                                <div v-else class="products-list">
                                    <div
                                        v-for="product in paymentMethod.products"
                                        :key="product.id"
                                        class="product-item p-3 border-round surface-100 mb-3"
                                    >
                                        <div class="flex flex-col">
                                            <div class="flex-1">
                                                <h6 class="font-medium mb-2">{{ product.name }}</h6>

                                                <!-- Настройки стоимости -->
                                                <div class="cost-settings">
                                                    <div class="flex align-items-center mb-2">
                                                        <Checkbox
                                                            v-model="product.isFixPrice"
                                                            :binary="true"
                                                            input-id="fixed-price"
                                                            class="mr-2"
                                                        />
                                                        <label for="fixed-price" class="text-sm">
                                                            Фиксированный тариф
                                                        </label>
                                                    </div>

                                                    <div v-if="product.isFixPrice" class="fixed-price-input">
                                                        <label class="block text-sm font-medium mb-1">
                                                            Стоимость (копейки)
                                                        </label>
                                                        <InputNumber
                                                            v-model="product.price"
                                                            :min="0"
                                                            :max="100000"
                                                            class="w-full"
                                                            placeholder="Введите стоимость"
                                                        />
                                                    </div>

                                                    <div v-else class="tariff-matrix">
                                                        <label class="block text-sm font-medium mb-2">
                                                            Тарифная матрица
                                                        </label>
                                                        <div class="matrix-container">
                                                            <table class="matrix-table">
                                                                <thead>
                                                                    <tr>
                                                                        <th class="matrix-header">Станции</th>
                                                                        <th
                                                                            v-for="(station, index) in getRouteStations()"
                                                                            :key="index"
                                                                            class="matrix-header"
                                                                        >
                                                                            {{ station }}
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr
                                                                        v-for="(fromStation, fromIndex) in getRouteStations()"
                                                                        :key="fromIndex"
                                                                    >
                                                                        <td class="matrix-station-label">{{ fromStation }}</td>
                                                                        <td
                                                                            v-for="(toStation, toIndex) in getRouteStations()"
                                                                            :key="toIndex"
                                                                            class="matrix-cell"
                                                                        >
                                                                            <InputNumber
                                                                                v-if="fromIndex < toIndex"
                                                                                :model-value="getMatrixValue(product, fromStation, toStation)"
                                                                                @update:model-value="updateMatrixValue(product, fromStation, toStation, $event)"
                                                                                :min="0"
                                                                                :max="100000"
                                                                                class="w-full"
                                                                                placeholder="0"
                                                                                size="small"
                                                                            />
                                                                            <span v-else class="matrix-empty">-</span>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex items-end gap-2" style="margin-top: 4px">
                                                <Button
                                                    label="Сохранить"
                                                    icon="pi pi-save"
                                                    severity="success"
                                                    size="small"
                                                    @click="saveProductChanges(product)"
                                                />
                                                <Button
                                                    label="Удалить"
                                                    icon="pi pi-trash"
                                                    severity="danger"
                                                    size="small"
                                                    @click="removeProduct(paymentMethod.id, product.id)"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>

        <template #footer>
            <Button
                label="Отмена"
                icon="pi pi-times"
                @click="closeDialog"
                class="p-button-text"
            />
            <Button
                label="Сохранить"
                icon="pi pi-check"
                @click="saveSettings"
                :loading="saving"
            />
        </template>

        <!-- Диалог добавления способа оплаты -->
        <Dialog
            v-model:visible="addPaymentMethodDialogVisible"
            header="Добавить способ оплаты"
            modal
            :style="{ width: '30rem' }"
        >
            <div class="p-fluid">
                <label class="block text-900 font-medium mb-2">Выберите способ оплаты</label>
                <Dropdown
                    v-model="selectedPaymentMethod"
                    :options="availablePaymentMethods"
                    option-label="name"
                    option-value="id"
                    placeholder="Выберите способ оплаты"
                    class="w-full"
                />
            </div>

            <template #footer>
                <Button
                    label="Отмена"
                    icon="pi pi-times"
                    @click="addPaymentMethodDialogVisible = false"
                    class="p-button-text"
                />
                <Button
                    label="Добавить"
                    icon="pi pi-check"
                    @click="addPaymentMethod"
                    :disabled="!selectedPaymentMethod"
                />
            </template>
        </Dialog>

        <!-- Диалог добавления продукта -->
        <Dialog
            v-model:visible="addProductDialogVisible"
            header="Добавить продукт"
            modal
            :style="{ width: '30rem' }"
        >
            <div class="p-fluid">
                <label class="block text-900 font-medium mb-2">Выберите продукт</label>
                <div class="mb-2 text-sm text-gray-600">
                    Доступно продуктов: {{ availableProducts.length }}
                </div>
                <Dropdown
                    v-model="selectedProduct"
                    :options="availableProducts"
                    option-label="name"
                    option-value="id"
                    placeholder="Выберите продукт"
                    class="w-full"
                />
                <div v-if="availableProducts.length === 0" class="mt-2 text-sm text-red-600">
                    Нет доступных продуктов. Проверьте консоль для отладочной информации.
                </div>
            </div>

            <template #footer>
                <Button
                    label="Отмена"
                    icon="pi pi-times"
                    @click="addProductDialogVisible = false"
                    class="p-button-text"
                />
                <Button
                    label="Добавить"
                    icon="pi pi-check"
                    @click="addProduct"
                    :disabled="!selectedProduct"
                />
            </template>
        </Dialog>
    </Dialog>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useToast} from 'primevue/usetoast';
import {TariffStorageService} from '@/service/TariffStorageService';
import {TariffService} from '@/service/TariffService';
import {proApiClient} from '@/service/ApiClient';
import {isAuthenticated} from '@/utils/auth-token';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import Dropdown from 'primevue/dropdown';
import Checkbox from 'primevue/checkbox';

const toast = useToast();
const storageService = new TariffStorageService();

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    tariffId: {
        type: String,
        default: null
    },
    routeId: {
        type: String,
        default: null
    },
    projectId: {
        type: String,
        default: null
    }
});

// Emits
const emit = defineEmits(['update:visible', 'saved']);

// Реактивные данные
const saving = ref(false);
const activeTabIndex = ref(0);
const tariffInfo = ref({
    name: '',
    status: 'ACTIVE',
    tags: ''
});
const paymentMethods = ref([]);

// Диалоги
const addPaymentMethodDialogVisible = ref(false);
const addProductDialogVisible = ref(false);
const selectedPaymentMethod = ref(null);
const selectedProduct = ref(null);
const currentPaymentMethodId = ref(null);

// Данные
const availablePaymentMethods = ref([]);
const availableProducts = ref([]);
const routes = ref([]);

// Опции статусов
const statusOptions = [
    { label: 'Активный', value: 'ACTIVE' },
    { label: 'Отключен', value: 'DISABLED' },
    { label: 'Заблокирован', value: 'BLOCKED' },
    { label: 'Удален', value: 'IS_DELETED' }
];

// Computed
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

const dialogHeader = computed(() => {
    const route = routes.value.find(r => r.id === props.routeId);
    const routeName = route ? route.name : 'Неизвестный маршрут';
    return `Настройки тарифа "${tariffInfo.value.name}" для маршрута "${routeName}"`;
});

// Методы
const loadData = async () => {
    try {
        // Проверяем авторизацию
        if (!isAuthenticated()) {
            toast.add({
                severity: 'error',
                summary: 'Ошибка авторизации',
                detail: 'Необходимо авторизоваться для загрузки продуктов',
                life: 5000
            });
            availableProducts.value = [];
            return;
        }

        // Загружаем данные из localStorage для способов оплаты и маршрутов
        const data = await storageService.loadTariffData();
        availablePaymentMethods.value = data.paymentMethods || [];
        routes.value = data.routes || [];

        // Загружаем станции маршрута из API
        if (props.routeId) {
            try {
                const routeStationsResponse = await proApiClient.get(`/api/pro/v1/routes/${props.routeId}/stations`);
                console.log('Route stations response:', routeStationsResponse);
                if (routeStationsResponse.data && Array.isArray(routeStationsResponse.data)) {
                    console.log('Route stations data:', routeStationsResponse.data);
                    console.log('First station structure:', routeStationsResponse.data[0]);

                    // Загружаем информацию о станциях по их ID
                    const stationIds = routeStationsResponse.data.map(routeStation => routeStation.stationId);
                    console.log('Station IDs:', stationIds);

                    const stationsWithNames = [];
                    for (const routeStation of routeStationsResponse.data) {
                        try {
                            const stationResponse = await proApiClient.get(`/api/pro/v1/stations/${routeStation.stationId}`);
                            if (stationResponse.data) {
                                stationsWithNames.push({
                                    ...routeStation,
                                    stationName: stationResponse.data.name || stationResponse.data.st_name || 'Неизвестная станция'
                                });
                            } else {
                                stationsWithNames.push({
                                    ...routeStation,
                                    stationName: 'Неизвестная станция'
                                });
                            }
                        } catch (stationError) {
                            console.error('Ошибка загрузки станции:', routeStation.stationId, stationError);
                            stationsWithNames.push({
                                ...routeStation,
                                stationName: 'Неизвестная станция'
                            });
                        }
                    }

                    console.log('Stations with names:', stationsWithNames);

                    // Обновляем маршрут с информацией о станциях
                    const routeIndex = routes.value.findIndex(r => r.id === props.routeId);
                    if (routeIndex !== -1) {
                        routes.value[routeIndex].stations = stationsWithNames;
                    } else {
                        // Если маршрут не найден в localStorage, создаем его
                        const routeResponse = await proApiClient.get(`/api/pro/v1/routes/${props.routeId}`);
                        if (routeResponse.data) {
                            routes.value.push({
                                id: props.routeId,
                                name: routeResponse.data.name,
                                stations: stationsWithNames
                            });
                        }
                    }
                }
            } catch (apiError) {
                console.error('Ошибка загрузки станций маршрута:', apiError);
            }
        }

        // Загружаем продукты из реального API с фильтрацией по проекту
        try {
            if (!props.projectId) {
                console.warn('ProjectId не передан, продукты не будут загружены');
                availableProducts.value = [];
                return;
            }

            const productsResponse = await proApiClient.get(`/api/pro/v1/projects/${props.projectId}/products`);

            // API возвращает структуру { content: [...], pagination: {...} }
            if (productsResponse.data && productsResponse.data.content) {
                availableProducts.value = productsResponse.data.content.map(product => ({
                    id: product.id,
                    name: product.name,
                    description: product.description || '',
                    status: product.status
                }));
            } else {
                availableProducts.value = [];
            }
        } catch (apiError) {
            // Проверяем, является ли ошибка связанной с авторизацией
            if (apiError.response?.status === 401) {
                toast.add({
                    severity: 'error',
                    summary: 'Ошибка авторизации',
                    detail: 'Не удалось загрузить продукты. Проверьте авторизацию.',
                    life: 5000
                });
            }

            availableProducts.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки данных:', error);
    }
};

const loadTariffData = async () => {
    if (!props.tariffId) return;

    try {
        // Загружаем информацию о тарифе из API
        try {
            const tariffResponse = await proApiClient.get(`/api/pro/v1/tariffs/${props.tariffId}`);
            if (tariffResponse.data) {
                const tariff = tariffResponse.data;
                tariffInfo.value = {
                    name: tariff.name,
                    status: tariff.status,
                    tags: tariff.tags || ''
                };
            }
        } catch (apiError) {
            console.error('Ошибка загрузки тарифа из API:', apiError);
            // Fallback на localStorage если API недоступен
            if (props.routeId) {
                const data = await storageService.loadTariffData();
                const routeData = data.tariffData.find(item => item.routeId === props.routeId);

                if (routeData && routeData.tariffs[props.tariffId]) {
                    const tariff = routeData.tariffs[props.tariffId];
                    tariffInfo.value = {
                        name: tariff.name,
                        status: tariff.status,
                        tags: tariff.tags || ''
                    };
                    paymentMethods.value = [...(tariff.paymentMethods || [])];
                }
            }
        }

        // Загружаем продукты тарифа из API
        try {
            const tariffProductsResponse = await proApiClient.get(`/api/pro/v1/tariffs/${props.tariffId}/products`);

            if (tariffProductsResponse.data && Array.isArray(tariffProductsResponse.data)) {
                // Группируем продукты по способам оплаты
                const productsByPaymentMethod = {};
                tariffProductsResponse.data.forEach(product => {
                    const paymentMethod = product.paymentMethodType;
                    if (!productsByPaymentMethod[paymentMethod]) {
                        productsByPaymentMethod[paymentMethod] = [];
                    }
                    productsByPaymentMethod[paymentMethod].push({
                        id: product.id, // Используем ID записи product_dict_row, а не productId
                        name: product.productName || 'Неизвестный продукт',
                        description: product.description || '',
                        status: product.status || 'ACTIVE',
                        price: product.price || 0,
                        isFixPrice: product.isFixPrice,
                        paymentMethodType: product.paymentMethodType // Добавляем paymentMethodType
                    });
                });

                // Создаем способы оплаты с продуктами
                paymentMethods.value = Object.keys(productsByPaymentMethod).map(paymentMethod => ({
                    id: paymentMethod,
                    type: paymentMethod,
                    name: getPaymentMethodLabel(paymentMethod),
                    products: productsByPaymentMethod[paymentMethod]
                }));

                // Загружаем тарифную матрицу для каждого продукта
                for (const paymentMethod of paymentMethods.value) {
                    for (const product of paymentMethod.products) {
                        if (!product.isFixPrice) {
                            try {
                                console.log('Загружаем тарифную матрицу для продукта:', product.id);
                                const matrixResponse = await TariffService.getTariffMatrices(product.id);
                                console.log('Ответ API тарифной матрицы:', matrixResponse);

                                // matrixResponse уже содержит data, так как TariffService.getTariffMatrices возвращает response.data
                                if (matrixResponse && Array.isArray(matrixResponse)) {
                                    console.log('Данные матрицы:', matrixResponse);
                                    // Инициализируем tariffMatrix если её нет
                                    if (!product.tariffMatrix) {
                                        product.tariffMatrix = {};
                                    }

                                    // Заполняем матрицу данными из API
                                    matrixResponse.forEach(matrix => {
                                        console.log('Обрабатываем матрицу:', matrix);
                                        if (matrix.tags) {
                                            console.log('Добавляем в матрицу:', matrix.tags, '=', matrix.amount);
                                            product.tariffMatrix[matrix.tags] = matrix.amount || 0;
                                        }
                                    });
                                    console.log('Итоговая матрица продукта:', product.tariffMatrix);
                                } else {
                                    console.log('Нет данных матрицы или неверный формат:', matrixResponse);
                                }
                            } catch (matrixError) {
                                console.error('Ошибка загрузки тарифной матрицы для продукта:', product.id, matrixError);
                            }
                        }
                    }
                }
            } else {
                // Если нет продуктов, создаем пустые способы оплаты
                paymentMethods.value = [
                    { id: 'CASH', type: 'CASH', name: 'Наличные', products: [] },
                    { id: 'EMV', type: 'EMV', name: 'Банковская карта', products: [] },
                    { id: 'TROIKA_TICKET', type: 'TROIKA_TICKET', name: 'Тройка (билет)', products: [] }
                ];
            }
        } catch (apiError) {
            console.error('Ошибка загрузки продуктов тарифа из API:', apiError);
            // Fallback на localStorage
            if (props.routeId) {
                const data = await storageService.loadTariffData();
                const routeData = data.tariffData.find(item => item.routeId === props.routeId);

                if (routeData && routeData.tariffs[props.tariffId]) {
                    const tariff = routeData.tariffs[props.tariffId];
                    paymentMethods.value = [...(tariff.paymentMethods || [])];
                }
            }
        }
    } catch (error) {
        console.error('Ошибка загрузки данных тарифа:', error);
    }
};

const showAddPaymentMethodDialog = () => {
    selectedPaymentMethod.value = null;
    addPaymentMethodDialogVisible.value = true;
};

const addPaymentMethod = () => {
    if (!selectedPaymentMethod.value) return;

    const paymentMethod = availablePaymentMethods.value.find(
        pm => pm.id === selectedPaymentMethod.value
    );

    if (paymentMethod) {
        const newPaymentMethod = {
            ...paymentMethod,
            products: []
        };
        paymentMethods.value.push(newPaymentMethod);
        activeTabIndex.value = paymentMethods.value.length - 1;
    }

    addPaymentMethodDialogVisible.value = false;
    selectedPaymentMethod.value = null;
};

const removePaymentMethod = (index) => {
    paymentMethods.value.splice(index, 1);
    if (activeTabIndex.value >= paymentMethods.value.length) {
        activeTabIndex.value = Math.max(0, paymentMethods.value.length - 1);
    }
};

const showAddProductDialog = (paymentMethodId) => {
    currentPaymentMethodId.value = paymentMethodId;
    selectedProduct.value = null;
    addProductDialogVisible.value = true;

    // Проверяем, загружены ли данные
    if (availableProducts.value.length === 0) {
        loadData();
    }
};

const addProduct = async () => {
    if (!selectedProduct.value || !currentPaymentMethodId.value || !props.tariffId || !props.projectId) {
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не все необходимые данные заполнены',
            life: 3000
        });
        return;
    }

    const product = availableProducts.value.find(p => p.id === selectedProduct.value);
    const paymentMethod = paymentMethods.value.find(pm => pm.id === currentPaymentMethodId.value);

    if (product && paymentMethod) {
        try {
            // Создаем продукт тарифа через API
            const productData = {
                projectId: props.projectId, // projectId теперь обязательный
                productId: product.id,
                tariffId: props.tariffId,
                paymentMethodType: paymentMethod.type, // Используем тип способа оплаты (CASH, CARD и т.д.)
                isFixPrice: true, // По умолчанию фиксированная цена
                price: 0,
                tags: ''
            };

            const response = await proApiClient.post('/api/pro/v1/tariffs/products', productData);

            if (response.data) {
                // Добавляем продукт в UI
                const newProduct = {
                    id: product.id,
                    name: product.name,
                    isFixPrice: true,
                    price: 0,
                    tariffMatrix: {}
                };

                paymentMethod.products.push(newProduct);

                toast.add({
                    severity: 'success',
                    summary: 'Успешно',
                    detail: 'Продукт добавлен к тарифу',
                    life: 3000
                });
            }
        } catch (error) {
            console.error('Ошибка добавления продукта к тарифу:', error);

            if (error.response?.status === 401) {
                toast.add({
                    severity: 'error',
                    summary: 'Ошибка авторизации',
                    detail: 'Не удалось добавить продукт. Проверьте авторизацию.',
                    life: 5000
                });
            } else {
                toast.add({
                    severity: 'error',
                    summary: 'Ошибка',
                    detail: 'Не удалось добавить продукт к тарифу',
                    life: 3000
                });
            }
        }
    }

    addProductDialogVisible.value = false;
    selectedProduct.value = null;
    currentPaymentMethodId.value = null;
};

const removeProduct = async (paymentMethodId, productId) => {
    const paymentMethod = paymentMethods.value.find(pm => pm.id === paymentMethodId);
    if (paymentMethod) {
        const productIndex = paymentMethod.products.findIndex(p => p.id === productId);
        if (productIndex !== -1) {
            try {
                // Удаляем продукт через API
                await proApiClient.delete(`/api/pro/v1/tariffs/products/${productId}`);

                // Удаляем из UI
                paymentMethod.products.splice(productIndex, 1);

                toast.add({
                    severity: 'success',
                    summary: 'Успешно',
                    detail: 'Продукт удален из тарифа',
                    life: 3000
                });
            } catch (error) {
                console.error('Ошибка удаления продукта из тарифа:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Ошибка',
                    detail: 'Не удалось удалить продукт из тарифа',
                    life: 3000
                });
            }
        }
    }
};

const getPaymentMethodLabel = (method) => {
    const labels = {
        'CASH': 'Наличные',
        'EMV': 'Банковская карта',
        'TROIKA_TICKET': 'Тройка (билет)',
        'TROIKA_WALLET': 'Тройка (кошелек)',
        'ABT_TICKET': 'Транспортная карта (билет)',
        'ABT_WALLET': 'Транспортная карта (кошелек)',
        'PROSTOR_TICKET': 'Простор (билет)',
        'QR_TICKET': 'QR (билет)',
        'QR_WALLET': 'QR (кошелек)'
    };
    return labels[method] || method;
};

const getRouteStations = () => {
    const route = routes.value.find(r => r.id === props.routeId);
    console.log('Found route:', route);
    if (route && route.stations) {
        console.log('Route stations:', route.stations);
        // Если станции - это объекты, извлекаем названия
        if (Array.isArray(route.stations) && route.stations.length > 0 && typeof route.stations[0] === 'object') {
            console.log('First station object:', route.stations[0]);
            const stationNames = route.stations.map(station => {
                const name = station.stationName || 'Неизвестная станция';
                console.log('Station object:', station, 'Extracted name:', name);
                return name;
            });
            console.log('Extracted station names:', stationNames);
            return stationNames;
        }
        // Если станции - это уже строки
        console.log('Stations are already strings:', route.stations);
        return route.stations;
    }
    console.log('No route or stations found');
    return [];
};

const getStationIdByName = (stationName) => {
    const route = routes.value.find(r => r.id === props.routeId);
    if (route && route.stations) {
        const station = route.stations.find(s =>
            s.stationName === stationName
        );
        return station ? station.stationId : null;
    }
    return null;
};

const getMatrixValue = (product, fromStation, toStation) => {
    if (!product.tariffMatrix) {
        product.tariffMatrix = {};
    }
    const key = `${fromStation}-${toStation}`;
    return product.tariffMatrix[key] || 0;
};

const updateMatrixValue = (product, fromStation, toStation, value) => {
    if (!product.tariffMatrix) {
        product.tariffMatrix = {};
    }
    const key = `${fromStation}-${toStation}`;
    product.tariffMatrix[key] = value;
};

const saveProductChanges = async (product) => {
    try {
        console.log('Debug product object:', product);
        console.log('Debug product.paymentMethodType:', product.paymentMethodType);

        // Подготавливаем данные для обновления
        const updateData = {
            paymentMethodType: product.paymentMethodType,
            isFixPrice: product.isFixPrice,
            price: product.price || 0
        };

        console.log('Debug updateData:', updateData);

        // Отправляем запрос на обновление продукта
        await TariffService.updateTariffProduct(product.id, updateData);

        // Если это не фиксированный тариф, сохраняем тарифную матрицу
        if (!product.isFixPrice && product.tariffMatrix) {
            await saveTariffMatrix(product);
        }

        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Изменения продукта сохранены',
            life: 3000
        });
    } catch (error) {
        console.error('Ошибка сохранения изменений продукта:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось сохранить изменения продукта',
            life: 3000
        });
    }
};

const saveTariffMatrix = async (product) => {
    try {
        const matrixData = {};
        const routeStations = getRouteStations();
        for (let i = 0; i < routeStations.length; i++) {
            for (let j = i + 1; j < routeStations.length; j++) {
                const fromStation = routeStations[i];
                const toStation = routeStations[j];
                const value = getMatrixValue(product, fromStation, toStation);
                if (value > 0) { // Сохраняем только ненулевые значения
                    matrixData[`${fromStation}-${toStation}`] = value;
                }
            }
        }

        if (Object.keys(matrixData).length > 0) {
            await TariffService.updateTariffMatrixForProduct(product.id, matrixData);
            console.log('Тарифная матрица сохранена для продукта:', product.id);
        }
    } catch (error) {
        console.error('Ошибка сохранения тарифной матрицы:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось сохранить тарифную матрицу',
            life: 3000
        });
    }
};

const saveSettings = async () => {
    try {
        saving.value = true;

        const settings = {
            name: tariffInfo.value.name,
            status: tariffInfo.value.status,
            tags: tariffInfo.value.tags,
            paymentMethods: paymentMethods.value
        };

        await storageService.updateTariffSettings(props.routeId, props.tariffId, settings);

        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Настройки тарифа сохранены',
            life: 3000
        });

        emit('saved');
        closeDialog();
    } catch (error) {
        console.error('Ошибка сохранения настроек:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось сохранить настройки',
            life: 3000
        });
    } finally {
        saving.value = false;
    }
};

const closeDialog = () => {
    visible.value = false;
    emit('update:visible', false);
};

// Watchers
watch(() => props.visible, (newValue) => {
    if (newValue) {
        loadData();
        loadTariffData();
    }
});

// Инициализация
onMounted(() => {
    loadData();
});
</script>

<style scoped>
.tariff-settings-dialog {
    max-height: 90vh;
}

.tariff-settings-content {
    max-height: 70vh;
    overflow-y: auto;
}

.tariff-info-section {
    background-color: var(--surface-50);
    padding: 1.5rem;
    border-radius: 0.5rem;
}

.payment-methods-section {
    background-color: var(--surface-0);
    border: 1px solid var(--surface-200);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.payment-method-content {
    padding: 1rem 0;
}

.products-section {
    margin-top: 1rem;
}

.no-products {
    background-color: var(--surface-50);
    border: 2px dashed var(--surface-300);
    border-radius: 0.5rem;
}

.product-item {
    background-color: var(--surface-0);
    border: 1px solid var(--surface-200);
}

.cost-settings {
    margin-top: 1rem;
}

.fixed-price-input {
    margin-top: 0.5rem;
}

.tariff-matrix {
    margin-top: 0.5rem;
}

.matrix-container {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;
}

.matrix-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.matrix-header {
    background-color: var(--surface-100);
    border: 1px solid var(--surface-300);
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
    min-width: 80px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.matrix-station-label {
    background-color: var(--surface-50);
    border: 1px solid var(--surface-300);
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
    min-width: 120px;
    position: sticky;
    left: 0;
    z-index: 5;
}

.matrix-cell {
    border: 1px solid var(--surface-200);
    padding: 0.25rem;
    text-align: center;
    min-width: 80px;
}

.matrix-empty {
    color: var(--text-color-secondary);
    font-size: 0.75rem;
}

.matrix-item {
    background-color: var(--surface-50);
    border: 1px solid var(--surface-200);
}

:deep(.p-tabview-panels) {
    padding: 1rem 0;
}

:deep(.p-tabview-nav) {
    border-bottom: 1px solid var(--surface-200);
}

:deep(.p-tabview-nav-link) {
    padding: 0.75rem 1rem;
}
</style>
