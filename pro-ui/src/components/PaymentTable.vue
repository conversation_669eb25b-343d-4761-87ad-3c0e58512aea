<template>
    <div class="card">
        <Toast />
        <ConfirmDialog />

        <!-- Заголовок и кнопки -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold"></h2>
            <!-- Кнопки управления -->
            <div class="flex gap-2">
                <!-- Кнопка сворачивания фильтров -->
                <Button
                    :icon="filtersCollapsed ? 'pi pi-chevron-down' : 'pi pi-chevron-up'"
                    @click="toggleFilters"
                    :label="filtersCollapsed ? 'Показать фильтры' : 'Скрыть фильтры'"
                    class="p-button-outlined p-button-sm"
                />

                <!-- Кнопка настроек колонок -->
                <Button
                    icon="pi pi-cog"
                    @click="columnSettingsVisible = true"
                    label="Настройки колонок"
                    class="p-button-outlined p-button-sm"
                />

                <!-- Кнопка экспорта -->
                <Button
                    icon="pi pi-download"
                    @click="showExportDialog = true"
                    label="Экспорт"
                    class="p-button-outlined p-button-sm"
                    :disabled="isExporting"
                />
            </div>
        </div>

        <!-- Фильтры -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-semibold">Фильтры</h3>
                <Button
                    :icon="filtersCollapsed ? 'pi pi-chevron-down' : 'pi pi-chevron-up'"
                    @click="toggleFilters"
                    class="p-button-text"
                    :label="filtersCollapsed ? 'Развернуть' : 'Свернуть'"
                />
            </div>

            <div v-show="!filtersCollapsed" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 border rounded-lg bg-gray-50">
                <!-- Фильтр по дате -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Дата проведения операции</label>
                    <Calendar
                        v-model="filters.dateRange"
                        selectionMode="range"
                        :showIcon="true"
                        placeholder="Выберите период"
                        class="w-full"
                        @date-select="onDateFilterChange"
                    />
                </div>

                <!-- Фильтр по организации -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Организация</label>
                    <AutoComplete
                        v-model="filters.organizationId"
                        :suggestions="filteredOrganizations"
                        @complete="searchOrganizations"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Начните вводить название организации..."
                        class="w-full"
                        :loading="organizationSearchLoading"
                        :delay="300"
                        :minLength="2"
                        :dropdown="true"
                        :forceSelection="true"
                        :showClear="true"
                        @change="onOrganizationFilterChange"
                    />
                </div>

                <!-- Фильтр по маршруту -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Маршрут</label>
                    <Dropdown
                        v-model="filters.routeId"
                        :options="routes"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Выберите маршрут"
                        class="w-full"
                        :showClear="true"
                        @change="onRouteFilterChange"
                    />
                </div>

                <!-- Фильтр по способу оплаты -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Способ оплаты</label>
                    <Dropdown
                        v-model="filters.payMethod"
                        :options="paymentMethods"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Выберите способ"
                        class="w-full"
                        :showClear="true"
                        @change="onPaymentMethodFilterChange"
                    />
                </div>

                <!-- Фильтр по ID билета -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">ID билета</label>
                    <InputText
                        v-model="filters.ticketId"
                        placeholder="Введите ID билета"
                        class="w-full"
                        @input="onTicketIdFilterChange"
                    />
                </div>

                <!-- Фильтр по серии билета -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Серия билета</label>
                    <InputText
                        v-model="filters.ticketSeries"
                        placeholder="Введите серию билета"
                        class="w-full"
                        @input="onTicketSeriesFilterChange"
                    />
                </div>

                <!-- Фильтр по номеру билета -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Номер билета</label>
                    <InputText
                        v-model="filters.ticketNumber"
                        placeholder="Введите номер билета"
                        class="w-full"
                        @input="onTicketNumberFilterChange"
                    />
                </div>

                <!-- Фильтр по идентификатору операции -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Идентификатор операции</label>
                    <InputText
                        v-model="filters.trxId"
                        placeholder="Введите ID операции"
                        class="w-full"
                        @input="onTrxIdFilterChange"
                    />
                </div>

                <!-- Фильтр по терминалу -->
                <div class="flex flex-col">
                    <label class="text-sm font-medium mb-1">Терминал</label>
                    <InputText
                        v-model="filters.terminalSerial"
                        placeholder="Введите серийный номер терминала"
                        class="w-full"
                        @input="onTerminalFilterChange"
                    />
                </div>

                <!-- Кнопки управления фильтрами -->
                <div class="flex items-end gap-2">
                    <Button
                        label="Применить"
                        icon="pi pi-check"
                        @click="onApplyFilters"
                        class="p-button-success"
                    />
                    <Button
                        label="Сбросить"
                        icon="pi pi-refresh"
                        @click="resetFilters"
                        class="p-button-outlined"
                    />
                </div>
            </div>
        </div>

                <!-- Таблица -->
        <DataTable
            :value="payments"
            :loading="loading"
            :paginator="true"
            :rows="pagination.limit"
            :totalRecords="pagination.totalCount"
            :lazy="true"
            @page="onPageChange"
            @sort="onSort"
            :sortField="pagination.sortField"
            dataKey="ticketId"
            :resizableColumns="true"
            :reorderableColumns="true"
            @column-reorder="onColumnReorder"
            columnResizeMode="expand"
            showGridlines
            stripedRows
            class="p-datatable-lg"
        >
            <!-- Динамические колонки в правильном порядке -->
            <template v-for="columnKey in orderedColumns" :key="columnKey">
                <!-- ID билета -->
                <Column
                    v-if="columnKey === 'ticketId'"
                    field="ticketId"
                    header="ID билета"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.ticketId }}</span>
                    </template>
                </Column>

                <!-- Дата проведения операции -->
                <Column
                    v-if="columnKey === 'createdAt'"
                    field="createdAt"
                    header="Дата проведения операции"
                    sortable
                    class="min-w-[180px]"
                >
                    <template #body="{ data }">
                        {{ formatDateTime(data.createdAt) }}
                    </template>
                </Column>

                <!-- Серия билета -->
                <Column
                    v-if="columnKey === 'ticketSeries'"
                    field="ticketSeries"
                    header="Серия билета"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.ticketSeries || '-' }}</span>
                    </template>
                </Column>

                <!-- Номер билета -->
                <Column
                    v-if="columnKey === 'ticketNumber'"
                    field="ticketNumber"
                    header="Номер билета"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.ticketNumber || '-' }}</span>
                    </template>
                </Column>

                <!-- Организация -->
                <Column
                    v-if="columnKey === 'orgName'"
                    field="orgName"
                    header="Организация"
                    sortable
                    class="min-w-[200px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.orgName }}</span>
                    </template>
                </Column>

                <!-- Маршрут -->
                <Column
                    v-if="columnKey === 'routeName'"
                    field="routeName"
                    header="Маршрут"
                    sortable
                    class="min-w-[180px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.routeName }}</span>
                    </template>
                </Column>

                <!-- Номер маршрута -->
                <Column
                    v-if="columnKey === 'routeNumber'"
                    field="routeNumber"
                    header="Номер маршрута"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.routeNumber }}</span>
                    </template>
                </Column>

                <!-- Сумма -->
                <Column
                    v-if="columnKey === 'amount'"
                    field="amount"
                    header="Сумма"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium text-green-600">
                            {{ formatCurrency(data.amount) }}
                        </span>
                    </template>
                </Column>

                <!-- Способ оплаты -->
                <Column
                    v-if="columnKey === 'payMethod'"
                    field="payMethod"
                    header="Способ оплаты"
                    sortable
                    class="min-w-[140px]"
                >
                    <template #body="{ data }">
                        <Tag
                            :value="getPaymentMethodLabel(data.payMethod)"
                            :severity="getPaymentMethodSeverity(data.payMethod)"
                        />
                    </template>
                </Column>

                <!-- Водитель -->
                <Column
                    v-if="columnKey === 'driverFio'"
                    field="driverFio"
                    header="Водитель"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        {{ data.driverFio || '-' }}
                    </template>
                </Column>

                <!-- Кондуктор -->
                <Column
                    v-if="columnKey === 'conductorFio'"
                    field="conductorFio"
                    header="Кондуктор"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        {{ data.conductorFio || '-' }}
                    </template>
                </Column>

                <!-- Транспортное средство -->
                <Column
                    v-if="columnKey === 'vhNumber'"
                    field="vhNumber"
                    header="Транспорт"
                    sortable
                    class="min-w-[140px]"
                >
                    <template #body="{ data }">
                        <div>
                            <div class="font-medium">{{ data.vhNumber }}</div>
                            <div class="text-sm text-gray-600">{{ data.vhType }}</div>
                        </div>
                    </template>
                </Column>

                <!-- Станция входа -->
                <Column
                    v-if="columnKey === 'stantionFrom'"
                    field="stantionFrom"
                    header="Станция входа"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.stantionFrom || '-' }}</span>
                    </template>
                </Column>

                <!-- Станция выхода -->
                <Column
                    v-if="columnKey === 'stantionTo'"
                    field="stantionTo"
                    header="Станция выхода"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        <span class="font-medium">{{ data.stantionTo || '-' }}</span>
                    </template>
                </Column>

                <!-- Тариф -->
                <Column
                    v-if="columnKey === 'tariffName'"
                    field="tariffName"
                    header="Тариф"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        {{ data.tariffName || '-' }}
                    </template>
                </Column>

                <!-- Продукт -->
                <Column
                    v-if="columnKey === 'productName'"
                    field="productName"
                    header="Продукт"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        {{ data.productName || '-' }}
                    </template>
                </Column>

                <!-- Терминал -->
                <Column
                    v-if="columnKey === 'terminalSerial'"
                    field="terminalSerial"
                    header="Терминал"
                    sortable
                    class="min-w-[140px]"
                >
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.terminalSerial }}</span>
                    </template>
                </Column>

                <!-- Смена -->
                <Column
                    v-if="columnKey === 'shiftNum'"
                    field="shiftNum"
                    header="Смена"
                    sortable
                    class="min-w-[80px]"
                >
                    <template #body="{ data }">
                        {{ data.shiftNum }}
                    </template>
                </Column>

                <!-- ЕРН -->
                <Column
                    v-if="columnKey === 'ern'"
                    field="ern"
                    header="ЕРН"
                    sortable
                    class="min-w-[80px]"
                >
                    <template #body="{ data }">
                        {{ data.ern }}
                    </template>
                </Column>

                <!-- Номер карты -->
                <Column
                    v-if="columnKey === 'maskedPan'"
                    field="maskedPan"
                    header="Номер карты"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <div v-if="data.maskedPan">
                            <div class="font-mono text-sm">{{ data.maskedPan }}</div>
                            <div class="text-xs text-gray-600">{{ data.paySystem }}</div>
                        </div>
                        <span v-else class="text-gray-400">-</span>
                    </template>
                </Column>

                <!-- UID ТК -->
                <Column
                    v-if="columnKey === 'cardUid'"
                    field="cardUid"
                    header="UID ТК"
                    sortable
                    class="min-w-[120px]"
                >
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.cardUid || '-' }}</span>
                    </template>
                </Column>

                <!-- Идентификатор операции -->
                <Column
                    v-if="columnKey === 'trxId'"
                    field="trxId"
                    header="Идентификатор операции"
                    sortable
                    class="min-w-[150px]"
                >
                    <template #body="{ data }">
                        <span class="font-mono text-sm">{{ data.trxId || '-' }}</span>
                    </template>
                </Column>

                <!-- Дата загрузки на сервер -->
                <Column
                    v-if="columnKey === 'recordAt'"
                    field="recordAt"
                    header="Дата загрузки на сервер"
                    sortable
                    class="min-w-[180px]"
                >
                    <template #body="{ data }">
                        {{ formatDateTime(data.recordAt) }}
                    </template>
                </Column>
            </template>
        </DataTable>

                <!-- Модальное окно настроек колонок -->
        <Dialog
            v-model:visible="columnSettingsVisible"
            modal
            header="Настройки колонок"
            :style="{ width: '600px' }"
        >
            <div class="space-y-4">
                <div class="text-sm text-gray-600 mb-3">
                    Перетаскивайте колонки в таблице для изменения их порядка. Используйте чекбоксы для управления видимостью.
                </div>

                <div class="space-y-2">
                    <div v-for="column in columnOrder" :key="column" class="flex items-center p-2 border rounded hover:bg-gray-50">
                        <div class="flex items-center mr-3">
                            <i class="pi pi-bars text-gray-400 mr-2"></i>
                            <Checkbox
                                :modelValue="columnVisibility[column]"
                                @update:modelValue="(value) => toggleColumn(column, value)"
                                :binary="true"
                            />
                        </div>
                        <label class="flex-1 cursor-pointer" @click="toggleColumn(column, !columnVisibility[column])">
                            {{ getColumnLabel(column) }}
                        </label>
                        <div class="text-xs text-gray-500">
                            {{ columnVisibility[column] ? 'Видима' : 'Скрыта' }}
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <Button label="Сбросить" @click="resetColumnSettings" class="p-button-outlined" />
                <Button label="Сохранить" @click="saveColumnSettings" class="p-button-success" />
            </template>
        </Dialog>

        <!-- Модальное окно выбора формата экспорта -->
        <Dialog
            v-model:visible="showExportDialog"
            header="Выберите формат экспорта"
            :modal="true"
            :closable="!isExporting"
            :closeOnEscape="!isExporting"
            class="w-96"
        >
            <div class="flex flex-col gap-4">
                <p class="text-gray-600">
                    Выберите формат для экспорта данных. Экспорт будет включать только видимые колонки в текущем порядке.
                </p>

                <div class="flex flex-col gap-3">
                    <Button
                        icon="pi pi-file-excel"
                        label="Выгрузить данные в Excel"
                        @click="exportToExcel"
                        class="p-button-success"
                        :loading="isExporting"
                        :disabled="isExporting"
                    />

                    <Button
                        icon="pi pi-file"
                        label="Выгрузить данные в CSV"
                        @click="exportToCSV"
                        class="p-button-info"
                        :loading="isExporting"
                        :disabled="isExporting"
                    />
                </div>
            </div>
        </Dialog>

        <!-- Модальное окно прогресса экспорта -->
        <Dialog
            v-model:visible="isExporting"
            header="Экспорт данных"
            :modal="true"
            :closable="false"
            :closeOnEscape="false"
            class="w-96"
        >
            <div class="flex flex-col gap-4">
                <div class="text-center">
                    <i class="pi pi-spin pi-spinner text-3xl text-blue-500 mb-2"></i>
                    <p class="text-gray-600">Загрузка данных для экспорта...</p>
                </div>

                <div class="w-full">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Прогресс: {{ exportProgress }}%</span>
                        <span>Страница {{ exportCurrentPage }} из {{ exportTotalPages }}</span>
                    </div>
                    <ProgressBar :value="exportProgress" class="w-full" />
                </div>

                <div class="flex justify-center">
                    <Button
                        icon="pi pi-times"
                        label="Отменить"
                        @click="cancelExport"
                        class="p-button-danger p-button-sm"
                    />
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import {useRoute} from 'vue-router';
import {PaymentService} from '@/service/PaymentService';
import {OrganizationService} from '@/service/OrganizationService';
import * as XLSX from 'xlsx';

const route = useRoute();
const projectId = route.params.projectId;

// Вспомогательная функция для скачивания файлов
const downloadFile = (blob, fileName) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};

// Состояние
const loading = ref(false);
const payments = ref([]);
const organizations = ref([]);
const routes = ref([]);
const paymentMethods = ref([]);
const filtersCollapsed = ref(false);
const columnSettingsVisible = ref(false);
const filteredOrganizations = ref([]);
const organizationSearchLoading = ref(false);

// Состояние экспорта
const isExporting = ref(false);
const exportProgress = ref(0);
const exportTotalPages = ref(0);
const exportCurrentPage = ref(0);
const exportAbortController = ref(null);
const showExportDialog = ref(false);

// Фильтры
const filters = ref({
    dateRange: null,
    organizationId: null,
    routeId: null,
    payMethod: null,
    ticketId: '',
    ticketSeries: '',
    ticketNumber: '',
    trxId: '',
    terminalSerial: ''
});

        // Пагинация
        const pagination = ref({
            page: 0,
            limit: 20,
            totalCount: 0,
            totalPage: 0,
            sortField: 'createdAt',
            sortOrder: 'desc'
        });

// Порядок колонок
const columnOrder = ref([
    'ticketId',
    'createdAt',
    'ticketSeries',
    'ticketNumber',
    'orgName',
    'routeName',
    'routeNumber',
    'amount',
    'payMethod',
    'driverFio',
    'conductorFio',
    'vhNumber',
    'stantionFrom',
    'stantionTo',
    'tariffName',
    'productName',
    'terminalSerial',
    'shiftNum',
    'ern',
    'maskedPan',
    'cardUid',
    'trxId',
    'recordAt'
]);

// Видимость колонок
const columnVisibility = ref({
    ticketId: true,
    createdAt: true,
    ticketSeries: true,
    ticketNumber: true,
    orgName: true,
    routeName: true,
    routeNumber: true,
    amount: true,
    payMethod: true,
    driverFio: true,
    conductorFio: true,
    vhNumber: true,
    stantionFrom: true,
    stantionTo: true,
    tariffName: true,
    productName: true,
    terminalSerial: true,
    shiftNum: true,
    ern: true,
    maskedPan: true,
    cardUid: true,
    trxId: true,
    recordAt: true
});

// Загрузка данных
onMounted(async () => {
    await Promise.all([
        loadOrganizations(),
        loadRoutes(),
        loadPaymentMethods(),
        loadColumnSettings(),
        loadFiltersState()
    ]);
    await loadPayments();
});

// Загрузка платежей
const loadPayments = async () => {
    try {
        loading.value = true;

        // Очищаем данные перед загрузкой новых
        payments.value = [];

        // Подготавливаем фильтры для API
        const apiFilters = {
            ...filters.value,
            createdAtFrom: filters.value.dateRange?.[0]?.toISOString(),
            createdAtTo: filters.value.dateRange?.[1]?.toISOString()
        };

        const response = await PaymentService.getPayments(projectId, apiFilters, pagination.value);
        payments.value = response.payments;
        pagination.value.totalCount = response.pagination.totalCount;
        pagination.value.totalPage = response.pagination.totalPage;
    } catch (error) {
        console.error('Ошибка загрузки платежей:', error);
        // Очищаем данные в случае ошибки
        payments.value = [];
        pagination.value.totalCount = 0;
        pagination.value.totalPage = 0;
    } finally {
        loading.value = false;
    }
};

// Загрузка организаций
const loadOrganizations = async () => {
    try {
        const response = await PaymentService.getOrganizations(projectId);
        organizations.value = response;
        
        // Инициализируем отфильтрованный список организаций
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
        organizations.value = [];
        filteredOrganizations.value = [];
    }
};

const searchOrganizations = async (event) => {
    const query = event.query;
    if (!query || query.length < 2) {
        // Проверяем, что organizations.value существует и является массивом
        if (organizations.value && Array.isArray(organizations.value)) {
            filteredOrganizations.value = organizations.value.slice(0, 20);
        } else {
            filteredOrganizations.value = [];
        }
        return;
    }

    try {
        organizationSearchLoading.value = true;
        const data = await OrganizationService.searchOrganizationsByName(query, 0, 20);
        filteredOrganizations.value = data || [];
    } catch (error) {
        console.error('Ошибка поиска организаций:', error);
        filteredOrganizations.value = [];
    } finally {
        organizationSearchLoading.value = false;
    }
};

// Загрузка маршрутов
const loadRoutes = async () => {
    try {
        const response = await PaymentService.getRoutes(projectId);
        routes.value = response;
    } catch (error) {
        console.error('Ошибка загрузки маршрутов:', error);
        routes.value = [];
    }
};

// Загрузка способов оплаты
const loadPaymentMethods = async () => {
    try {
        const response = await PaymentService.getPaymentMethods();
        paymentMethods.value = response;
    } catch (error) {
        console.error('Ошибка загрузки способов оплаты:', error);
        paymentMethods.value = [];
    }
};



// Обработчики событий
const onPageChange = (event) => {
    pagination.value.page = event.page;
    pagination.value.limit = event.rows;
    loadPayments();
};

const onSort = (event) => {
    pagination.value.sortField = event.sortField;
    pagination.value.sortOrder = event.sortOrder === 1 ? 'asc' : 'desc';
    // Сбрасываем страницу при изменении сортировки
    pagination.value.page = 0;
    loadPayments();
};

const onDateFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    // Debounce для фильтра по дате
    setTimeout(() => loadPayments(), 300);
};

const onTicketIdFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    setTimeout(() => loadPayments(), 300);
};

const onTicketSeriesFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    setTimeout(() => loadPayments(), 300);
};

const onTicketNumberFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    setTimeout(() => loadPayments(), 300);
};

const onTrxIdFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    setTimeout(() => loadPayments(), 300);
};

const onTerminalFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    setTimeout(() => loadPayments(), 300);
};

// Обработчики для фильтров-выпадающих списков
const onOrganizationFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    loadPayments();
};

const onRouteFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    loadPayments();
};

const onPaymentMethodFilterChange = () => {
    // Сбрасываем страницу при изменении фильтра
    pagination.value.page = 0;
    loadPayments();
};

const onApplyFilters = () => {
    // Сбрасываем страницу при применении фильтров
    pagination.value.page = 0;
    loadPayments();
};

const resetFilters = () => {
    filters.value = {
        dateRange: null,
        organizationId: null,
        routeId: null,
        payMethod: null,
        ticketId: '',
        ticketSeries: '',
        ticketNumber: '',
        trxId: '',
        terminalSerial: ''
    };
    // Сбрасываем страницу при сбросе фильтров
    pagination.value.page = 0;
    loadPayments();
};

// Открытие настроек колонок
const openColumnSettings = () => {
    columnSettingsVisible.value = true;
};

// Переключение видимости фильтров
const toggleFilters = () => {
    filtersCollapsed.value = !filtersCollapsed.value;
    localStorage.setItem(`payment-filters-collapsed-${projectId}`, filtersCollapsed.value.toString());
};

const toggleColumn = (column, visible) => {
    columnVisibility.value[column] = visible;
};

const resetColumnSettings = () => {
    columnVisibility.value = {
        ticketId: true,
        createdAt: true,
        ticketSeries: true,
        ticketNumber: true,
        orgName: true,
        routeName: true,
        routeNumber: true,
        amount: true,
        payMethod: true,
        driverFio: true,
        conductorFio: true,
        vhNumber: true,
        stantionFrom: true,
        stantionTo: true,
        tariffName: true,
        productName: true,
        terminalSerial: true,
        shiftNum: true,
        ern: true,
        maskedPan: true,
        cardUid: true,
        trxId: true,
        recordAt: true
    };

    columnOrder.value = [
        'ticketId',
        'createdAt',
        'ticketSeries',
        'ticketNumber',
        'orgName',
        'routeName',
        'routeNumber',
        'amount',
        'payMethod',
        'driverFio',
        'conductorFio',
        'vhNumber',
        'stantionFrom',
        'stantionTo',
        'tariffName',
        'productName',
        'terminalSerial',
        'shiftNum',
        'ern',
        'maskedPan',
        'cardUid',
        'trxId',
        'recordAt'
    ];
};

const saveColumnSettings = () => {
    localStorage.setItem(`payment-columns-${projectId}`, JSON.stringify(columnVisibility.value));
    localStorage.setItem(`payment-column-order-${projectId}`, JSON.stringify(columnOrder.value));
    columnSettingsVisible.value = false;
};

// Обработка перетаскивания колонок
const onColumnReorder = (event) => {
    console.log('=== onColumnReorder вызван ===');
    console.log('event:', event);
    console.log('dragIndex:', event.dragIndex);
    console.log('dropIndex:', event.dropIndex);

    if (event && typeof event.dragIndex === 'number' && typeof event.dropIndex === 'number') {
        console.log('Обновляем порядок колонок...');
        console.log('Старый columnOrder:', columnOrder.value);

        // Создаем копию массива
        const newOrder = [...columnOrder.value];

        // Перемещаем элемент из dragIndex в dropIndex
        const draggedColumn = newOrder.splice(event.dragIndex, 1)[0];
        newOrder.splice(event.dropIndex, 0, draggedColumn);

        // Обновляем порядок колонок
        columnOrder.value = newOrder;

        console.log('Новый columnOrder:', columnOrder.value);

        // Сохраняем в localStorage
        const storageKey = `payment-column-order-${projectId}`;
        console.log('Сохраняем в localStorage с ключом:', storageKey);
        console.log('Данные для сохранения:', JSON.stringify(columnOrder.value));

        localStorage.setItem(storageKey, JSON.stringify(columnOrder.value));

        // Проверяем, что сохранилось
        const saved = localStorage.getItem(storageKey);
        console.log('Проверка сохранения - получено из localStorage:', saved);
        console.log('Проверка сохранения - парсированное значение:', JSON.parse(saved));

        console.log('=== onColumnReorder завершен ===');
    } else {
        console.log('❌ event.dragIndex или event.dropIndex не определены');
        console.log('event.dragIndex:', event.dragIndex);
        console.log('event.dropIndex:', event.dropIndex);
    }
};

// Загрузка настроек из localStorage
const loadColumnSettings = () => {
    console.log('=== loadColumnSettings вызван ===');

    const saved = localStorage.getItem(`payment-columns-${projectId}`);
    if (saved) {
        console.log('Найдены сохраненные настройки видимости:', saved);
        columnVisibility.value = { ...columnVisibility.value, ...JSON.parse(saved) };
    } else {
        console.log('Сохраненные настройки видимости не найдены');
    }

    const savedOrder = localStorage.getItem(`payment-column-order-${projectId}`);
    if (savedOrder) {
        console.log('Найден сохраненный порядок колонок:', savedOrder);
        const parsedOrder = JSON.parse(savedOrder);
        console.log('Парсированный порядок:', parsedOrder);
        console.log('Старый columnOrder:', columnOrder.value);
        columnOrder.value = parsedOrder;
        console.log('Новый columnOrder:', columnOrder.value);
    } else {
        console.log('Сохраненный порядок колонок не найден');
        console.log('Используется порядок по умолчанию:', columnOrder.value);
    }

    console.log('Итоговый columnOrder.value:', columnOrder.value);
    console.log('Итоговый columnVisibility.value:', columnVisibility.value);
    console.log('=== loadColumnSettings завершен ===');
};

const loadFiltersState = () => {
    const saved = localStorage.getItem(`payment-filters-collapsed-${projectId}`);
    if (saved !== null) {
        filtersCollapsed.value = JSON.parse(saved);
    }
};

// Утилиты
const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU');
};

const formatCurrency = (amount) => {
    if (!amount) return '0.00 ₽';
    // Конвертируем копейки в рубли
    const rubles = amount / 100;
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(rubles);
};

const getPaymentMethodLabel = (method) => {
    const labels = {
        'CASH': 'Наличные',
        'CARD': 'Карта',
        'MOBILE': 'Мобильная',
        'QR': 'QR-код'
    };
    return labels[method] || method;
};

const getPaymentMethodSeverity = (method) => {
    const severities = {
        'CASH': 'warning',
        'CARD': 'success',
        'MOBILE': 'info',
        'QR': 'secondary'
    };
    return severities[method] || 'info';
};

const getColumnLabel = (column) => {
    const labels = {
        ticketId: 'ID билета',
        createdAt: 'Дата проведения операции',
        ticketSeries: 'Серия билета',
        ticketNumber: 'Номер билета',
        orgName: 'Организация',
        routeName: 'Маршрут',
        routeNumber: 'Номер маршрута',
        amount: 'Сумма',
        payMethod: 'Способ оплаты',
        driverFio: 'Водитель',
        conductorFio: 'Кондуктор',
        vhNumber: 'Транспорт',
        stantionFrom: 'Станция входа',
        stantionTo: 'Станция выхода',
        tariffName: 'Тариф',
        productName: 'Продукт',
        terminalSerial: 'Терминал',
        shiftNum: 'Смена',
        ern: 'ЕРН',
        maskedPan: 'Номер карты',
        cardUid: 'UID ТК',
        trxId: 'Идентификатор операции',
        recordAt: 'Дата загрузки на сервер'
    };
    return labels[column] || column;
};

// Вычисляемое свойство для колонок в правильном порядке
const orderedColumns = computed(() => {
    const result = columnOrder.value.filter(column => columnVisibility.value[column]);
    console.log('orderedColumns computed вызван:', {
        columnOrder: columnOrder.value,
        columnVisibility: columnVisibility.value,
        result: result
    });
    return result;
});

// Конфигурация колонок
const columnConfigs = {
    ticketId: {
        field: 'ticketId',
        header: 'ID билета',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => `<span class="font-mono text-sm">${data.ticketId}</span>`
    },
    createdAt: {
        field: 'createdAt',
        header: 'Дата проведения операции',
        sortable: true,
        class: 'min-w-[180px]',
        template: (data) => formatDateTime(data.createdAt)
    },
    orgName: {
        field: 'orgName',
        header: 'Организация',
        sortable: true,
        class: 'min-w-[200px]',
        template: (data) => `<span class="font-medium">${data.orgName}</span>`
    },
    routeName: {
        field: 'routeName',
        header: 'Маршрут',
        sortable: true,
        class: 'min-w-[180px]',
        template: (data) => `<span class="font-medium">${data.routeName}</span>`
    },
    routeNumber: {
        field: 'routeNumber',
        header: 'Номер маршрута',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => `<span class="font-medium">${data.routeNumber}</span>`
    },
    amount: {
        field: 'amount',
        header: 'Сумма',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => `<span class="font-medium text-green-600">${formatCurrency(data.amount)}</span>`
    },
    payMethod: {
        field: 'payMethod',
        header: 'Способ оплаты',
        sortable: true,
        class: 'min-w-[140px]',
        template: (data) => `<Tag value="${getPaymentMethodLabel(data.payMethod)}" :severity="getPaymentMethodSeverity(data.payMethod)" />`
    },
    driverFio: {
        field: 'driverFio',
        header: 'Водитель',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => data.driverFio || '-'
    },
    conductorFio: {
        field: 'conductorFio',
        header: 'Кондуктор',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => data.conductorFio || '-'
    },
    vhNumber: {
        field: 'vhNumber',
        header: 'Транспорт',
        sortable: true,
        class: 'min-w-[140px]',
        template: (data) => `
            <div>
                <div class="font-medium">${data.vhNumber}</div>
                <div class="text-sm text-gray-600">${data.vhType}</div>
            </div>
        `
    },
    stantionFrom: {
        field: 'stantionFrom',
        header: 'Станция входа',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => `<span class="font-medium">${data.stantionFrom || '-'}</span>`
    },
    stantionTo: {
        field: 'stantionTo',
        header: 'Станция выхода',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => `<span class="font-medium">${data.stantionTo || '-'}</span>`
    },
    tariffName: {
        field: 'tariffName',
        header: 'Тариф',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => data.tariffName || '-'
    },
    productName: {
        field: 'productName',
        header: 'Продукт',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => data.productName || '-'
    },
    terminalSerial: {
        field: 'terminalSerial',
        header: 'Терминал',
        sortable: true,
        class: 'min-w-[140px]',
        template: (data) => `<span class="font-mono text-sm">${data.terminalSerial}</span>`
    },
    shiftNum: {
        field: 'shiftNum',
        header: 'Смена',
        sortable: true,
        class: 'min-w-[80px]',
        template: (data) => data.shiftNum
    },
    ern: {
        field: 'ern',
        header: 'ЕРН',
        sortable: true,
        class: 'min-w-[80px]',
        template: (data) => data.ern
    },
    maskedPan: {
        field: 'maskedPan',
        header: 'Номер карты',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => data.maskedPan ? `
            <div>
                <div class="font-mono text-sm">${data.maskedPan}</div>
                <div class="text-xs text-gray-600">${data.paySystem}</div>
            </div>
        ` : '<span class="text-gray-400">-</span>'
    },
    cardUid: {
        field: 'cardUid',
        header: 'UID ТК',
        sortable: true,
        class: 'min-w-[120px]',
        template: (data) => `<span class="font-mono text-sm">${data.cardUid || '-'}</span>`
    },
    trxId: {
        field: 'trxId',
        header: 'Идентификатор операции',
        sortable: true,
        class: 'min-w-[150px]',
        template: (data) => `<span class="font-mono text-sm">${data.trxId || '-'}</span>`
    }
};

// Функция для получения заголовков колонок
const getColumnHeaders = () => {
    const headers = {
        ticketId: 'ID билета',
        createdAt: 'Дата проведения операции',
        ticketSeries: 'Серия билета',
        ticketNumber: 'Номер билета',
        orgName: 'Организация',
        routeName: 'Маршрут',
        routeNumber: 'Номер маршрута',
        amount: 'Сумма',
        payMethod: 'Способ оплаты',
        driverFio: 'Водитель',
        conductorFio: 'Кондуктор',
        vhNumber: 'Транспорт',
        stantionFrom: 'Станция входа',
        stantionTo: 'Станция выхода',
        tariffName: 'Тариф',
        productName: 'Продукт',
        terminalSerial: 'Терминал',
        shiftNum: 'Смена',
        ern: 'ЕРН',
        maskedPan: 'Номер карты',
        cardUid: 'UID ТК',
        trxId: 'Идентификатор операции',
        recordAt: 'Дата загрузки на сервер'
    };

    return orderedColumns.value.map(columnKey => headers[columnKey]);
};

// Функция для форматирования данных для экспорта
const formatDataForExport = (payment) => {
    console.log('formatDataForExport вызван для payment:', payment);
    console.log('orderedColumns.value:', orderedColumns.value);

    const formattedData = {};

    orderedColumns.value.forEach(columnKey => {
        console.log(`Обрабатываем колонку: ${columnKey}`);

        switch (columnKey) {
            case 'ticketId':
                formattedData[columnKey] = payment.ticketId;
                break;
            case 'createdAt':
                formattedData[columnKey] = formatDateTime(payment.createdAt);
                break;
            case 'ticketSeries':
                formattedData[columnKey] = payment.ticketSeries || '-';
                break;
            case 'ticketNumber':
                formattedData[columnKey] = payment.ticketNumber || '-';
                break;
            case 'orgName':
                formattedData[columnKey] = payment.orgName;
                break;
            case 'routeName':
                formattedData[columnKey] = payment.routeName;
                break;
            case 'routeNumber':
                formattedData[columnKey] = payment.routeNumber;
                break;
            case 'amount':
                formattedData[columnKey] = formatCurrency(payment.amount);
                break;
            case 'payMethod':
                formattedData[columnKey] = getPaymentMethodLabel(payment.payMethod);
                break;
            case 'driverFio':
                formattedData[columnKey] = payment.driverFio || '-';
                break;
            case 'conductorFio':
                formattedData[columnKey] = payment.conductorFio || '-';
                break;
            case 'vhNumber':
                formattedData[columnKey] = `${payment.vhNumber} (${payment.vhType})`;
                break;
            case 'stantionFrom':
                formattedData[columnKey] = payment.stantionFrom || '-';
                break;
            case 'stantionTo':
                formattedData[columnKey] = payment.stantionTo || '-';
                break;
            case 'tariffName':
                formattedData[columnKey] = payment.tariffName || '-';
                break;
            case 'productName':
                formattedData[columnKey] = payment.productName || '-';
                break;
            case 'terminalSerial':
                formattedData[columnKey] = payment.terminalSerial;
                break;
            case 'shiftNum':
                formattedData[columnKey] = payment.shiftNum;
                break;
            case 'ern':
                formattedData[columnKey] = payment.ern;
                break;
            case 'maskedPan':
                formattedData[columnKey] = payment.maskedPan ? `${payment.maskedPan} (${payment.paySystem})` : '-';
                break;
            case 'cardUid':
                formattedData[columnKey] = payment.cardUid || '-';
                break;
            case 'trxId':
                formattedData[columnKey] = payment.trxId || '-';
                break;
            case 'recordAt':
                formattedData[columnKey] = formatDateTime(payment.recordAt);
                break;
            default:
                console.warn(`Неизвестная колонка: ${columnKey}`);
                formattedData[columnKey] = payment[columnKey] || '-';
        }
    });

    console.log('formattedData результат:', formattedData);
    return formattedData;
};

// Функция для получения всех данных для экспорта
const getAllDataForExport = async () => {
    const allData = [];
    let currentPage = 0;
    const pageSize = 100; // Увеличиваем размер страницы для экспорта

    try {
        console.log('=== getAllDataForExport начат ===');
        console.log('projectId:', projectId);
        console.log('filters.value:', filters.value);
        console.log('pagination.sortField:', pagination.sortField);

        // Получаем общее количество страниц
        const firstPageResponse = await PaymentService.getPayments(projectId, {
            page: 0,
            limit: pageSize,
            sortField: pagination.sortField,
            ...filters.value
        });

        console.log('firstPageResponse:', firstPageResponse);

        // Проверяем, есть ли данные в ответе
        if (!firstPageResponse || !firstPageResponse.payments || !firstPageResponse.pagination) {
            console.error('firstPageResponse не содержит ожидаемые поля:', firstPageResponse);
            throw new Error('Ошибка при получении данных');
        }

        const totalCount = firstPageResponse.pagination.totalCount;
        exportTotalPages.value = Math.ceil(totalCount / pageSize);

        console.log('totalCount:', totalCount);
        console.log('exportTotalPages.value:', exportTotalPages.value);
        console.log('firstPageResponse.payments:', firstPageResponse.payments);

        // Добавляем данные первой страницы
        allData.push(...firstPageResponse.payments.map(formatDataForExport));
        currentPage = 1;
        exportCurrentPage.value = currentPage;
        exportProgress.value = Math.round((currentPage / exportTotalPages.value) * 100);

        console.log('Данные первой страницы добавлены, allData.length:', allData.length);

        // Получаем остальные страницы
        while (currentPage < exportTotalPages.value) {
            // Проверяем, не была ли отменена операция
            if (exportAbortController.value?.signal.aborted) {
                throw new Error('Экспорт отменен');
            }

            console.log(`Загружаем страницу ${currentPage} из ${exportTotalPages.value}`);

            const response = await PaymentService.getPayments(projectId, {
                page: currentPage,
                limit: pageSize,
                sortField: pagination.sortField,
                ...filters.value
            });

            console.log(`Страница ${currentPage} response:`, response);

            if (!response || !response.payments) {
                console.error(`Страница ${currentPage} не содержит payments:`, response);
                throw new Error('Ошибка при получении данных');
            }

            allData.push(...response.payments.map(formatDataForExport));
            currentPage++;
            exportCurrentPage.value = currentPage;
            exportProgress.value = Math.round((currentPage / exportTotalPages.value) * 100);

            console.log(`Страница ${currentPage-1} добавлена, allData.length:`, allData.length);
        }

        console.log('=== getAllDataForExport завершен успешно ===');
        console.log('Итоговый allData.length:', allData.length);

        return allData;
    } catch (error) {
        console.error('=== getAllDataForExport ошибка ===');
        console.error('error:', error);
        console.error('error.message:', error.message);
        console.error('error.stack:', error.stack);

        if (error.message === 'Экспорт отменен') {
            throw error;
        }
        throw new Error(`Ошибка при получении данных: ${error.message}`);
    }
};

// Функция экспорта в Excel
const exportToExcel = async () => {
    try {
        isExporting.value = true;
        exportProgress.value = 0;
        exportCurrentPage.value = 0;
        exportAbortController.value = new AbortController();

        const data = await getAllDataForExport();

        // Создаем рабочую книгу
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(data);

        // Добавляем заголовки
        const headers = getColumnHeaders();
        XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'A1' });

        // Добавляем лист в книгу
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Платежи');

        // Генерируем файл
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // Скачиваем файл
        const fileName = `payments_${new Date().toISOString().split('T')[0]}.xlsx`;
        downloadFile(blob, fileName);

    } catch (error) {
        if (error.message !== 'Экспорт отменен') {
            console.error('Ошибка экспорта в Excel:', error);
            // Здесь можно добавить уведомление об ошибке
        }
    } finally {
        isExporting.value = false;
        exportProgress.value = 0;
        exportCurrentPage.value = 0;
        exportAbortController.value = null;
    }
};

// Функция экспорта в CSV
const exportToCSV = async () => {
    try {
        isExporting.value = true;
        exportProgress.value = 0;
        exportCurrentPage.value = 0;
        exportAbortController.value = new AbortController();

        const data = await getAllDataForExport();

        // Создаем CSV контент
        const headers = getColumnHeaders();
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                Object.values(row).map(value =>
                    typeof value === 'string' && value.includes(',') ? `"${value}"` : value
                ).join(',')
            )
        ].join('\n');

        // Создаем blob и скачиваем
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const fileName = `payments_${new Date().toISOString().split('T')[0]}.csv`;
        downloadFile(blob, fileName);

    } catch (error) {
        if (error.message !== 'Экспорт отменен') {
            console.error('Ошибка экспорта в CSV:', error);
            // Здесь можно добавить уведомление об ошибке
        }
    } finally {
        isExporting.value = false;
        exportProgress.value = 0;
        exportCurrentPage.value = 0;
        exportAbortController.value = null;
    }
};

// Функция отмены экспорта
const cancelExport = () => {
    if (exportAbortController.value) {
        exportAbortController.value.abort();
    }
};
</script>

<style scoped>
.p-datatable .p-datatable-thead > tr > th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
}

.p-datatable .p-datatable-tbody > tr:hover {
    background-color: #f1f5f9;
}

.p-column-filter {
    width: 100%;
}
</style>
