<template>
    <Dialog
        v-model:visible="visible"
        header="Создать новый тариф"
        modal
        :style="{ width: '40rem' }"
    >
        <div class="p-fluid">
            <div class="mb-4">
                <label class="block text-900 font-medium mb-2">Наименование тарифа *</label>
                <InputText
                    v-model="tariffForm.name"
                    placeholder="Введите наименование тарифа"
                    class="w-full"
                    :class="{ 'p-invalid': submitted && !tariffForm.name }"
                />
                <small class="p-error" v-if="submitted && !tariffForm.name">
                    Наименование тарифа обязательно для заполнения.
                </small>
            </div>

            <div class="mb-4">
                <label class="block text-900 font-medium mb-2">Тэги</label>
                <InputText
                    v-model="tariffForm.tags"
                    placeholder="Введите тэги через запятую"
                    class="w-full"
                />
                <small class="text-color-secondary">Например: basic, default, premium</small>
            </div>

            <div class="mb-4">
                <label class="block text-900 font-medium mb-2">Статус</label>
                <Dropdown
                    v-model="tariffForm.status"
                    :options="statusOptions"
                    option-label="label"
                    option-value="value"
                    placeholder="Выберите статус"
                    class="w-full"
                />
            </div>
        </div>

        <template #footer>
            <Button
                label="Отмена"
                icon="pi pi-times"
                @click="closeDialog"
                class="p-button-text"
            />
            <Button
                label="Создать"
                icon="pi pi-check"
                @click="createTariff"
                :loading="saving"
                :disabled="!tariffForm.name"
            />
        </template>
    </Dialog>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {useToast} from 'primevue/usetoast';
import {TariffService} from '@/service/TariffService';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';

const toast = useToast();

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    projectId: {
        type: String,
        default: null
    }
});

// Emits
const emit = defineEmits(['update:visible', 'saved']);

// Реактивные данные
const saving = ref(false);
const submitted = ref(false);
const tariffForm = ref({
    name: '',
    tags: '',
    status: 'ACTIVE'
});

// Опции статусов
const statusOptions = [
    { label: 'Активный', value: 'ACTIVE' },
    { label: 'Отключен', value: 'DISABLED' },
    { label: 'Заблокирован', value: 'BLOCKED' }
];

// Computed
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// Методы
const createTariff = async () => {
    submitted.value = true;

    if (!tariffForm.value.name) {
        return;
    }

    if (!props.projectId) {
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'ID проекта не указан',
            life: 3000
        });
        return;
    }

    try {
        saving.value = true;

        // Создаем новый тариф через API
        const tariffData = {
            projectId: props.projectId,
            name: tariffForm.value.name,
            status: tariffForm.value.status,
            tags: tariffForm.value.tags
        };

        await TariffService.createTariff(tariffData);

        toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Тариф создан',
            life: 3000
        });

        emit('saved');
        closeDialog();
    } catch (error) {
        console.error('Ошибка создания тарифа:', error);
        toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось создать тариф: ' + (error.message || 'Неизвестная ошибка'),
            life: 3000
        });
    } finally {
        saving.value = false;
    }
};

const closeDialog = () => {
    visible.value = false;
    resetForm();
    emit('update:visible', false);
};

const resetForm = () => {
    tariffForm.value = {
        name: '',
        tags: '',
        status: 'ACTIVE'
    };
    submitted.value = false;
};

// Watchers
watch(() => props.visible, (newValue) => {
    if (newValue) {
        resetForm();
    }
});
</script>

<style scoped>
.p-error {
    color: var(--red-500);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.text-color-secondary {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
