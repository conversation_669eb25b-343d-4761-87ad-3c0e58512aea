<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ProjectService } from '@/service/ProjectService';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    selectedContract: {
        type: Object,
        default: null
    }
});

const emit = defineEmits(['update:visible', 'project-created']);

const router = useRouter();

// Состояние формы
const projectData = ref({
    name: '',
    description: '',
    region: '',
    // Поля из договора (только для чтения)
    number: '',
    startDate: '',
    endDate: ''
});

const loading = ref(false);
const errors = ref({});

// Вычисляемые свойства
const isVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

const isFormValid = computed(() => {
    return projectData.value.name.trim() &&
           !Object.keys(errors.value).length;
});

// Методы валидации
const validateField = (field, value) => {
    switch (field) {
        case 'name':
            if (!value.trim()) {
                errors.value.name = 'Название проекта обязательно';
            } else if (value.length < 3) {
                errors.value.name = 'Название должно содержать минимум 3 символа';
            } else {
                delete errors.value.name;
            }
            break;
        case 'region':
            if (!value.trim()) {
                errors.value.region = 'Регион обязателен';
            } else {
                delete errors.value.region;
            }
            break;
        case 'description':
            if (value && value.length > 500) {
                errors.value.description = 'Описание не должно превышать 500 символов';
            } else {
                delete errors.value.description;
            }
            break;
    }
};

// Обработчики событий
const onFieldChange = (field, value) => {
    projectData.value[field] = value;
    validateField(field, value);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU');
};

const createProject = async () => {
    try {
        loading.value = true;

        // Финальная валидация
        validateField('name', projectData.value.name);
        validateField('description', projectData.value.description);

        if (!isFormValid.value) {
            return;
        }

        // Подготавливаем данные для отправки
        const projectPayload = {
            name: projectData.value.name.trim(),
            description: projectData.value.description.trim(),
            region: projectData.value.region.trim(),
            contractId: props.selectedContract.id,
            startDate: new Date(props.selectedContract.startDate),
            endDate: new Date(props.selectedContract.endDate)
        };

        console.log('Создание проекта:', projectPayload);

        // Вызываем API создания проекта
        const createdProject = await ProjectService.createProject(projectPayload);

        console.log('Проект создан:', createdProject);

        // Уведомляем родительский компонент
        emit('project-created', createdProject);

        // Закрываем модальное окно
        closeModal();

        // Переходим к созданному проекту
        if (createdProject.id) {
            router.push(`/pro/${createdProject.id}`);
        }

    } catch (error) {
        console.error('Ошибка создания проекта:', error);
        // Можно добавить toast уведомление об ошибке
    } finally {
        loading.value = false;
    }
};

const closeModal = () => {
    isVisible.value = false;
    // Сбрасываем форму
    projectData.value = {
        name: '',
        description: '',
        region: '',
        number: '',
        contractName: '',
        startDate: '',
        endDate: ''
    };
    errors.value = {};
};

// Заполняем данные из выбранного договора
const fillContractData = () => {
    if (props.selectedContract) {
        projectData.value.number = props.selectedContract.number || '';
        projectData.value.startDate = props.selectedContract.startDate || '';
        projectData.value.endDate = props.selectedContract.endDate || '';

        // Предзаполняем название проекта на основе договора
        if (props.selectedContract.projectName) {
            projectData.value.name = props.selectedContract.projectName;
        }
    }
};

// Следим за изменением выбранного договора
onMounted(() => {
    fillContractData();
});

// Следим за изменениями props.selectedContract
watch(() => props.selectedContract, () => {
    fillContractData();
}, { immediate: true });
</script>

<template>
    <Dialog
        v-model:visible="isVisible"
        modal
        header="Создание нового проекта"
        :style="{ width: '70vw', maxWidth: '800px' }"
        :closable="true"
    >
        <div class="project-creation-form">
            <!-- Информация о выбранном договоре -->
            <div v-if="selectedContract" class="contract-info mb-4 p-3 border-1 border-300 border-round">
                <h4 class="text-lg font-semibold mb-3 text-primary">
                    <i class="pi pi-file-text mr-2"></i>
                    Выбранный договор
                </h4>
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <label class="block text-sm font-medium mb-1">Номер договора</label>
                        <InputText
                            :value="projectData.number"
                            readonly
                            class="w-full"
                            disabled
                        />
                    </div>
                    <div class="col-12 md:col-6">
                        <label class="block text-sm font-medium mb-1">Дата начала</label>
                        <InputText
                            :value="formatDate(projectData.startDate)"
                            readonly
                            class="w-full"
                            disabled
                        />
                    </div>
                    <div class="col-12 md:col-6">
                        <label class="block text-sm font-medium mb-1">Дата окончания</label>
                        <InputText
                            :value="formatDate(projectData.endDate)"
                            readonly
                            class="w-full"
                            disabled
                        />
                    </div>
                </div>
            </div>

            <!-- Форма данных проекта -->
            <div class="project-form">
                <h4 class="text-lg font-semibold mb-3">
                    <i class="pi pi-cog mr-2"></i>
                    Данные проекта
                </h4>

                <div class="grid">
                    <!-- Название проекта -->
                    <div class="col-12">
                        <label class="block text-sm font-medium mb-1">
                            Название проекта <span class="text-red-500">*</span>
                        </label>
                        <InputText
                            :value="projectData.name"
                            @input="onFieldChange('name', $event.target.value)"
                            placeholder="Введите название проекта"
                            class="w-full"
                            :class="{ 'p-invalid': errors.name }"
                        />
                        <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                    </div>

                    <!-- Описание -->
                    <div class="col-12">
                        <label class="block text-sm font-medium mb-1">Описание</label>
                        <Textarea
                            :value="projectData.description"
                            @input="onFieldChange('description', $event.target.value)"
                            placeholder="Введите описание проекта (необязательно)"
                            class="w-full"
                            :class="{ 'p-invalid': errors.description }"
                            rows="3"
                            :maxlength="500"
                        />
                        <small v-if="errors.description" class="p-error">{{ errors.description }}</small>
                        <small v-else class="text-color-secondary">
                            {{ projectData.description.length }}/500 символов
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-content-end gap-2">
                <Button
                    label="Отмена"
                    icon="pi pi-times"
                    text
                    @click="closeModal"
                    :disabled="loading"
                />
                <Button
                    label="Создать проект"
                    icon="pi pi-plus"
                    :disabled="!isFormValid || loading"
                    :loading="loading"
                    @click="createProject"
                />
            </div>
        </template>
    </Dialog>
</template>

<style scoped lang="scss">
.project-creation-form {
    min-height: 400px;
}

.contract-info {
    background: var(--surface-50);
}

.project-form {
    .grid {
        margin: 0;
    }
}

:deep(.p-inputtext:disabled) {
    background: var(--surface-100);
    color: var(--text-color-secondary);
}
</style>
