import {tmsApiClient} from './ApiClient.js';

export const TmsApiService = {
    // Получить список терминалов
    async getTerminals(params = {}) {
        try {
            const queryParams = new URLSearchParams();

            if (params.search) queryParams.append('search', params.search);
            if (params.projectId) queryParams.append('projectId', params.projectId);
            if (params.organizationId) queryParams.append('organizationId', params.organizationId);
            if (params.status !== undefined) queryParams.append('status', params.status);
            if (params.page !== undefined) queryParams.append('page', params.page);
            if (params.size !== undefined) queryParams.append('size', params.size);

            const endpoint = `/api/tms/v1/terminals?${queryParams.toString()}`;
            return await tmsApiClient.get(endpoint);
        } catch (error) {
            console.error('Ошибка при получении терминалов:', error);
            throw error;
        }
    },

    // Получить терминал по ID
    async getTerminalById(id) {
        try {
            const endpoint = `/api/tms/v1/terminals/${id}`;
            return await tmsApiClient.get(endpoint);
        } catch (error) {
            console.error('Ошибка при получении терминала:', error);
            if (error.message.includes('404')) {
                return null;
            }
            throw error;
        }
    },

    // Создать новый терминал
    async createTerminal(terminalData) {
        try {
            const endpoint = `/api/tms/v1/terminals`;
            return await tmsApiClient.post(endpoint, terminalData);
        } catch (error) {
            console.error('Ошибка при создании терминала:', error);
            throw error;
        }
    },

    // Обновить терминал
    async updateTerminal(id, terminalData) {
        try {
            const endpoint = `/api/tms/v1/terminals/${id}`;
            return await tmsApiClient.put(endpoint, terminalData);
        } catch (error) {
            console.error('Ошибка при обновлении терминала:', error);
            if (error.message.includes('404')) {
                throw new Error('Терминал не найден');
            }
            throw error;
        }
    },

    // Удалить терминал
    async deleteTerminal(id) {
        try {
            const endpoint = `/api/tms/v1/terminals/${id}`;
            await tmsApiClient.delete(endpoint);
            return { success: true };
        } catch (error) {
            console.error('Ошибка при удалении терминала:', error);
            if (error.message.includes('404')) {
                throw new Error('Терминал не найден');
            }
            throw error;
        }
    }
};
