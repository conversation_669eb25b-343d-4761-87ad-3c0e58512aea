import {pasivApiClient} from "@/service/ApiClient";

export class ContractService {
    // {
    //     id: '550e8400-e29b-41d4-a716-446655440001',
    //         // Основные атрибуты договора
    //         projectCode: 'MSK-001',
    //     projectName: 'СберТройка ПРО Москва',
    //     projectType: 'transport_system',
    //     contractType: 'system_rules',
    //     contractName: 'Правила системы СберТройка ПРО г. Москва',
    //     contractNumber: 'ПС-СТ-2024-001',
    //     signatureDate: '2024-01-15T00:00:00Z',
    //     conclusionDate: '2024-01-01T00:00:00Z',
    //     completionDate: '2024-12-31T23:59:59Z',
    //     status: 'active',
    //     externalId1C: 'DOC_000001_2024',
    //
    //     // Поля для выпадающего списка
    //     number: 'ПС-СТ-2024-001',
    //     organizationName: 'ПАО Сбербанк',
    //     amount: 25000000,
    //     validTo: '2024-12-31T23:59:59Z',
    //     displayName: 'ПС-СТ-2024-001 - ПАО Сбербанк',
    //
    //     // Организации в договоре с ролями
    //     contractOrganizations: [
    //     {
    //         organizationId: 1,
    //         organizationName: 'ПАО Сбербанк',
    //         role: 'operator',
    //         roleDescription: 'Оператор - организатор ТКП СТ'
    //     },
    //     {
    //         organizationId: 2,
    //         organizationName: 'ООО "Городской транспорт"',
    //         role: 'carrier',
    //         roleDescription: 'Перевозчик'
    //     },
    //     {
    //         organizationId: 3,
    //         organizationName: 'ООО "СберТех"',
    //         role: 'processing_center',
    //         roleDescription: 'Процессинговый центр'
    //     }
    // ],
    //
    //     // Системная информация
    //     syncStatus: 'synced',
    //     lastSyncDate: '2024-01-20T10:30:00Z',
    //     createdDate: '2023-12-15T09:00:00Z'
    // }
    static async getContracts() {
        try {
            const response = await pasivApiClient.get(`/api/pasiv/v1/contracts`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении организаций:', error);
            throw error;
        }
    }

    /**
     * Получение договоров для создания ПРО проекта
     * Временно делает то же самое что и getContracts()
     */
    static async getContractForPROProjectCreation() {
        try {
            const response = await pasivApiClient.get(`/api/pasiv/v1/contracts`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении договоров для создания проекта:', error);
            throw error;
        }
    }

    // getContractsByProject(projectCode) {
    //     return Promise.resolve(this.getData());
    // }

    // getContractById(contractId) {
    //     const contracts = this.getData();
    //     const contract = contracts.find(c => c.id == contractId);
    //     return Promise.resolve(contract);
    // }

    // createContract(projectCode, contractData) {
    //     console.log('Creating contract for project:', projectCode, contractData);
    //
    //     const newContract = {
    //         ...contractData,
    //         id: Date.now(),
    //         status: 'draft',
    //         syncStatus: 'never',
    //         lastSyncDate: null,
    //         createdDate: new Date().toISOString()
    //     };
    //
    //     return Promise.resolve(newContract);
    // },
    //
    // updateContract(contractId, contractData) {
    //     console.log('Updating contract:', contractId, contractData);
    //
    //     const updatedContract = {
    //         ...contractData,
    //         id: contractId
    //     };
    //
    //     return Promise.resolve(updatedContract);
    // },
    //
    // deleteContract(contractId) {
    //     console.log('Deleting contract:', contractId);
    //     return Promise.resolve({ success: true });
    // },
    //
    // syncContract(contractId) {
    //     console.log('Syncing contract with 1C:', contractId);
    //
    //     return new Promise((resolve) => {
    //         setTimeout(() => {
    //             const success = Math.random() > 0.2;
    //             resolve({
    //                 success,
    //                 message: success
    //                     ? 'Синхронизация договора с 1С выполнена успешно'
    //                     : 'Ошибка синхронизации с 1С: договор не найден в системе',
    //                 syncDate: new Date().toISOString()
    //             });
    //         }, 2000);
    //     });
    // },
    //
    // syncAllContracts() {
    //     console.log('Syncing all contracts with 1C');
    //
    //     return new Promise((resolve) => {
    //         setTimeout(() => {
    //             const totalCount = this.getData().length;
    //             const successCount = Math.floor(totalCount * 0.75);
    //             const errorCount = totalCount - successCount;
    //
    //             resolve({
    //                 success: true,
    //                 totalCount,
    //                 successCount,
    //                 errorCount,
    //                 message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
    //                 syncDate: new Date().toISOString()
    //             });
    //         }, 4000);
    //     });
    // }
}
