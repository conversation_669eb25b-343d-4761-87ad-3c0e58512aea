import {proApiClient} from './ApiClient';
import {OrganizationService} from './OrganizationService.js';
import {RouteService} from './RouteService.js';

export class PaymentService {
    static async getPayments(projectId, filters = {}, pagination = {}) {
        try {
            const params = new URLSearchParams();

            // Добавляем projectId как обязательный параметр
            params.append('projectId', projectId);

            // Добавляем фильтры
            if (filters.createdAtFrom) {
                params.append('createdAtFrom', filters.createdAtFrom);
            }
            if (filters.createdAtTo) {
                params.append('createdAtTo', filters.createdAtTo);
            }
            if (filters.organizationId) {
                params.append('organizationId', filters.organizationId);
            }
            if (filters.routeId) {
                params.append('routeId', filters.routeId);
            }
            if (filters.ticketId) {
                params.append('ticketId', filters.ticketId);
            }
            if (filters.ticketSeries) {
                params.append('ticketSeries', filters.ticketSeries);
            }
            if (filters.ticketNumber) {
                params.append('ticketNumber', filters.ticketNumber);
            }
            if (filters.payMethod) {
                params.append('payMethod', filters.payMethod);
            }
            if (filters.trxId) {
                params.append('trxId', filters.trxId);
            }
            if (filters.terminalSerial) {
                params.append('terminalSerial', filters.terminalSerial);
            }

            // Добавляем параметры пагинации
            if (pagination.page !== undefined) {
                params.append('page', pagination.page);
            }
            if (pagination.limit) {
                params.append('limit', pagination.limit);
            }
            if (pagination.sortField) {
                params.append('sortField', pagination.sortField);
            }
            if (pagination.sortOrder) {
                params.append('sortOrder', pagination.sortOrder);
            }

            const response = await proApiClient.get(`/api/pro/v1/payments?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении платежей:', error);
            throw error;
        }
    }

    static async getPaymentExamples(projectId) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/payments/examples?projectId=${projectId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении примеров платежей:', error);
            throw error;
        }
    }

    static async getOrganizations(projectId) {
        try {
            // Используем готовый OrganizationService вместо API
            const organizations = OrganizationService.getData();
            return organizations;
        } catch (error) {
            console.error('Ошибка при получении организаций:', error);
            throw error;
        }
    }

    static async getRoutes(projectId) {
        try {
            // Используем готовый RouteService вместо API
            const routes = await RouteService.getRoutesByProject(projectId);
            return routes;
        } catch (error) {
            console.error('Ошибка при получении маршрутов:', error);
            throw error;
        }
    }

    static async getPaymentMethods() {
        try {
            const response = await proApiClient.get('/api/pro/v1/payment-methods');
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении способов оплаты:', error);
            throw error;
        }
    }

    static async getPaymentStats(projectId) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/payments/stats?projectId=${projectId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении статистики платежей:', error);
            throw error;
        }
    }

    static async getPaymentMethodStats(projectId) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/payments/stats/methods?projectId=${projectId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении статистики по способам оплаты:', error);
            throw error;
        }
    }

    static async getPaymentPeriodStats(projectId) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/payments/stats/periods?projectId=${projectId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении статистики по периодам:', error);
            throw error;
        }
    }
}
