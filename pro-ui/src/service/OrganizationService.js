import {pasivApiClient} from './ApiClient.js';

export class OrganizationService {
    // {
    //     id: '02225417-810e-49a6-bcbc-919fa643bc84',
    //     name: 'ООО "Грузоперевозки Север"',
    //     shortName: 'Грузоперевозки Север',
    //     inn: '7708901234',
    //     kpp: '770801001',
    //     ogrn: '1027700890123',
    //     address: 'г. Москва, Северный бульвар, д. 33',
    //     phone: '+7 (495) 890-12-34',
    //     email: '<EMAIL>',
    //     director: 'Новиков Алексей Викторович',
    //     status: 'active',
    //     syncStatus: 'error',
    //     lastSyncDate: '2024-01-08T11:15:00Z',
    //     createdDate: '2023-08-20T17:45:00Z'
    // }
    static async getOrganizations(page = 0, size = 20, name = null) {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                size: size.toString()
            });
            
            if (name) {
                params.append('name', name);
            }
            
            const response = await pasivApiClient.get(`/api/pasiv/v1/organizations?${params.toString()}`);
            
            // Проверяем структуру ответа и возвращаем данные
            if (response.data && response.data.data && Array.isArray(response.data.data)) {
                return response.data.data; // Возвращаем массив организаций из data
            } else if (response.data && Array.isArray(response.data)) {
                return response.data; // Возвращаем массив организаций напрямую
            } else {
                console.warn('Неожиданная структура ответа API организаций:', response.data);
                return [];
            }
        } catch (error) {
            console.error('Ошибка при получении организаций:', error);
            throw error;
        }
    }

    static async searchOrganizationsByName(name, page = 0, size = 20) {
        return this.getOrganizations(page, size, name);
    }

    static async getOrganizationById(id) {
        try {
            const response = await pasivApiClient.get(`/api/pasiv/v1/organizations`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении организаций:', error);
            throw error;
        }
    }

    // createOrganization(organizationData) {
    //     console.log('Creating organization:', organizationData);
    //
    //     const newOrganization = {
    //         ...organizationData,
    //         id: Date.now(),
    //         status: 'active',
    //         syncStatus: 'never',
    //         lastSyncDate: null,
    //         createdDate: new Date().toISOString()
    //     };
    //
    //     return Promise.resolve(newOrganization);
    // }
    //
    // updateOrganization(organizationId, organizationData) {
    //     console.log('Updating organization:', organizationId, organizationData);
    //
    //     const updatedOrganization = {
    //         ...organizationData,
    //         id: organizationId
    //     };
    //
    //     return Promise.resolve(updatedOrganization);
    // }
    //
    // deleteOrganization(organizationId) {
    //     console.log('Deleting organization:', organizationId);
    //     return Promise.resolve({ success: true });
    // }
    //
    // syncOrganization(organizationId) {
    //     console.log('Syncing organization with 1C:', organizationId);
    //
    //     // Имитация синхронизации
    //     return new Promise((resolve) => {
    //         setTimeout(() => {
    //             const success = Math.random() > 0.2; // 80% успеха
    //             resolve({
    //                 success,
    //                 message: success
    //                     ? 'Синхронизация с 1С выполнена успешно'
    //                     : 'Ошибка синхронизации с 1С: таймаут соединения',
    //                 syncDate: new Date().toISOString()
    //             });
    //         }, 2000); // Имитация задержки
    //     });
    // }
    //
    // syncAllOrganizations() {
    //     console.log('Syncing all organizations with 1C');
    //
    //     // Имитация массовой синхронизации
    //     return new Promise((resolve) => {
    //         setTimeout(() => {
    //             const totalCount = this.getData().length;
    //             const successCount = Math.floor(totalCount * 0.8); // 80% успеха
    //             const errorCount = totalCount - successCount;
    //
    //             resolve({
    //                 success: true,
    //                 totalCount,
    //                 successCount,
    //                 errorCount,
    //                 message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
    //                 syncDate: new Date().toISOString()
    //             });
    //         }, 5000); // Имитация более длительной операции
    //     });
    // }
}
