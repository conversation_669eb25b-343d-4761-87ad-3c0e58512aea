import {sbolApiClient} from './ApiClient.js';

/**
 * Сервис для работы с доступностью услуг агентам (регион-проект)
 */
export class RegionProjectService {

    /**
     * Получить список связок регион-проект
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Array>} Список связок
     */
    static async getRegionProjects(filters = {}) {
        try {
            const params = {
                page: filters.page || 0,
                size: filters.size || 20
            };

            const response = await sbolApiClient.get('/api/abt/v1/sbol/region-projects', params);

            if (response.success) {
                return response.data.content || response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения связок регион-проект');
            }
        } catch (error) {
            console.error('Ошибка получения связок регион-проект:', error);
            throw error;
        }
    }

    /**
     * Получить связку по ID
     * @param {string} id - ID связки
     * @returns {Promise<Object>} Связка регион-проект
     */
    static async getRegionProject(id) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/region-projects/${id}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Связка регион-проект не найдена');
            }
        } catch (error) {
            console.error('Ошибка получения связки регион-проект:', error);
            throw error;
        }
    }

    /**
     * Создать новую связку регион-проект
     * @param {Object} regionProject - Данные связки
     * @returns {Promise<Object>} Созданная связка
     */
    static async createRegionProject(regionProject) {
        try {
            const response = await sbolApiClient.post('/api/abt/v1/sbol/region-projects', regionProject);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания связки регион-проект');
            }
        } catch (error) {
            console.error('Ошибка создания связки регион-проект:', error);
            throw error;
        }
    }

    /**
     * Обновить связку регион-проект
     * @param {string} id - ID связки
     * @param {Object} regionProject - Данные связки
     * @returns {Promise<Object>} Обновленная связка
     */
    static async updateRegionProject(id, regionProject) {
        try {
            const response = await sbolApiClient.put(`/api/abt/v1/sbol/region-projects/${id}`, regionProject);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления связки регион-проект');
            }
        } catch (error) {
            console.error('Ошибка обновления связки регион-проект:', error);
            throw error;
        }
    }

    /**
     * Удалить связку регион-проект
     * @param {string} id - ID связки
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteRegionProject(id) {
        try {
            const response = await sbolApiClient.delete(`/api/abt/v1/sbol/region-projects/${id}`);

            if (response.success) {
                return true;
            } else {
                throw new Error(response.message || 'Ошибка удаления связки регион-проект');
            }
        } catch (error) {
            console.error('Ошибка удаления связки регион-проект:', error);
            throw error;
        }
    }

    /**
     * Получить связки по региону
     * @param {string} regionId - ID региона
     * @returns {Promise<Array>} Список связок
     */
    static async getRegionProjectsByRegion(regionId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/region-projects/region/${regionId}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения связок по региону');
            }
        } catch (error) {
            console.error('Ошибка получения связок по региону:', error);
            throw error;
        }
    }

    /**
     * Получить связки по проекту
     * @param {string} projectId - ID проекта
     * @returns {Promise<Array>} Список связок
     */
    static async getRegionProjectsByProject(projectId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/region-projects/project/${projectId}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения связок по проекту');
            }
        } catch (error) {
            console.error('Ошибка получения связок по проекту:', error);
            throw error;
        }
    }

    /**
     * Получить связку по региону и проекту
     * @param {string} regionId - ID региона
     * @param {string} projectId - ID проекта
     * @returns {Promise<Object>} Связка регион-проект
     */
    static async getRegionProjectByRegionAndProject(regionId, projectId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/region-projects/region/${regionId}/project/${projectId}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Связка регион-проект не найдена');
            }
        } catch (error) {
            console.error('Ошибка получения связки по региону и проекту:', error);
            throw error;
        }
    }
}
