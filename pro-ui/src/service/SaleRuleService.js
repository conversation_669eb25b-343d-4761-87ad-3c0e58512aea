import {sbolApiClient} from './ApiClient.js';
import {ServiceService} from './ServiceService.js';

/**
 * Сервис для работы с правилами продаж
 */
export class SaleRuleService {

    /**
     * Получить список правил продаж
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Array>} Список правил продаж
     */
    static async getSaleRules(filters = {}) {
        try {
            const params = {
                page: filters.page || 0,
                size: filters.size || 20
            };

            const response = await sbolApiClient.get('/api/abt/v1/sbol/sale-rules', params);

            if (response.success) {
                return response.data.content || response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения правил продаж');
            }
        } catch (error) {
            console.error('Ошибка получения правил продаж:', error);
            throw error;
        }
    }

    /**
     * Получить правило продаж по ID
     * @param {string} id - ID правила
     * @returns {Promise<Object>} Правило продаж
     */
    static async getSaleRule(id) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/sale-rules/${id}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Правило продаж не найдено');
            }
        } catch (error) {
            console.error('Ошибка получения правила продаж:', error);
            throw error;
        }
    }

    /**
     * Создать новое правило продаж
     * @param {Object} rule - Данные правила
     * @returns {Promise<Object>} Созданное правило
     */
    static async createSaleRule(rule) {
        try {
            const response = await sbolApiClient.post('/api/abt/v1/sbol/sale-rules', rule);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания правила продаж');
            }
        } catch (error) {
            console.error('Ошибка создания правила продаж:', error);
            throw error;
        }
    }

    /**
     * Обновить правило продаж
     * @param {string} id - ID правила
     * @param {Object} rule - Данные правила
     * @returns {Promise<Object>} Обновленное правило
     */
    static async updateSaleRule(id, rule) {
        try {
            const response = await sbolApiClient.put(`/api/abt/v1/sbol/sale-rules/${id}`, rule);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления правила продаж');
            }
        } catch (error) {
            console.error('Ошибка обновления правила продаж:', error);
            throw error;
        }
    }

    /**
     * Удалить правило продаж
     * @param {string} id - ID правила
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteSaleRule(id) {
        try {
            const response = await sbolApiClient.delete(`/api/abt/v1/sbol/sale-rules/${id}`);

            if (response.success) {
                return true;
            } else {
                throw new Error(response.message || 'Ошибка удаления правила продаж');
            }
        } catch (error) {
            console.error('Ошибка удаления правила продаж:', error);
            throw error;
        }
    }

    /**
     * Получить правила продаж по услуге
     * @param {string} serviceId - ID услуги
     * @returns {Promise<Array>} Список правил
     */
    static async getSaleRulesByService(serviceId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/sale-rules/service/${serviceId}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения правил продаж по услуге');
            }
        } catch (error) {
            console.error('Ошибка получения правил продаж по услуге:', error);
            throw error;
        }
    }

    /**
     * Получить активные правила продаж по услуге
     * @param {string} serviceId - ID услуги
     * @returns {Promise<Array>} Список активных правил
     */
    static async getActiveSaleRulesByService(serviceId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/sale-rules/service/${serviceId}/active`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения активных правил продаж');
            }
        } catch (error) {
            console.error('Ошибка получения активных правил продаж:', error);
            throw error;
        }
    }

    /**
     * Получить правила продаж по типу
     * @param {string} ruleType - Тип правила
     * @returns {Promise<Array>} Список правил
     */
    static async getSaleRulesByType(ruleType) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/sale-rules/type/${ruleType}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения правил продаж по типу');
            }
        } catch (error) {
            console.error('Ошибка получения правил продаж по типу:', error);
            throw error;
        }
    }

    /**
     * Получить правила продаж по активности
     * @param {boolean} isActive - Статус активности
     * @returns {Promise<Array>} Список правил
     */
    static async getSaleRulesByActive(isActive) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/sale-rules/active/${isActive}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения правил продаж по активности');
            }
        } catch (error) {
            console.error('Ошибка получения правил продаж по активности:', error);
            throw error;
        }
    }

    /**
     * Получить типы правил
     * @returns {Promise<Array>} Список типов
     */
    static async getRuleTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Месячный диапазон', value: 'MONTHLY_RANGE' },
                    { label: 'Недельный диапазон', value: 'WEEKLY_RANGE' },
                    { label: 'Дневной диапазон', value: 'DAILY_RANGE' },
                    { label: 'Диапазон баланса', value: 'BALANCE_RANGE' }
                ]);
            }, 100);
        });
    }

    /**
     * Получить логики правил
     * @returns {Promise<Array>} Список логик
     */
    static async getRuleLogics() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'И (все правила должны выполняться)', value: 'AND' },
                    { label: 'ИЛИ (хотя бы одно правило должно выполняться)', value: 'OR' }
                ]);
            }, 100);
        });
    }

    /**
     * Получить список услуг для выбора
     * @returns {Promise<Array>} Список услуг
     */
    static async getServices() {
        try {
            const services = await ServiceService.getServices();
            return services.map(service => ({
                label: service.name,
                value: service.id
            }));
        } catch (error) {
            console.error('Ошибка получения списка услуг:', error);
            return [];
        }
    }
}
