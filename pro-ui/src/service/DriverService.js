import {EmployeeService} from './EmployeeService.js';

export const DriverService = {
    getData: () => {
        return [
            {
                id: 1,
                personnelNumber: 'D001',
                fullName: 'Иван<PERSON> И<PERSON>',
                pinCode: '1234',
                cardPan: '1234567890123456',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                role: 'driver',
                login: 'ivanov.i',
                email: '<EMAIL>',
                phone: '+7 (999) 123-45-67'
            },
            {
                id: 2,
                personnelNumber: 'D002',
                fullName: 'Петров Петр Петрович',
                pinCode: '5678',
                cardPan: '2345678901234567',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                role: 'driver',
                login: 'petrov.p',
                email: '<EMAIL>',
                phone: '+7 (999) 234-56-78'
            },
            {
                id: 3,
                personnelNumber: 'D003',
                fullName: 'Сидоров Сидор <PERSON>и<PERSON><PERSON><PERSON><PERSON>',
                pinCode: '9012',
                cardPan: '3456789012345678',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                role: 'admin',
                login: 'sidorov.s',
                email: '<EMAIL>',
                phone: '+7 (999) 345-67-89'
            },
            {
                id: 4,
                personnelNumber: 'D004',
                fullName: 'Козлов Андрей Владимирович',
                pinCode: '3456',
                cardPan: '****************',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"'
            },
            {
                id: 5,
                personnelNumber: 'D005',
                fullName: 'Морозов Дмитрий Александрович',
                pinCode: '7890',
                cardPan: '5678901234567890',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"'
            },
            {
                id: 6,
                personnelNumber: 'D006',
                fullName: 'Волков Сергей Николаевич',
                pinCode: '2468',
                cardPan: '6789012345678901',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"'
            },
            {
                id: 7,
                personnelNumber: 'D007',
                fullName: 'Лебедев Михаил Игоревич',
                pinCode: '1357',
                cardPan: '7890123456789012',
                organizationId: 7,
                organizationName: 'ИП Лебедев М.И.'
            },
            {
                id: 8,
                personnelNumber: 'D008',
                fullName: 'Новиков Алексей Викторович',
                pinCode: '9753',
                cardPan: '8901234567890123',
                organizationId: 8,
                organizationName: 'ООО "Грузоперевозки Север"'
            },
            {
                id: 9,
                personnelNumber: 'D009',
                fullName: 'Федоров Владимир Сергеевич',
                pinCode: '8642',
                cardPan: '9012345678901234',
                organizationId: 6,
                organizationName: 'ООО "Электротранс"'
            },
            {
                id: 10,
                personnelNumber: 'D010',
                fullName: 'Соколов Артем Дмитриевич',
                pinCode: '1593',
                cardPan: '0123456789012345',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"'
            },
            {
                id: 11,
                personnelNumber: 'D011',
                fullName: 'Павлов Максим Андреевич',
                pinCode: '7531',
                cardPan: '1357924680135792',
                organizationId: 5,
                organizationName: 'ООО "Такси Комфорт"'
            },
            {
                id: 12,
                personnelNumber: 'D012',
                fullName: 'Семенов Роман Олегович',
                pinCode: '9517',
                cardPan: '2468135790246813',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"'
            }
        ]
    },

    getDrivers() {
        return Promise.resolve(this.getData());
    },

    getDriversByProject(projectId, filters = {}, pagination = {}) {
        // Используем новый API для получения сотрудников
        return EmployeeService.getEmployeesByProject(projectId, filters, pagination);
    },

    getDriverById(driverId) {
        // Используем новый API для получения сотрудника
        return EmployeeService.getEmployeeById(driverId);
    },

    createDriver(projectId, driverData) {
        // Используем новый API для создания сотрудника
        return EmployeeService.createEmployee(projectId, driverData);
    },

    updateDriver(driverId, driverData) {
        // Используем новый API для обновления сотрудника
        return EmployeeService.updateEmployee(driverId, driverData);
    },

    deleteDriver(driverId) {
        // Используем новый API для удаления сотрудника
        return EmployeeService.deleteEmployee(driverId);
    }
}
