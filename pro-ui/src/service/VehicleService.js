import {proApiClient} from './ApiClient';

export const VehicleService = {
    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    async getVehicles(projectId, filters = {}, page = 0, size = 10) {
        const params = new URLSearchParams({
            projectId: projectId,
            page: page.toString(),
            size: size.toString()
        });

        if (filters.type) params.append('type', filters.type);
        if (filters.status) params.append('status', filters.status);
        if (filters.organizationId) params.append('organizationId', filters.organizationId);
        if (filters.search) params.append('search', filters.search);

        const response = await proApiClient.get(`/api/pro/v1/vehicles?${params.toString()}`);
        return response.data;
    },

    /**
     * Получить транспортное средство по ID
     */
    async getVehicleById(vehicleId) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/${vehicleId}`);
        return response.data;
    },

    /**
     * Получить все версии транспортного средства
     */
    async getVehicleVersions(vehicleId) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/${vehicleId}/versions`);
        return response.data;
    },

    /**
     * Создать новое транспортное средство
     */
    async createVehicle(vehicleData) {
        const response = await proApiClient.post(`/api/pro/v1/vehicles`, vehicleData);
        return response.data;
    },

    /**
     * Обновить транспортное средство
     */
    async updateVehicle(vehicleId, vehicleData) {
        const response = await proApiClient.put(`/api/pro/v1/vehicles/${vehicleId}`, vehicleData);
        return response.data;
    },

    /**
     * Удалить транспортное средство
     */
    async deleteVehicle(vehicleId) {
        const response = await proApiClient.delete(`/api/pro/v1/vehicles/${vehicleId}`);
        return response.data;
    },

    /**
     * Получить транспортные средства по проекту
     */
    async getVehiclesByProject(projectId) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/project/${projectId}`);
        return response.data;
    },

    /**
     * Получить транспортные средства по организации
     */
    async getVehiclesByOrganization(organizationId) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/organization/${organizationId}`);
        return response.data;
    },

    /**
     * Получить транспортные средства по типу
     */
    async getVehiclesByType(type) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/type/${type}`);
        return response.data;
    },

    /**
     * Получить транспортные средства по статусу
     */
    async getVehiclesByStatus(status) {
        const response = await proApiClient.get(`/api/pro/v1/vehicles/status/${status}`);
        return response.data;
    },

    /**
     * Получить опции для типов транспортных средств
     */
    getVehicleTypeOptions() {
        return [
            { label: 'Автобус', value: 'BUS' },
            { label: 'Трамвай', value: 'TRAM' },
            { label: 'Троллейбус', value: 'TROLLEYBUS' },
            { label: 'Метро', value: 'METRO' }
        ];
    },

    /**
     * Получить опции для статусов транспортных средств
     */
    getVehicleStatusOptions() {
        return [
            { label: 'Активное', value: 'ACTIVE' },
            { label: 'Отключено', value: 'DISABLED' },
            { label: 'Заблокировано', value: 'BLOCKED' },
            { label: 'Удалено', value: 'IS_DELETED' }
        ];
    },

    /**
     * Получить название типа транспортного средства
     */
    getVehicleTypeLabel(type) {
        const options = this.getVehicleTypeOptions();
        const option = options.find(opt => opt.value === type);
        return option ? option.label : type;
    },

    /**
     * Получить название статуса транспортного средства
     */
    getVehicleStatusLabel(status) {
        const options = this.getVehicleStatusOptions();
        const option = options.find(opt => opt.value === status);
        return option ? option.label : status;
    }
};
