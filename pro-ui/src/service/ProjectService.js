import {proApiClient} from './ApiClient.js';

export const ProjectService = {
    async getProjects() {
        try {
            const response = await proApiClient.get('/api/pro/v1/projects');
            if (response.success) {
                // API возвращает структуру { content: [...], pagination: {...} }
                const projects = response.data?.content || [];
                return projects;
            } else {
                throw new Error(response.message || 'Ошибка получения проектов');
            }
        } catch (error) {
            console.error('Ошибка получения проектов:', error);
            throw error;
        }
    },

    async getProjectById(id) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/projects/${id}`);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения проекта');
            }
        } catch (error) {
            console.error('Ошибка получения проекта:', error);
            throw error;
        }
    },

    async createProject(projectData) {
        console.log('Creating project:', projectData);

        try {
            // Преобразуем даты из ISO формата в формат даты (YYYY-MM-DD)
            const startDate = projectData.startDate ? projectData.startDate.toISOString().split('T')[0] : null;
            const endDate = projectData.endDate ? projectData.endDate.toISOString().split('T')[0] : null;

            // Создаем объект с полями для создания проекта
            const projectPayload = {
                name: projectData.name,
                description: projectData.description || '',
                region: projectData.region,
                contractId: projectData.contractId,
                contractNumber: projectData.contractNumber,
                startDate: startDate,
                endDate: endDate
            };

            const response = await proApiClient.post('/api/pro/v1/projects', projectPayload);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания проекта');
            }
        } catch (error) {
            console.error('Ошибка создания проекта:', error);
            throw error;
        }
    },

    async createProjectFromContract(contractId, projectData) {
        console.log('Creating project from contract:', contractId, projectData);

        try {
            // Преобразуем даты из ISO формата в формат даты (YYYY-MM-DD)
            const startDate = projectData.startDate ? projectData.startDate.split('T')[0] : null;
            const endDate = projectData.endDate ? projectData.endDate.split('T')[0] : null;

            // Создаем объект только с полями, которые есть в модели Project
            const projectPayload = {
                name: projectData.name,
                code: projectData.code,
                contractId: contractId,
                contractNumber: projectData.contractNumber,
                contractName: projectData.contractName,
                startDate: startDate,
                endDate: endDate,
                manager: projectData.manager,
                projectType: 'transport_system', // Всегда СберТройка ПРО
                status: 'DRAFT', // Правильный формат enum
                progress: 0,
                syncStatus: 'PENDING' // Правильный формат
                // Убираем routes, так как это @Transient поле
            };

            const response = await proApiClient.post('/api/pro/v1/projects', projectPayload);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания проекта');
            }
        } catch (error) {
            console.error('Ошибка создания проекта:', error);
            throw error;
        }
    },

    async updateProject(id, projectData) {
        console.log('Updating project:', id, projectData);

        try {
            // Преобразуем даты из ISO формата в формат даты (YYYY-MM-DD)
            const startDate = projectData.startDate ? projectData.startDate.split('T')[0] : null;
            const endDate = projectData.endDate ? projectData.endDate.split('T')[0] : null;

            // Создаем объект только с полями, которые есть в модели Project
            const projectPayload = {
                name: projectData.name,
                code: projectData.code,
                contractId: projectData.contractId,
                contractNumber: projectData.contractNumber,
                contractName: projectData.contractName,
                startDate: startDate,
                endDate: endDate,
                manager: projectData.manager,
                projectType: projectData.projectType,
                status: projectData.status === 'draft' ? 'DRAFT' :
                        projectData.status === 'active' ? 'ACTIVE' :
                        projectData.status === 'completed' ? 'COMPLETED' :
                        projectData.status,
                progress: projectData.progress,
                syncStatus: projectData.syncStatus === 'pending' ? 'PENDING' :
                           projectData.syncStatus === 'synced' ? 'SYNCED' :
                           projectData.syncStatus
                // Убираем routes, так как это @Transient поле
            };

            const response = await proApiClient.put(`/api/pro/v1/projects/${id}`, projectPayload);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления проекта');
            }
        } catch (error) {
            console.error('Ошибка обновления проекта:', error);
            throw error;
        }
    },

    async deleteProject(id) {
        console.log('Deleting project:', id);
        try {
            const response = await proApiClient.delete(`/api/pro/v1/projects/${id}`);
            if (response.success) {
                return { success: true };
            } else {
                throw new Error(response.message || 'Ошибка удаления проекта');
            }
        } catch (error) {
            console.error('Ошибка удаления проекта:', error);
            throw error;
        }
    },

    async syncProjectWithContract(projectId) {
        console.log('Syncing project with contract:', projectId);
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/sync`);
            if (response.success) {
                return {
                    success: true,
                    message: `Проект ${projectId} синхронизирован с договором`,
                    syncDate: new Date().toISOString()
                };
            } else {
                throw new Error(response.message || 'Ошибка синхронизации проекта');
            }
        } catch (error) {
            console.error('Ошибка синхронизации проекта:', error);
            throw error;
        }
    },

    async activateProject(id) {
        console.log('Activating project:', id);
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${id}/activate`);
            if (response.success) {
                return {
                    success: true,
                    message: `Проект ${id} активирован`,
                    activationDate: new Date().toISOString()
                };
            } else {
                throw new Error(response.message || 'Ошибка активации проекта');
            }
        } catch (error) {
            console.error('Ошибка активации проекта:', error);
            throw error;
        }
    },

    async completeProject(id) {
        console.log('Completing project:', id);
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${id}/complete`);
            if (response.success) {
                return {
                    success: true,
                    message: `Проект ${id} завершен`,
                    completionDate: new Date().toISOString()
                };
            } else {
                throw new Error(response.message || 'Ошибка завершения проекта');
            }
        } catch (error) {
            console.error('Ошибка завершения проекта:', error);
            throw error;
        }
    },

    async getProjectOrganizations(projectId) {
        try {
            // Сначала получаем данные проекта, чтобы узнать contractId
            const project = await this.getProjectById(projectId);
            
            if (!project || !project.contractId) {
                console.warn('Project not found or no contractId:', projectId);
                return [];
            }

            // Используем PasivOrganizationService для получения организаций по contractId
            const { PasivOrganizationService } = await import('./PasivOrganizationService.js');
            const organizations = await PasivOrganizationService.getOrganizationsByContractId(project.contractId);
            
            return organizations || [];
        } catch (error) {
            console.error('Ошибка получения организаций проекта:', error);

            // Fallback к локальным данным при ошибке API
            try {
                const projects = await this.getProjects();
                const project = projects.find(p => p.id == projectId);

                if (!project || !project.participantOrganizations) {
                    return [];
                }

                // Получаем ID организаций из проекта
                const organizationIds = project.participantOrganizations.map(org => org.id);

                // Импортируем OrganizationService динамически для избежания циклических зависимостей
                const { OrganizationService } = await import('./OrganizationService.js');

                // Получаем полные данные организаций из OrganizationService
                const organizations = await OrganizationService.getOrganizationsByIds(organizationIds);

                // Объединяем данные организаций с их ролями в проекте
                const organizationsWithRoles = organizations.map(org => {
                    const projectOrg = project.participantOrganizations.find(po => po.id === org.id);
                    return {
                        ...org,
                        role: projectOrg ? projectOrg.role : 'unknown'
                    };
                });

                return organizationsWithRoles;
            } catch (fallbackError) {
                console.error('Ошибка fallback получения организаций:', fallbackError);
                return [];
            }
        }
    },

    async getProjectStats(projectId) {
        console.log('Getting project stats:', projectId);

        try {
            const response = await proApiClient.get(`/api/pro/v1/projects/${projectId}/stats`);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения статистики проекта');
            }
        } catch (error) {
            console.error('Ошибка получения статистики проекта:', error);
            throw error;
        }
    },

    async updateManifest(projectId) {
        console.log('Updating manifest for project:', projectId);
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/manifest/update`);
            if (response.success) {
                return {
                    success: true,
                    message: response.message || 'Справочники проекта успешно обновлены',
                    updateDate: new Date().toISOString(),
                    stats: response.data // Обновленная статистика
                };
            } else {
                throw new Error(response.message || 'Ошибка обновления справочников проекта');
            }
        } catch (error) {
            console.error('Ошибка обновления справочников проекта:', error);
            throw error;
        }
    }
}
