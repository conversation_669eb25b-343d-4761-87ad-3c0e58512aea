import {sbolApiClient} from './ApiClient.js';
import {AgentService} from './AgentService.js';

/**
 * Сервис для работы с связями услуг и агентов
 */
export class ServiceAgentService {
    /**
     * Получить все связи услуг с агентами
     */
    static async getServiceAgents(page = 0, size = 20) {
        try {
            const response = await sbolApiClient.get('/api/abt/v1/sbol/service-agents', { page, size });
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении связей услуг с агентами:', error);
            throw error;
        }
    }

    /**
     * Получить связь услуги с агентом по ID
     */
    static async getServiceAgent(id) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/service-agents/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении связи услуги с агентом:', error);
            throw error;
        }
    }

    /**
     * Создать новую связь услуги с агентом
     */
    static async createServiceAgent(serviceAgentData) {
        try {
            const response = await sbolApiClient.post('/api/abt/v1/sbol/service-agents', serviceAgentData);
            return response.data;
        } catch (error) {
            console.error('Ошибка при создании связи услуги с агентом:', error);
            throw error;
        }
    }

    /**
     * Обновить связь услуги с агентом
     */
    static async updateServiceAgent(id, serviceAgentData) {
        try {
            const response = await sbolApiClient.put(`/api/abt/v1/sbol/service-agents/${id}`, serviceAgentData);
            return response.data;
        } catch (error) {
            console.error('Ошибка при обновлении связи услуги с агентом:', error);
            throw error;
        }
    }

    /**
     * Удалить связь услуги с агентом
     */
    static async deleteServiceAgent(id) {
        try {
            const response = await sbolApiClient.delete(`/api/abt/v1/sbol/service-agents/${id}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при удалении связи услуги с агентом:', error);
            throw error;
        }
    }

    /**
     * Получить связи услуги с агентами по ID услуги
     */
    static async getServiceAgentsByService(serviceId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/service-agents/service/${serviceId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении связей услуги с агентами:', error);
            throw error;
        }
    }

    /**
     * Получить связи агента с услугами по ID агента
     */
    static async getServiceAgentsByAgent(agentId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/service-agents/agent/${agentId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении связей агента с услугами:', error);
            throw error;
        }
    }

    /**
     * Получить связь услуги с агентом по ID услуги и ID агента
     */
    static async getServiceAgentByServiceAndAgent(serviceId, agentId) {
        try {
            const response = await sbolApiClient.get(`/api/abt/v1/sbol/service-agents/service/${serviceId}/agent/${agentId}`);
            return response.data;
        } catch (error) {
            console.error('Ошибка при получении связи услуги с агентом:', error);
            throw error;
        }
    }

    /**
     * Получить список агентов из реестра агентов
     */
    static async getAgents() {
        try {
            const agents = await AgentService.getAgents();
            return agents.map(agent => ({
                id: agent.uuid, // Используем UUID как ID
                name: agent.name,
                code: agent.code,
                status: agent.status
            }));
        } catch (error) {
            console.error('Ошибка при получении списка агентов:', error);
            throw error;
        }
    }
}
