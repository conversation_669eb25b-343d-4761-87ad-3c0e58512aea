import {pasivApiClient} from './ApiClient.js';

export const PasivOrganizationService = {
    async getOrganizationsByContractId(contractId, params = {}) {
        try {
            const queryParams = new URLSearchParams();
            
            // Добавляем параметры пагинации
            if (params.page !== undefined) queryParams.append('page', params.page);
            if (params.size !== undefined) queryParams.append('size', params.size);
            
            // Добавляем параметры фильтрации
            if (params.name) queryParams.append('name', params.name);
            if (params.inn) queryParams.append('inn', params.inn);
            if (params.kpp) queryParams.append('kpp', params.kpp);
            if (params.includeDeleted !== undefined) queryParams.append('includeDeleted', params.includeDeleted);

            const url = `/api/pasiv/v1/organizations/contract/${contractId}?${queryParams.toString()}`;
            const response = await pasivApiClient.get(url);
            
            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения организаций по договору');
            }
        } catch (error) {
            console.error('Ошибка получения организаций по договору:', error);
            throw error;
        }
    },

    async getOrganizations(params = {}) {
        console.log('Getting organizations:', params);

        try {
            const queryParams = new URLSearchParams();
            
            // Добавляем параметры пагинации
            if (params.page !== undefined) queryParams.append('page', params.page);
            if (params.size !== undefined) queryParams.append('size', params.size);
            
            // Добавляем параметры фильтрации
            if (params.name) queryParams.append('name', params.name);
            if (params.inn) queryParams.append('inn', params.inn);
            if (params.kpp) queryParams.append('kpp', params.kpp);
            if (params.includeDeleted !== undefined) queryParams.append('includeDeleted', params.includeDeleted);

            const url = `/api/pasiv/v1/organizations?${queryParams.toString()}`;
            const response = await pasivApiClient.get(url);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения организаций');
            }
        } catch (error) {
            console.error('Ошибка получения организаций:', error);
            throw error;
        }
    },

    async getOrganizationById(id) {
        console.log('Getting organization by ID:', id);

        try {
            const response = await pasivApiClient.get(`/api/pasiv/v1/organizations/${id}`);
            
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения организации');
            }
        } catch (error) {
            console.error('Ошибка получения организации:', error);
            throw error;
        }
    }
}; 