import {proApiClient} from './ApiClient.js';

export class EmployeeService {
    /**
     * Получить сотрудников проекта с фильтрацией и пагинацией
     */
    static async getEmployeesByProject(projectId, filters = {}, pagination = {}) {
        try {
            const params = new URLSearchParams({
                page: pagination.page || 0,
                size: pagination.size || 20
            });

            // Добавляем фильтры только если они есть
            if (filters.fullName) {
                params.append('fullName', filters.fullName);
            }
            if (filters.personalNumber) {
                params.append('personalNumber', filters.personalNumber);
            }
            if (filters.organizationId) {
                params.append('organizationId', filters.organizationId);
            }
            if (filters.role) {
                params.append('role', filters.role);
            }

            console.log('URL параметры:', params.toString());

            const response = await proApiClient.get(`/api/pro/v1/projects/${projectId}/employees?${params}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения сотрудников');
            }
        } catch (error) {
            console.error('Ошибка при получении сотрудников:', error);
            throw error;
        }
    }

    /**
     * Получить сотрудника по ID
     */
    static async getEmployeeById(id) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/employees/${id}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения сотрудника');
            }
        } catch (error) {
            console.error('Ошибка при получении сотрудника:', error);
            throw error;
        }
    }

    /**
     * Создать нового сотрудника
     */
    static async createEmployee(projectId, employeeData) {
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/employees`, employeeData);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания сотрудника');
            }
        } catch (error) {
            console.error('Ошибка при создании сотрудника:', error);
            throw error;
        }
    }

    /**
     * Обновить сотрудника
     */
    static async updateEmployee(id, employeeData) {
        try {
            const response = await proApiClient.put(`/api/pro/v1/employees/${id}`, employeeData);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления сотрудника');
            }
        } catch (error) {
            console.error('Ошибка при обновлении сотрудника:', error);
            throw error;
        }
    }

    /**
     * Удалить сотрудника
     */
    static async deleteEmployee(id) {
        try {
            const response = await proApiClient.delete(`/api/pro/v1/employees/${id}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка удаления сотрудника');
            }
        } catch (error) {
            console.error('Ошибка при удалении сотрудника:', error);
            throw error;
        }
    }

    /**
     * Получить роли сотрудников
     */
    static getEmployeeRoles() {
        return [
            { label: 'Водитель', value: 'terminal_user_driver' },
            { label: 'Администратор', value: 'terminal_user_admin' },
            { label: 'Кассир', value: 'terminal_user_cashier' },
            { label: 'Инкассатор', value: 'terminal_user_collector' },
            { label: 'Кондуктор', value: 'terminal_user_conductor' },
            { label: 'Контролер', value: 'terminal_user_controler' }
        ];
    }

    /**
     * Получить название роли по значению
     */
    static getRoleLabel(roleValue) {
        const roles = this.getEmployeeRoles();
        const role = roles.find(r => r.value === roleValue);
        return role ? role.label : roleValue;
    }
}
