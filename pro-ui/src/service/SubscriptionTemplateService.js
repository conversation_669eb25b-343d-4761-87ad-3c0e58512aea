import {abtApiClient} from './ApiClient.js';

/**
 * Сервис для работы с шаблонами абонементов
 */
export class SubscriptionTemplateService {

    /**
     * Получить список шаблонов абонементов
     * @param {Object} filters - Фильтры для поиска
     * @returns {Promise<Array>} Список шаблонов
     */
    static async getTemplates(filters = {}) {
        try {
            let endpoint = '/api/abt/v1/subscription-templates';

            // Применяем фильтры
            if (filters.status === 'active') {
                endpoint = '/api/abt/v1/subscription-templates/active';
            } else if (filters.type) {
                endpoint = `/api/abt/v1/subscription-templates/type/${filters.type}`;
            } else if (filters.isSocial !== undefined) {
                endpoint = `/api/abt/v1/subscription-templates/social/${filters.isSocial}`;
            }

            const response = await abtApiClient.get(endpoint);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка получения данных');
            }

            let templates = response.data || [];

            // Применяем поиск на клиенте, если нужно
            if (filters.search) {
                const search = filters.search.toLowerCase();
                templates = templates.filter(t =>
                    t.stName.toLowerCase().includes(search) ||
                    (t.description && t.description.toLowerCase().includes(search))
                );
            }

            return templates;
        } catch (error) {
            console.error('Ошибка получения шаблонов:', error);
            throw error;
        }
    }

    /**
     * Получить шаблон по ID
     * @param {string} id - ID шаблона
     * @returns {Promise<Object>} Шаблон
     */
    static async getTemplate(id) {
        try {
            const response = await abtApiClient.get(`/api/abt/v1/subscription-templates/${id}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка получения данных');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка получения шаблона:', error);
            throw error;
        }
    }

    /**
     * Получить счетчики шаблона
     * @param {string} templateId - ID шаблона
     * @returns {Promise<Array>} Список счетчиков
     */
    static async getTemplateCounters(templateId) {
        try {
            const response = await abtApiClient.get(`/api/abt/v1/subscription-template-counters/template/${templateId}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка получения данных');
            }

            return response.data || [];
        } catch (error) {
            console.error('Ошибка получения счетчиков шаблона:', error);
            throw error;
        }
    }

    /**
     * Получить правила шаблона
     * @param {string} templateId - ID шаблона
     * @returns {Promise<Array>} Список правил
     */
    static async getTemplateRules(templateId) {
        try {
            const response = await abtApiClient.get(`/api/abt/v1/subscription-template-rules/template/${templateId}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка получения данных');
            }

            return response.data || [];
        } catch (error) {
            console.error('Ошибка получения правил:', error);
            throw error;
        }
    }

    /**
     * Создать новый шаблон
     * @param {Object} template - Данные шаблона
     * @returns {Promise<Object>} Созданный шаблон
     */
    static async createTemplate(template) {
        try {
            const response = await abtApiClient.post('/api/abt/v1/subscription-templates', template);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка создания шаблона');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка создания шаблона:', error);
            throw error;
        }
    }

    /**
     * Обновить шаблон
     * @param {string} id - ID шаблона
     * @param {Object} template - Данные шаблона
     * @returns {Promise<Object>} Обновленный шаблон
     */
    static async updateTemplate(id, template) {
        try {
            const response = await abtApiClient.put(`/api/abt/v1/subscription-templates/${id}`, template);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка обновления шаблона');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка обновления шаблона:', error);
            throw error;
        }
    }

    /**
     * Удалить шаблон
     * @param {string} id - ID шаблона
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteTemplate(id) {
        try {
            const response = await abtApiClient.delete(`/api/abt/v1/subscription-templates/${id}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка удаления шаблона');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка удаления шаблона:', error);
            throw error;
        }
    }

    /**
     * Получить типы абонементов
     * @returns {Promise<Array>} Список типов
     */
    static async getTemplateTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Кошелек', value: 'WALLET' },
                    { label: 'Поездочный', value: 'TRAVEL' },
                    { label: 'Безлимитный', value: 'UNLIMITED' }
                ]);
            }, 100);
        });
    }

    /**
     * Получить типы срока действия
     * @returns {Promise<Array>} Список типов
     */
    static async getValidTimeTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Календарный месяц', value: 'INTERVAL' },
                    { label: 'Дни от первой поездки', value: 'DAYS' },
                    { label: 'Дни с ограничением', value: 'INTERVAL_AND_DAYS' }
                ]);
            }, 100);
        });
    }

    /**
     * Создать счетчик для шаблона
     * @param {Object} counter - Данные счетчика
     * @returns {Promise<Object>} Созданный счетчик
     */
    static async createCounter(counter) {
        try {
            const response = await abtApiClient.post('/api/abt/v1/subscription-template-counters', counter);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка создания счетчика');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка создания счетчика:', error);
            throw error;
        }
    }

    /**
     * Обновить счетчик
     * @param {string} id - ID счетчика
     * @param {Object} counter - Данные счетчика
     * @returns {Promise<Object>} Обновленный счетчик
     */
    static async updateCounter(id, counter) {
        try {
            const response = await abtApiClient.put(`/api/abt/v1/subscription-template-counters/${id}`, counter);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка обновления счетчика');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка обновления счетчика:', error);
            throw error;
        }
    }

    /**
     * Удалить счетчик
     * @param {string} id - ID счетчика
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteCounter(id) {
        try {
            const response = await abtApiClient.delete(`/api/abt/v1/subscription-template-counters/${id}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка удаления счетчика');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка удаления счетчика:', error);
            throw error;
        }
    }

    /**
     * Создать несколько счетчиков для шаблона
     * @param {string} templateId - ID шаблона
     * @param {Array} counters - Массив счетчиков
     * @returns {Promise<Array>} Созданные счетчики
     */
    static async createCountersForTemplate(templateId, counters) {
        try {
            const response = await abtApiClient.post(`/api/abt/v1/subscription-template-counters/template/${templateId}/batch`, counters);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка создания счетчиков');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка создания счетчиков:', error);
            throw error;
        }
    }

    /**
     * Создать правило для шаблона
     * @param {Object} rule - Данные правила
     * @returns {Promise<Object>} Созданное правило
     */
    static async createRule(rule) {
        try {
            const response = await abtApiClient.post('/api/abt/v1/subscription-template-rules', rule);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка создания правила');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка создания правила:', error);
            throw error;
        }
    }

    /**
     * Обновить правило
     * @param {string} id - ID правила
     * @param {Object} rule - Данные правила
     * @returns {Promise<Object>} Обновленное правило
     */
    static async updateRule(id, rule) {
        try {
            const response = await abtApiClient.put(`/api/abt/v1/subscription-template-rules/${id}`, rule);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка обновления правила');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка обновления правила:', error);
            throw error;
        }
    }

    /**
     * Удалить правило
     * @param {string} id - ID правила
     * @returns {Promise<boolean>} Результат удаления
     */
    static async deleteRule(id) {
        try {
            const response = await abtApiClient.delete(`/api/abt/v1/subscription-template-rules/${id}`);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка удаления правила');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка удаления правила:', error);
            throw error;
        }
    }

    /**
     * Создать несколько правил для шаблона
     * @param {string} templateId - ID шаблона
     * @param {Array} rules - Массив правил
     * @returns {Promise<Array>} Созданные правила
     */
    static async createRulesForTemplate(templateId, rules) {
        try {
            const response = await abtApiClient.post(`/api/abt/v1/subscription-template-rules/template/${templateId}/batch`, rules);

            if (!response.success) {
                throw new Error(response.message || 'Ошибка создания правил');
            }

            return response.data;
        } catch (error) {
            console.error('Ошибка создания правил:', error);
            throw error;
        }
    }

    /**
     * Получить типы счетчиков
     * @returns {Promise<Array>} Список типов
     */
    static async getCounterTypes() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { label: 'Количество поездок', value: 'TRAVEL_COUNT' },
                    { label: 'Сумма списаний', value: 'AMOUNT' },
                    { label: 'Время действия', value: 'TIME_LIMIT' }
                ]);
            }, 100);
        });
    }
}
