import {proApiClient} from './ApiClient';

export const StationService = {

    async getStations() {
        try {
            const response = await proApiClient.get('/api/pro/v1/stations');
            if (response.success) {
                return response.data?.content || [];
            } else {
                throw new Error(response.message || 'Ошибка получения станций');
            }
        } catch (error) {
            console.error('Ошибка получения станций:', error);
            throw error;
        }
    },

    async getStationsByProject(projectId, page = 0, size = 10, filters = {}) {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                size: size.toString()
            });

            // Добавляем фильтры
            if (filters.name) params.append('name', filters.name);
            if (filters.city) params.append('city', filters.city);
            if (filters.region) params.append('region', filters.region);
            if (filters.country) params.append('country', filters.country);

            const response = await proApiClient.get(`/api/pro/v1/projects/${projectId}/stations?${params}`);
            if (response.success) {
                return {
                    content: response.data?.content || [],
                    pagination: response.data?.pagination || {}
                };
            } else {
                throw new Error(response.message || 'Ошибка получения станций проекта');
            }
        } catch (error) {
            console.error('Ошибка получения станций проекта:', error);
            throw error;
        }
    },

    async getStationsByIds(stationIds) {
        try {
            const response = await proApiClient.post('/api/pro/v1/stations/batch', { stationIds });
            if (response.success) {
                return response.data || [];
            } else {
                throw new Error(response.message || 'Ошибка получения станций по ID');
            }
        } catch (error) {
            console.error('Ошибка получения станций по ID:', error);
            throw error;
        }
    },

    async getStationById(stationId) {
        try {
            console.log('Запрашиваем станцию по ID:', stationId);
            const response = await proApiClient.get(`/api/pro/v1/stations/${stationId}`);
            console.log('Ответ от сервера для получения станции:', response);

            if (response.success) {
                console.log('Данные станции:', response.data);
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка получения станции');
            }
        } catch (error) {
            console.error('Ошибка получения станции:', error);
            throw error;
        }
    },

    async createStation(projectId, stationData) {
        try {
            const response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/stations`, stationData);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка создания станции');
            }
        } catch (error) {
            console.error('Ошибка создания станции:', error);
            throw error;
        }
    },

    async updateStation(stationId, stationData) {
        try {
            console.log('Обновляем станцию, ID:', stationId);
            console.log('Данные для обновления:', stationData);
            const response = await proApiClient.put(`/api/pro/v1/stations/${stationId}`, stationData);
            console.log('Ответ от сервера при обновлении:', response);

            if (response.success) {
                console.log('Станция успешно обновлена:', response.data);
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка обновления станции');
            }
        } catch (error) {
            console.error('Ошибка обновления станции:', error);
            throw error;
        }
    },

    async deleteStation(stationId) {
        try {
            const response = await proApiClient.delete(`/api/pro/v1/stations/${stationId}`);
            if (response.success) {
                return { success: true };
            } else {
                throw new Error(response.message || 'Ошибка удаления станции');
            }
        } catch (error) {
            console.error('Ошибка удаления станции:', error);
            throw error;
        }
    },

    async syncStationWithContract(stationId) {
        try {
            const response = await proApiClient.post(`/api/pro/v1/stations/${stationId}/sync`);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка синхронизации станции');
            }
        } catch (error) {
            console.error('Ошибка синхронизации станции:', error);
            throw error;
        }
    },

    async importStations(projectId, file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await proApiClient.post(`/api/pro/v1/projects/${projectId}/stations/import`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка импорта станций');
            }
        } catch (error) {
            console.error('Ошибка импорта станций:', error);
            throw error;
        }
    },

    async exportStations(projectId) {
        try {
            const response = await proApiClient.get(`/api/pro/v1/projects/${projectId}/stations/export`, {
                responseType: 'blob'
            });

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Ошибка экспорта станций');
            }
        } catch (error) {
            console.error('Ошибка экспорта станций:', error);
            throw error;
        }
    }
};
