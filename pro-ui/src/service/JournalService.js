import {tmsApiClient} from './ApiClient.js';

export const JournalService = {
    async getJournalList(params = {}) {
        try {
            const queryParams = new URLSearchParams();

            if (params.projectId) queryParams.append('projectId', params.projectId);
            if (params.terminalSerial) queryParams.append('terminalSerial', params.terminalSerial);
            if (params.eventTypes && params.eventTypes.length > 0) {
                params.eventTypes.forEach(type => queryParams.append('eventTypes', type));
            }
            if (params.createdAtFrom) queryParams.append('createdAtFrom', params.createdAtFrom);
            if (params.createdAtTo) queryParams.append('createdAtTo', params.createdAtTo);
            if (params.shiftNum) queryParams.append('shiftNum', params.shiftNum);
            if (params.ern) queryParams.append('ern', params.ern);
            if (params.page !== undefined) queryParams.append('page', params.page);
            if (params.size !== undefined) queryParams.append('size', params.size);
            if (params.sortField) queryParams.append('sortField', params.sortField);
            if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

            const endpoint = `/api/tms/v1/journal?${queryParams.toString()}`;
            const response = await tmsApiClient.get(endpoint);
            return response || { events: [], pagination: { page: 0, limit: 25, totalCount: 0, totalPage: 0 } };
        } catch (error) {
            console.error('Ошибка при получении журнала операций:', error);
            return { events: [], pagination: { page: 0, limit: 25, totalCount: 0, totalPage: 0 } };
        }
    },

    async getEventTypes() {
        try {
            const response = await tmsApiClient.get('/api/tms/v1/event-types');
            return response || [];
        } catch (error) {
            console.error('Ошибка при получении типов событий:', error);
            return [];
        }
    }
};
