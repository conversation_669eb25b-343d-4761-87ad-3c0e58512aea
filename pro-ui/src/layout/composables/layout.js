import {computed, reactive} from 'vue';

const layoutConfig = reactive({
    preset: 'Aura',
    primary: 'emerald',
    surface: null,
    darkTheme: false,
    menuMode: 'static'
});

const layoutState = reactive({
    staticMenuDesktopInactive: false,
    overlayMenuActive: false,
    profileSidebarVisible: false,
    configSidebarVisible: false,
    staticMenuMobileActive: false,
    menuHoverActive: false,
    activeMenuItem: null
});

// Функции для работы с localStorage
const STORAGE_KEY = 'pro-sidebar-state';

const saveSidebarState = () => {
    const state = {
        staticMenuDesktopInactive: layoutState.staticMenuDesktopInactive,
        overlayMenuActive: layoutState.overlayMenuActive,
        staticMenuMobileActive: layoutState.staticMenuMobileActive
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
};

const loadSidebarState = () => {
    try {
        const savedState = localStorage.getItem(STORAGE_KEY);
        if (savedState) {
            const state = JSON.parse(savedState);
            layoutState.staticMenuDesktopInactive = state.staticMenuDesktopInactive || false;
            layoutState.overlayMenuActive = state.overlayMenuActive || false;
            layoutState.staticMenuMobileActive = state.staticMenuMobileActive || false;
        }
    } catch (error) {
        console.warn('Ошибка загрузки состояния sidebar из localStorage:', error);
    }
};

export function useLayout() {
    const setActiveMenuItem = (item) => {
        layoutState.activeMenuItem = item.value || item;
    };

    const toggleDarkMode = () => {
        if (!document.startViewTransition) {
            executeDarkModeToggle();

            return;
        }

        document.startViewTransition(() => executeDarkModeToggle(event));
    };

    const executeDarkModeToggle = () => {
        layoutConfig.darkTheme = !layoutConfig.darkTheme;
        document.documentElement.classList.toggle('app-dark');
    };

    const toggleMenu = () => {
        if (layoutConfig.menuMode === 'overlay') {
            layoutState.overlayMenuActive = !layoutState.overlayMenuActive;
        }

        if (window.innerWidth > 991) {
            layoutState.staticMenuDesktopInactive = !layoutState.staticMenuDesktopInactive;
        } else {
            layoutState.staticMenuMobileActive = !layoutState.staticMenuMobileActive;
        }

        // Сохраняем состояние в localStorage
        saveSidebarState();
    };

    const isSidebarActive = computed(() => layoutState.overlayMenuActive || layoutState.staticMenuMobileActive);

    const isDarkTheme = computed(() => layoutConfig.darkTheme);

    const getPrimary = computed(() => layoutConfig.primary);

    const getSurface = computed(() => layoutConfig.surface);

    // Функция инициализации для загрузки состояния при старте приложения
    const initializeLayout = () => {
        loadSidebarState();
    };

    return {
        layoutConfig,
        layoutState,
        toggleMenu,
        isSidebarActive,
        isDarkTheme,
        getPrimary,
        getSurface,
        setActiveMenuItem,
        toggleDarkMode,
        initializeLayout
    };
}
