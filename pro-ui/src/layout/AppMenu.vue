<script setup>
import {computed} from 'vue';
import {useAuthStore} from '@/stores/auth';

import AppMenuItem from './AppMenuItem.vue';

const authStore = useAuthStore();
const showAllFeatures = computed(() => authStore.isShowAllFeatures);

const model = computed(() => {
    const baseModel = [
        {
            label: 'СберТройка ПРО',
            items: [
                { label: 'Проекты', icon: 'pi pi-fw pi-star', to: '/pro' },
                { label: 'Претензионный центр', icon: 'pi pi-fw pi-star', to: '/pro-support' },
            ]
        },
                {
            label: 'АС TMS',
            items: [
                { label: 'Терминалы', icon: 'pi pi-fw pi-tablet', to: '/tms/terminals' },
                // Пункт "ПО Терминалов" показывается только при showAllFeatures === true
                ...(showAllFeatures.value ? [{ label: 'ПО Терминалов', icon: 'pi pi-fw pi-download', to: '/tms/software' }] : []),
                // Пункт "Конфигурации" показывается только при showAllFeatures === true
                ...(showAllFeatures.value ? [{ label: 'Конфигурации', icon: 'pi pi-fw pi-cog', to: '/tms/configurations' }] : []),
                { label: 'Операции', icon: 'pi pi-fw pi-list', to: '/tms/operations' },
                // Пункт "Телеметрия" показывается только при showAllFeatures === true
                ...(showAllFeatures.value ? [{ label: 'Телеметрия', icon: 'pi pi-fw pi-chart-line', to: '/tms/telemetry' }] : []),
            ]
                },
        // Раздел "АС ПАСИВ" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС ПАСИВ',
            items: [
                { label: 'Организации', icon: 'pi pi-fw pi-building', to: '/organizations' },
                { label: 'Договоры', icon: 'pi pi-fw pi-file-edit', to: '/contracts' },
                { label: 'Тарифы', icon: 'pi pi-fw pi-dollar', to: '/pasiv/tariffs' },
                { label: 'Биллинг', icon: 'pi pi-fw pi-calculator', to: '/pasiv/billing' },
                { label: 'Взаиморасчеты', icon: 'pi pi-fw pi-money-bill', to: '/pasiv/settlements' },
                { label: 'Финансовые отчеты', icon: 'pi pi-fw pi-chart-bar', to: '/pasiv/reports' },
                { label: 'Клиринг', icon: 'pi pi-fw pi-refresh', to: '/pasiv/clearing' },
            ]
        }] : []),
        // Раздел "АС Агент" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС Агент',
            items: [
                { label: 'Реестр агентов', icon: 'pi pi-fw pi-users', to: '/agent/registry' },
                { label: 'Точки обслуживания', icon: 'pi pi-fw pi-map-marker', to: '/agent/service-points' },
                { label: 'Операции агентов', icon: 'pi pi-fw pi-shopping-cart', to: '/agent/operations' },
                { label: 'Отчеты агентов', icon: 'pi pi-fw pi-file', to: '/agent/reports' },
            ]
        }] : []),
        // Раздел "АС CASH" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС CASH',
            items: [
                { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/cash/transactions' },
                { label: 'Билеты (наличные)', icon: 'pi pi-fw pi-ticket', to: '/cash/tickets' },
                { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/cash/statistics' },
            ]
        }] : []),
        // Раздел "АС EMV" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС EMV',
            items: [
                { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/emv/transactions' },
                { label: 'Билеты (банк. карты)', icon: 'pi pi-fw pi-ticket', to: '/emv/tickets' },
                { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/emv/statistics' },
            ]
        }] : []),
        // Раздел "АС CBT" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС CBT',
            items: [
                { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/cbt/transactions' },
                { label: 'Билеты (Тройка)', icon: 'pi pi-fw pi-ticket', to: '/cbt/tickets' },
                { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/cbt/statistics' },
            ]
        }] : []),
        // Раздел "АС ABT" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС ABT',
            items: [
                { label: 'Транзакции', icon: 'pi pi-fw pi-list', to: '/abt/transactions' },
                { label: 'Билеты (мобильные)', icon: 'pi pi-fw pi-ticket', to: '/abt/tickets' },
                { label: 'Шаблоны абонементов', icon: 'pi pi-fw pi-cog', to: '/abt/templates' },
                { label: 'Абонементы', icon: 'pi pi-fw pi-id-card', to: '/abt/subscriptions' },
                { label: 'Кошельки', icon: 'pi pi-fw pi-wallet', to: '/abt/wallets' },
                { label: 'Билетное меню', icon: 'pi pi-fw pi-shopping-cart', to: '/abt/services' },
                { label: 'Продажи', icon: 'pi pi-fw pi-credit-card', to: '/abt/sales' },
                { label: 'Статистика', icon: 'pi pi-fw pi-chart-bar', to: '/abt/statistics' },
            ]
        }] : []),
        // Раздел "АС FISCAL" показывается только при showAllFeatures === true
        ...(showAllFeatures.value ? [{
            label: 'АС FISCAL',
            items: [
                { label: 'Фискальные операции', icon: 'pi pi-fw pi-receipt', to: '/fiscal/operations' },
                { label: 'Фискальные регистраторы', icon: 'pi pi-fw pi-print', to: '/fiscal/registers' },
                { label: 'Чеки и документы', icon: 'pi pi-fw pi-file-pdf', to: '/fiscal/documents' },
                { label: 'Отчеты ФНС', icon: 'pi pi-fw pi-send', to: '/fiscal/fns-reports' },
            ]
        }] : [])
    ];

    return baseModel;
});
</script>

<template>
    <ul class="layout-menu">
        <template v-for="(item, i) in model" :key="item">
            <app-menu-item v-if="!item.separator" :item="item" :index="i"></app-menu-item>
            <li v-if="item.separator" class="menu-separator"></li>
        </template>
    </ul>
</template>

<style lang="scss" scoped></style>
