import AppLayout from '@/layout/AppLayout.vue';
import {createRouter, createWebHistory} from 'vue-router';
import {requireAuth} from './auth-guard';

const router = createRouter({
    history: createWebHistory(),
    routes: [
        // Маршруты для авторизации (без проверки)
        {
            path: '/auth-error',
            name: 'auth-error',
            component: () => import('@/views/AuthError.vue')
        },
        {
            path: '/forbidden',
            name: 'forbidden',
            component: () => import('@/views/Forbidden.vue')
        },
        // Основные маршруты с проверкой авторизации
        {
            path: '/',
            component: AppLayout,
            beforeEnter: requireAuth,
            children: [
        {
            path: '/',
            component: AppLayout,
            children: [
                {
                    path: '/',
                    redirect: '/pro'
                },
                {
                    path: '/pro-support',
                    name: 'pro_support',
                    component: () => import('@/views/ProSupport.vue')
                },
                {
                    path: '/organizations',
                    name: 'organizations',
                    component: () => import('@/views/OrganizationList.vue')
                },
                {
                    path: '/organizations/create',
                    name: 'organizations_create',
                    component: () => import('@/views/OrganizationForm.vue')
                },
                {
                    path: '/organizations/:organizationId',
                    name: 'organizations_detail',
                    component: () => import('@/views/OrganizationDetail.vue')
                },
                {
                    path: '/organizations/:organizationId/edit',
                    name: 'organizations_edit',
                    component: () => import('@/views/OrganizationForm.vue')
                },
                {
                    path: '/contracts',
                    name: 'contracts',
                    component: () => import('@/views/project/finance/ContractList.vue')
                },
                {
                    path: '/contracts/create',
                    name: 'contracts_create',
                    component: () => import('@/views/project/finance/ContractForm.vue')
                },
                {
                    path: '/contracts/:contractId',
                    name: 'contracts_detail',
                    component: () => import('@/views/project/finance/ContractDetail.vue')
                },
                {
                    path: '/contracts/:contractId/edit',
                    name: 'contracts_edit',
                    component: () => import('@/views/project/finance/ContractForm.vue')
                },
                {
                    path: '/pro',
                    name: 'pro',
                    component: () => import('@/views/Pro.vue')
                },
                {
                    path: '/pro/create',
                    name: 'pro_create',
                    component: () => import('@/views/ProjectForm.vue')
                },
                // Убираем дублирующиеся маршруты - используем старую структуру с дочерними маршрутами
                {
                    path: '/pro/:projectId',
                    name: 'pro_project',
                    component: () => import('@/views/ProjectDetail.vue'),
                    children: [
                        // Убираем дочерний маршрут info - основная информация отображается в ProjectDetail.vue
                        {
                            path: 'nsi/station',
                            name: 'pro_project_nsi_station',
                            component: () => import('@/views/project/nsi/StationList.vue')
                        },
                        {
                            path: 'nsi/station/create',
                            name: 'pro_project_nsi_station_create',
                            component: () => import('@/views/project/nsi/StationForm.vue')
                        },
                        {
                            path: 'nsi/station/:stationId/edit',
                            name: 'pro_project_nsi_station_edit',
                            component: () => import('@/views/project/nsi/StationForm.vue')
                        },
                        {
                            path: 'nsi/route',
                            name: 'pro_project_nsi_route',
                            component: () => import('@/views/project/nsi/RouteList.vue')
                        },
                        {
                            path: 'nsi/route/create',
                            name: 'pro_project_nsi_route_create',
                            component: () => import('@/views/project/nsi/RouteForm.vue')
                        },
                        {
                            path: 'nsi/route/:routeId/edit',
                            name: 'pro_project_nsi_route_edit',
                            component: () => import('@/views/project/nsi/RouteForm.vue')
                        },
                        {
                            path: 'nsi/driver',
                            name: 'pro_project_nsi_driver',
                            component: () => import('@/views/project/nsi/DriverList.vue')
                        },
                        {
                            path: 'nsi/driver/create',
                            name: 'pro_project_nsi_driver_create',
                            component: () => import('@/views/project/nsi/DriverForm.vue')
                        },
                        {
                            path: 'nsi/driver/:driverId/edit',
                            name: 'pro_project_nsi_driver_edit',
                            component: () => import('@/views/project/nsi/DriverForm.vue')
                        },
                        {
                            path: 'nsi/driver/:driverId',
                            name: 'pro_project_nsi_driver_detail',
                            component: () => import('@/views/project/nsi/DriverDetail.vue')
                        },
                        {
                            path: 'nsi/vehicle',
                            name: 'pro_project_nsi_vehicle',
                            component: () => import('@/views/project/nsi/VehicleList.vue')
                        },
                        {
                            path: 'nsi/vehicle/create',
                            name: 'pro_project_nsi_vehicle_create',
                            component: () => import('@/views/project/nsi/VehicleForm.vue')
                        },
                        {
                            path: 'nsi/vehicle/:vehicleId/edit',
                            name: 'pro_project_nsi_vehicle_edit',
                            component: () => import('@/views/project/nsi/VehicleForm.vue')
                        },
                        {
                            path: 'nsi/vehicle/:vehicleId',
                            name: 'pro_project_nsi_vehicle_detail',
                            component: () => import('@/views/project/nsi/VehicleForm.vue')
                        },
                        {
                            path: 'nsi/organization',
                            name: 'pro_project_nsi_organization',
                            component: () => import('@/views/project/nsi/OrganizationList.vue')
                        },
                        {
                            path: 'nsi/product',
                            name: 'pro_project_nsi_product',
                            component: () => import('@/views/project/nsi/ProductList.vue')
                        },
                        {
                            path: 'nsi/product/create',
                            name: 'pro_project_nsi_product_create',
                            component: () => import('@/views/project/nsi/ProductForm.vue')
                        },
                        {
                            path: 'nsi/product/:productId/edit',
                            name: 'pro_project_nsi_product_edit',
                            component: () => import('@/views/project/nsi/ProductForm.vue')
                        },
                        {
                            path: 'nsi/tariff',
                            name: 'pro_project_nsi_tariff',
                            component: () => import('@/views/nsi/TariffList.vue')
                        },
                        {
                            path: 'nsi/tariff/:tariffId',
                            name: 'pro_project_nsi_tariff_detail',
                            component: () => import('@/views/nsi/TariffDetail.vue')
                        },
                        {
                            path: 'tariffs',
                            name: 'pro_project_tariffs',
                            component: () => import('@/views/project/TariffPage.vue')
                        },
                        {
                            path: 'payments',
                            name: 'pro_project_payments',
                            component: () => import('@/views/project/PaymentList.vue')
                        },
                        {
                            path: 'statistics',
                            name: 'pro_project_statistics',
                            component: () => import('@/views/project/Statistics.vue')
                        },
                        // Финансы
                        {
                            path: 'finance/contract',
                            name: 'pro_project_finance_contract',
                            component: () => import('@/views/project/finance/ProjectContract.vue')
                        },
                        {
                            path: 'finance/contract/create',
                            name: 'pro_project_finance_contract_create',
                            component: () => import('@/views/project/finance/ContractForm.vue')
                        },
                        {
                            path: 'finance/contract/:contractId',
                            name: 'pro_project_finance_contract_detail',
                            component: () => import('@/views/project/finance/ContractDetail.vue')
                        },
                        {
                            path: 'finance/contract/:contractId/edit',
                            name: 'pro_project_finance_contract_edit',
                            component: () => import('@/views/project/finance/ContractForm.vue')
                        },
                        {
                            path: 'finance/payment-methods',
                            name: 'pro_project_finance_payment_methods',
                            component: () => import('@/views/project/finance/PaymentMethodList.vue')
                        },
                        {
                            path: 'finance/payment-method/create',
                            name: 'pro_project_finance_payment_method_create',
                            component: () => import('@/views/project/finance/PaymentMethodForm.vue')
                        },
                        {
                            path: 'finance/payment-method/:methodId/edit',
                            name: 'pro_project_finance_payment_method_edit',
                            component: () => import('@/views/project/finance/PaymentMethodForm.vue')
                        },
                        // Редактирование проекта
                        {
                            path: 'edit',
                            name: 'pro_project_edit',
                            component: () => import('@/views/ProjectForm.vue')
                        }
                    ]
                }
            ]
        },
        // АС TMS - Управление терминалами
        {
            path: '/tms',
            component: AppLayout,
            children: [
                {
                    path: 'terminals',
                    name: 'tms_terminals',
                    component: () => import('@/views/tms/TerminalList.vue')
                },
                {
                    path: 'terminals/create',
                    name: 'tms_terminals_create',
                    component: () => import('@/views/tms/TerminalForm.vue')
                },
                {
                    path: 'terminals/:terminalId',
                    name: 'tms_terminals_detail',
                    component: () => import('@/views/tms/TerminalDetail.vue')
                },
                {
                    path: 'terminals/:terminalId/edit',
                    name: 'tms_terminals_edit',
                    component: () => import('@/views/tms/TerminalForm.vue')
                },
                {
                    path: 'software',
                    name: 'tms_software',
                    component: () => import('@/views/tms/SoftwareList.vue')
                },
                {
                    path: 'configurations',
                    name: 'tms_configurations',
                    component: () => import('@/views/tms/ConfigurationList.vue')
                },
                {
                    path: 'operations',
                    name: 'tms_operations',
                    component: () => import('@/views/tms/OperationList.vue')
                },
                {
                    path: 'telemetry',
                    name: 'tms_telemetry',
                    component: () => import('@/views/tms/TelemetryDashboard.vue')
                }
            ]
        },
        // АС ПАСИВ - Биллинг и взаиморасчеты
        {
            path: '/pasiv',
            component: AppLayout,
            children: [
                {
                    path: 'tariffs',
                    name: 'pasiv_tariffs',
                    component: () => import('@/views/pasiv/TariffList.vue')
                },
                {
                    path: 'tariffs/create',
                    name: 'pasiv_tariffs_create',
                    component: () => import('@/views/pasiv/TariffForm.vue')
                },
                {
                    path: 'tariffs/:tariffId/edit',
                    name: 'pasiv_tariffs_edit',
                    component: () => import('@/views/pasiv/TariffForm.vue')
                },
                {
                    path: 'billing',
                    name: 'pasiv_billing',
                    component: () => import('@/views/pasiv/BillingList.vue')
                },
                {
                    path: 'settlements',
                    name: 'pasiv_settlements',
                    component: () => import('@/views/pasiv/SettlementList.vue')
                },
                {
                    path: 'reports',
                    name: 'pasiv_reports',
                    component: () => import('@/views/pasiv/ReportList.vue')
                },
                {
                    path: 'clearing',
                    name: 'pasiv_clearing',
                    component: () => import('@/views/pasiv/ClearingList.vue')
                }
            ]
        },
        // АС Агент
        {
            path: '/agent',
            component: AppLayout,
            children: [
                {
                    path: 'registry',
                    name: 'agent_registry',
                    component: () => import('@/views/agent/AgentList.vue')
                },
                {
                    path: 'registry/create',
                    name: 'agent_registry_create',
                    component: () => import('@/views/agent/AgentForm.vue')
                },
                {
                    path: 'registry/:agentId',
                    name: 'agent_registry_detail',
                    component: () => import('@/views/agent/AgentDetail.vue')
                },
                {
                    path: 'registry/:agentId/edit',
                    name: 'agent_registry_edit',
                    component: () => import('@/views/agent/AgentForm.vue')
                },
                {
                    path: 'service-points',
                    name: 'agent_service_points',
                    component: () => import('@/views/agent/ServicePointList.vue')
                },
                {
                    path: 'operations',
                    name: 'agent_operations',
                    component: () => import('@/views/agent/OperationList.vue')
                },
                {
                    path: 'reports',
                    name: 'agent_reports',
                    component: () => import('@/views/agent/ReportList.vue')
                }
            ]
        },
        // АС CASH
        {
            path: '/cash',
            component: AppLayout,
            children: [
                {
                    path: 'transactions',
                    name: 'cash_transactions',
                    component: () => import('@/views/cash/TransactionList.vue')
                },
                {
                    path: 'tickets',
                    name: 'cash_tickets',
                    component: () => import('@/views/cash/TicketList.vue')
                },
                {
                    path: 'statistics',
                    name: 'cash_statistics',
                    component: () => import('@/views/cash/Statistics.vue')
                }
            ]
        },
        // АС EMV
        {
            path: '/emv',
            component: AppLayout,
            children: [
                {
                    path: 'transactions',
                    name: 'emv_transactions',
                    component: () => import('@/views/emv/TransactionList.vue')
                },
                {
                    path: 'tickets',
                    name: 'emv_tickets',
                    component: () => import('@/views/emv/TicketList.vue')
                },
                {
                    path: 'statistics',
                    name: 'emv_statistics',
                    component: () => import('@/views/emv/Statistics.vue')
                }
            ]
        },
        // АС CBT
        {
            path: '/cbt',
            component: AppLayout,
            children: [
                {
                    path: 'transactions',
                    name: 'cbt_transactions',
                    component: () => import('@/views/cbt/TransactionList.vue')
                },
                {
                    path: 'tickets',
                    name: 'cbt_tickets',
                    component: () => import('@/views/cbt/TicketList.vue')
                },
                {
                    path: 'statistics',
                    name: 'cbt_statistics',
                    component: () => import('@/views/cbt/Statistics.vue')
                }
            ]
        },
        // АС ABT
        {
            path: '/abt',
            component: AppLayout,
            children: [
                {
                    path: 'transactions',
                    name: 'abt_transactions',
                    component: () => import('@/views/abt/TransactionList.vue')
                },
                {
                    path: 'tickets',
                    name: 'abt_tickets',
                    component: () => import('@/views/abt/TicketList.vue')
                },
                {
                    path: 'statistics',
                    name: 'abt_statistics',
                    component: () => import('@/views/abt/Statistics.vue')
                },
                {
                    path: 'templates',
                    name: 'abt_templates',
                    component: () => import('@/views/abt/SubscriptionTemplateList.vue')
                },
                {
                    path: 'templates/create',
                    name: 'abt_templates_create',
                    component: () => import('@/views/abt/SubscriptionTemplateForm.vue')
                },
                {
                    path: 'templates/:id',
                    name: 'abt_templates_detail',
                    component: () => import('@/views/abt/SubscriptionTemplateDetail.vue')
                },
                {
                    path: 'templates/:id/edit',
                    name: 'abt_templates_edit',
                    component: () => import('@/views/abt/SubscriptionTemplateForm.vue')
                },
                {
                    path: 'subscriptions',
                    name: 'abt_subscriptions',
                    component: () => import('@/views/abt/SubscriptionList.vue')
                },
                {
                    path: 'wallets',
                    name: 'abt_wallets',
                    component: () => import('@/views/abt/WalletList.vue')
                },
                {
                    path: 'services',
                    name: 'abt_services',
                    component: () => import('@/views/abt/ServiceList.vue')
                },
                {
                    path: 'services/create',
                    name: 'abt_services_create',
                    component: () => import('@/views/abt/ServiceForm.vue')
                },
                {
                    path: 'services/:id',
                    name: 'abt_services_detail',
                    component: () => import('@/views/abt/ServiceDetail.vue')
                },
                {
                    path: 'services/:id/edit',
                    name: 'abt_services_edit',
                    component: () => import('@/views/abt/ServiceForm.vue')
                },
                {
                    path: 'sales',
                    name: 'abt_sales',
                    component: () => import('@/views/abt/SalesList.vue')
                },
                {
                    path: 'sales/:id',
                    name: 'abt_sales_detail',
                    component: () => import('@/views/abt/SalesDetail.vue')
                }
            ]
        },
        // АС FISCAL
        {
            path: '/fiscal',
            component: AppLayout,
            beforeEnter: requireAuth,
            children: [
                {
                    path: 'operations',
                    name: 'fiscal_operations',
                    component: () => import('@/views/fiscal/FiscalOperationList.vue')
                },
                {
                    path: 'registers',
                    name: 'fiscal_registers',
                    component: () => import('@/views/fiscal/FiscalRegisterList.vue')
                },
                {
                    path: 'documents',
                    name: 'fiscal_documents',
                    component: () => import('@/views/fiscal/DocumentList.vue')
                },
                {
                    path: 'fns-reports',
                    name: 'fiscal_fns_reports',
                    component: () => import('@/views/fiscal/FnsReportList.vue')
                }
            ]
        }
            ]
        }
    ]
});

export default router;
