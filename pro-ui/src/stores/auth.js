import {defineStore} from 'pinia';
import getKeycloakConfig from '@/config/keycloak';
import {initKeycloak} from '@/utils/keycloak-init';

export const useAuthStore = defineStore('auth', {
    state: () => ({
        keycloak: null,
        authenticated: false,
        user: null,
        token: null,
        loading: true,
        error: null
    }),

    getters: {
        isAuthenticated: (state) => state.authenticated,
        getUser: (state) => state.user,
        getToken: (state) => state.token,
        isLoading: (state) => state.loading,
        getError: (state) => state.error,

        isShowAllFeatures: (state) => {
            if (!state.keycloak || !state.keycloak.tokenParsed) {
                console.log('JWT token not available or not parsed');
                return false;
            }

            const tokenParsed = state.keycloak.tokenParsed;
            const hasFeatureAll = 'feature_all' in tokenParsed;
            const featureAllValue = tokenParsed.feature_all;

            console.log('JWT token parsed:', tokenParsed);
            console.log('Has feature_all attribute:', hasFeatureAll);
            console.log('feature_all value:', featureAllValue);

            return hasFeatureAll && featureAllValue === true;
        },

        getProjectId: (state) => {
            if (!state.keycloak || !state.keycloak.tokenParsed) {
                return null;
            }

            const tokenParsed = state.keycloak.tokenParsed;
            const projectId = tokenParsed.projectId;

            console.log('JWT token parsed:', tokenParsed);
            console.log('projectId from token:', projectId);

            return projectId;
        },

        hasProjectId: (state) => {
            if (!state.keycloak || !state.keycloak.tokenParsed) {
                return false;
            }

            const tokenParsed = state.keycloak.tokenParsed;
            const projectId = tokenParsed.projectId;

            return projectId && projectId !== '' && projectId !== null;
        }
    },

    actions: {
        async initKeycloak() {
            try {
                this.loading = true;
                this.error = null;

                // Инициализация Keycloak
                const Keycloak = await initKeycloak();
                this.keycloak = new Keycloak(getKeycloakConfig());

                // Обработчики событий
                this.keycloak.onTokenExpired = () => {
                    console.log('Token expired, refreshing...');
                    this.keycloak.updateToken(70).then((refreshed) => {
                        if (refreshed) {
                            this.token = this.keycloak.token;
                            console.log('Token refreshed successfully');
                        }
                    }).catch(() => {
                        console.error('Failed to refresh token');
                        this.logout();
                    });
                };

                this.keycloak.onAuthLogout = () => {
                    console.log('User logged out');
                    this.authenticated = false;
                    this.user = null;
                    this.token = null;
                };

                // Инициализация
                const authenticated = await this.keycloak.init({
                    onLoad: 'check-sso',
                    silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
                    checkLoginIframe: false,
                    enableLogging: import.meta.env.DEV
                });

                if (authenticated) {
                    this.authenticated = true;
                    this.token = this.keycloak.token;
                    this.user = {
                        id: this.keycloak.subject,
                        username: this.keycloak.tokenParsed?.preferred_username,
                        email: this.keycloak.tokenParsed?.email,
                        firstName: this.keycloak.tokenParsed?.given_name,
                        lastName: this.keycloak.tokenParsed?.family_name,
                        roles: this.keycloak.tokenParsed?.realm_access?.roles || []
                    };
                    console.log('User authenticated:', this.user);
                } else {
                    console.log('User not authenticated');
                }

                this.loading = false;
                return authenticated;
            } catch (error) {
                console.error('Keycloak initialization failed:', error);
                this.error = error.message;
                this.loading = false;
                return false;
            }
        },

        async login() {
            if (this.keycloak) {
                await this.keycloak.login({
                    redirectUri: window.location.origin
                });
            }
        },

        async logout() {
            if (this.keycloak) {
                await this.keycloak.logout({
                    redirectUri: window.location.origin
                });
            }
        },

        async updateToken() {
            if (this.keycloak) {
                try {
                    const refreshed = await this.keycloak.updateToken(70);
                    if (refreshed) {
                        this.token = this.keycloak.token;
                        return true;
                    }
                } catch (error) {
                    console.error('Failed to refresh token:', error);
                    this.logout();
                }
            }
            return false;
        },

        hasRole(role) {
            if (!this.user || !this.user.roles) {
                return false;
            }
            return this.user.roles.includes(role);
        },

        hasAnyRole(roles) {
            if (!this.user || !this.user.roles) {
                return false;
            }
            return roles.some(role => this.user.roles.includes(role));
        }
    }
});
