{"name": "sakai-vue", "version": "4.3.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "generate-proto": "npm run generate-proto-js && npm run generate-proto-ts", "generate-proto-js": "grpc_tools_node_protoc --js_out=import_style=commonjs,binary:./src/generated --proto_path=./proto ./proto/*.proto", "generate-proto-ts": "grpc_tools_node_protoc --plugin=protoc-gen-ts=./node_modules/.bin/protoc-gen-ts --ts_out=service=grpc-node:./src/generated --proto_path=./proto ./proto/*.proto"}, "dependencies": {"@primeuix/themes": "^1.0.0", "chart.js": "3.3.2", "google-protobuf": "^3.21.2", "grpc-web": "^1.5.0", "keycloak-js": "^22.0.5", "pinia": "^2.1.0", "primeicons": "^7.0.0", "primevue": "^4.3.1", "tailwindcss-primeui": "^0.5.0", "vue": "^3.4.34", "vue-router": "^4.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.3.1", "@rushstack/eslint-patch": "^1.8.0", "@types/google-protobuf": "^3.15.12", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "grpc_tools_node_protoc_ts": "^5.3.3", "grpc-tools": "^1.12.4", "postcss": "^8.4.40", "prettier": "^3.2.5", "protoc-gen-grpc-web": "^1.5.0", "sass": "^1.55.0", "tailwindcss": "^3.4.6", "typescript": "^5.2.2", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.1"}}