package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.controller.model.ProjectDto
import ru.sbertroika.tkp3.pro.model.OperatorOrganization
import java.util.*

@Component
class ProjectMapper {

    /**
     * Преобразовать Project в ProjectDto
     */
    fun toDto(project: Project): ProjectDto {
        return ProjectDto(
            id = project.id,
            version = project.version,
            versionCreatedAt = project.versionCreatedAt,
            versionCreatedBy = project.versionCreatedBy,
            name = project.name,
            status = project.status,
            contractId = project.contractId,
            description = project.description,
            index = project.index,
            tags = project.tags,
            // Вычисляемые поля заполняются отдельно
            operatorOrganization = null,
            activeTerminals = 0,
            terminals = 0,
            vehicles = 0,
            routes = emptyList(),
            monthlyTransactions = 0,
            monthlyRevenue = 0
        )
    }

    /**
     * Преобразовать ProjectDto в Project
     */
    fun toEntity(dto: ProjectDto): Project {
        return Project(
            id = dto.id,
            version = dto.version,
            versionCreatedAt = dto.versionCreatedAt,
            versionCreatedBy = dto.versionCreatedBy,
            name = dto.name,
            status = dto.status,
            contractId = dto.contractId,
            description = dto.description,
            index = dto.index,
            tags = dto.tags
        )
    }

    /**
     * Заполнить вычисляемые поля в ProjectDto
     */
    fun fillComputedFields(dto: ProjectDto, operatorOrganization: OperatorOrganization? = null): ProjectDto {
        dto.operatorOrganization = operatorOrganization
        // Здесь можно добавить логику для заполнения других вычисляемых полей
        return dto
    }
} 