package ru.sbertroika.tkp3.pro.controller.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.ProductDto
import ru.sbertroika.tkp3.pro.controller.output.ProductRepository
import ru.sbertroika.tkp3.pro.model.Product
import ru.sbertroika.tkp3.pro.model.ProductStatus
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class ProductService(
    private val productRepository: ProductRepository
) {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Получить все продукты для отображения (только последние версии)
     */
    fun getAllProducts(): Flux<ProductDto> {
        return productRepository.findAllLatestVersions()
    }

    /**
     * Получить продукты проекта с пагинацией и фильтрами (только последние версии)
     */
    fun getProductsByProject(
        projectId: UUID,
        page: Int = 0,
        size: Int = 10,
        name: String? = null,
        status: String? = null
    ): Mono<Map<String, Any>> {
        val offset = page * size

        return Mono.zip(
            productRepository.findByProjectIdWithFilters(
                projectId, name, status, size, offset
            ).collectList(),
            productRepository.countByProjectIdWithFilters(projectId, name, status)
        ).map { result ->
            val products = result.t1
            val total = result.t2
            val totalPages = if (total > 0) ((total - 1) / size) + 1 else 0

            mapOf(
                "content" to products,
                "pagination" to mapOf(
                    "page" to page,
                    "size" to size,
                    "totalElements" to total,
                    "totalPages" to totalPages
                )
            )
        }
    }

    /**
     * Получить продукт по ID (последняя версия)
     */
    fun getProductById(id: UUID): Mono<ProductDto> {
        return productRepository.findLatestVersionById(id)
    }

    /**
     * Получить продукты по списку ID (последние версии)
     */
    fun getProductsByIds(ids: List<UUID>): Flux<ProductDto> {
        return productRepository.findLatestVersionsByIds(ids)
    }

    /**
     * Создать продукт
     */
    fun createProduct(projectId: UUID, product: Product, createdBy: UUID): Mono<Product> {
        logger.info("Создание продукта: name=${product.name}")
        val now = Timestamp.valueOf(LocalDateTime.now())
        val newProduct = Product(
            id = UUID.randomUUID(),
            version = 1,
            versionCreatedAt = now,
            versionCreatedBy = createdBy,
            projectId = projectId,
            name = product.name,
            status = ProductStatus.ACTIVE.name,
            tags = product.tags ?: ""
        )

        return productRepository.save(newProduct)
            .doOnSuccess { logger.info("Продукт создан: id=${it.id}") }
            .doOnError { logger.error("Ошибка создания продукта: ${it.message}") }
    }

    /**
     * Обновить продукт (создать новую версию)
     */
    fun updateProduct(id: UUID, product: Product, updatedBy: UUID): Mono<Product> {
        logger.info("Обновление продукта: id=$id, name=${product.name}")
        
        return productRepository.findLatestVersionById(id)
            .flatMap { existingProduct ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                val newVersion = existingProduct.version!! + 1
                
                val updatedProduct = Product(
                    id = id,
                    version = newVersion,
                    versionCreatedAt = now,
                    versionCreatedBy = updatedBy,
                    projectId = existingProduct.projectId,
                    name = product.name,
                    status = product.status ?: existingProduct.status,
                    tags = product.tags ?: existingProduct.tags
                )

                productRepository.save(updatedProduct)
                    .doOnSuccess { logger.info("Продукт обновлен: id=${it.id}, version=${it.version}") }
                    .doOnError { logger.error("Ошибка обновления продукта: ${it.message}") }
            }
            .switchIfEmpty(Mono.error(IllegalArgumentException("Продукт с id=$id не найден")))
    }

    /**
     * Удалить продукт (создать новую версию со статусом IS_DELETED)
     */
    fun deleteProduct(id: UUID, deletedBy: UUID): Mono<Product> {
        logger.info("Удаление продукта: id=$id")
        
        return productRepository.findLatestVersionById(id)
            .flatMap { existingProduct ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                val newVersion = existingProduct.version!! + 1
                
                val deletedProduct = Product(
                    id = id,
                    version = newVersion,
                    versionCreatedAt = now,
                    versionCreatedBy = deletedBy,
                    projectId = existingProduct.projectId,
                    name = existingProduct.name,
                    status = ProductStatus.IS_DELETED.name,
                    tags = existingProduct.tags
                )

                productRepository.save(deletedProduct)
                    .doOnSuccess { logger.info("Продукт удален: id=${it.id}, version=${it.version}") }
                    .doOnError { logger.error("Ошибка удаления продукта: ${it.message}") }
            }
            .switchIfEmpty(Mono.error(IllegalArgumentException("Продукт с id=$id не найден")))
    }
} 