package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.ParticipantOrganization
import java.util.*

@Repository
interface ParticipantOrganizationRepository : ReactiveCrudRepository<ParticipantOrganization, UUID> {

    /**
     * Получить все организации-участники проекта
     */
    @Query("""
        SELECT * FROM participant_organization 
        WHERE po_project_id = :projectId
        ORDER BY po_created_at
    """)
    fun findByProjectId(projectId: UUID): Flux<ParticipantOrganization>

    /**
     * Подсчитать количество уникальных организаций в проекте
     */
    @Query("""
        SELECT COUNT(DISTINCT po_organization_id) 
        FROM participant_organization 
        WHERE po_project_id = :projectId
    """)
    fun countUniqueOrganizationsByProjectId(projectId: UUID): Mono<Long>
} 