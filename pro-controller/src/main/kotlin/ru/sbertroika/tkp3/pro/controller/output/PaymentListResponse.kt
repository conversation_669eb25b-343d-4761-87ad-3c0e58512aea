package ru.sbertroika.tkp3.pro.controller.output

import ru.sbertroika.tkp3.pro.controller.model.PaymentDto

data class Pagination(
    val page: Int,
    val limit: Int,
    val sortField: String? = null,
    val sortOrder: String? = null,
    val totalPage: Long? = null,
    val totalCount: Long? = null
)

data class PaymentListResponse(
    val payments: List<PaymentDto>,
    val pagination: Pagination
)

data class PaymentStatsResponse(
    val totalPayments: Long,
    val totalAmount: Float,
    val organizationsCount: Long,
    val routesCount: Long
)

data class PaymentMethodStatsResponse(
    val cardPayments: Long,
    val cashPayments: Long,
    val mobilePayments: Long,
    val qrPayments: Long
)

data class PaymentPeriodStatsResponse(
    val todayAmount: Float,
    val yesterdayAmount: Float,
    val weekAmount: Float,
    val monthAmount: Float
) 