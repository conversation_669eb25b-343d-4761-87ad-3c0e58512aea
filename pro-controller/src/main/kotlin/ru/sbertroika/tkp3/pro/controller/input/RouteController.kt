package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.RouteService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.model.Route
import ru.sbertroika.tkp3.pro.model.RouteStation
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class RouteController(
    private val routeService: RouteService
) : BaseController() {

    @GetMapping("/routes")
    fun getRoutes(
        @RequestParam(required = false) projectId: String?,
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) scheme: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(projectId?.let { UUID.fromString(it) })
                logUserAction("запрашивает список маршрутов", effectiveProjectId?.toString() ?: "всех проектов")
                
                Mono.zip(
                    routeService.getAllRoutes(effectiveProjectId, status, scheme, page, size).collectList(),
                    routeService.getRoutesCount(effectiveProjectId, status, scheme)
                )
            }.map { result ->
                val routes = result.t1
                val totalCount = result.t2
                val totalPages = (totalCount + size - 1) / size
                val response = mapOf(
                    "content" to routes,
                    "pagination" to mapOf(
                        "page" to page,
                        "size" to size,
                        "totalElements" to totalCount,
                        "totalPages" to totalPages,
                        "hasNext" to (page < totalPages - 1),
                        "hasPrevious" to (page > 0)
                    )
                )
                ApiResponse.success(response)
            }.onErrorResume { error ->
                logError("Ошибка при получении маршрутов: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения маршрутов: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}")
    fun getRouteById(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { route ->
                        if (tokenProjectId.isPresent && route.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к маршруту из другого проекта", 
                                RuntimeException("Route projectId: ${route.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Route>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            Mono.just(route)
                        }
                    }
            }
            .map { route ->
                ApiResponse.success(route)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка получения маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/versions")
    fun getAllVersionsByRouteId(@PathVariable id: String): Mono<ApiResponse<List<Route>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getAllVersionsByRouteId(UUID.fromString(id))
                    .collectList()
                    .flatMap { routes ->
                        if (routes.isNotEmpty()) {
                            val firstRoute = routes.first()
                            if (tokenProjectId.isPresent && firstRoute.projectId != tokenProjectId.get()) {
                                logError("Попытка доступа к версиям маршрута из другого проекта", 
                                    RuntimeException("Route projectId: ${firstRoute.projectId}, Token projectId: $tokenProjectId"))
                                Mono.error<List<Route>>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                            } else {
                                Mono.just(routes)
                            }
                        } else {
                            Mono.just(routes)
                        }
                    }
            }
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении версий маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения версий маршрута: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/routes")
    fun getRoutesByProject(@PathVariable projectId: String): Mono<ApiResponse<List<Route>>> {
        logUserAction("запрашивает список маршрутов проекта", projectId)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(UUID.fromString(projectId))
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                routeService.getRoutesByProjectId(effectiveProjectId)
                    .collectList()
            }
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении маршрутов проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов проекта: ${error.message}"))
            }
    }

    @GetMapping("/routes/status/{status}")
    fun getRoutesByStatus(@PathVariable status: String): Mono<ApiResponse<List<Route>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRoutesByStatus(status)
                    .collectList()
                    .flatMap { routes ->
                        if (tokenProjectId.isPresent) {
                            val filteredRoutes = routes.filter { it.projectId == tokenProjectId.get() }
                            Mono.just(filteredRoutes)
                        } else {
                            Mono.just(routes)
                        }
                    }
            }
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении маршрутов по статусу: ${error.message}", error)
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов по статусу: ${error.message}"))
            }
    }

    @GetMapping("/routes/scheme/{scheme}")
    fun getRoutesByScheme(@PathVariable scheme: String): Mono<ApiResponse<List<Route>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRoutesByScheme(scheme)
                    .collectList()
                    .flatMap { routes ->
                        if (tokenProjectId.isPresent) {
                            val filteredRoutes = routes.filter { it.projectId == tokenProjectId.get() }
                            Mono.just(filteredRoutes)
                        } else {
                            Mono.just(routes)
                        }
                    }
            }
            .map { routes ->
                ApiResponse.success(routes)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении маршрутов по схеме: ${error.message}", error)
                Mono.just(ApiResponse.error<List<Route>>("Ошибка получения маршрутов по схеме: ${error.message}"))
            }
    }

    @PostMapping("/routes")
    fun createRoute(@RequestBody route: Route): Mono<ApiResponse<Route>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                if (tokenProjectId.isPresent) {
                    val routeWithProjectId = route.copy(projectId = tokenProjectId.get())
                    routeService.createRoute(routeWithProjectId, currentUserId)
                } else {
                    routeService.createRoute(route, currentUserId)
                }
            }
            .map { createdRoute ->
                logUserAction("успешно создал маршрут", createdRoute.name ?: "Без названия")
                ApiResponse.success(createdRoute)
            }
            .onErrorResume { error ->
                logError("Ошибка при создании маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка создания маршрута: ${error.message}"))
            }
    }

    @PutMapping("/routes/{id}")
    fun updateRoute(@PathVariable id: String, @RequestBody route: Route): Mono<ApiResponse<Route>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Route>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            val projectIdForUpdate = existingRoute.projectId ?: 
                                throw RuntimeException("Маршрут не привязан к проекту")
                            routeService.updateRoute(UUID.fromString(id), route.copy(projectId = projectIdForUpdate), currentUserId)
                        }
                    }
            }
            .map { updatedRoute ->
                ApiResponse.success(updatedRoute)
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка обновления маршрута: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/{id}")
    fun deleteRoute(@PathVariable id: String): Mono<ApiResponse<String>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.deleteRoute(UUID.fromString(id))
                        }
                    }
            }
            .map {
                ApiResponse.success("Маршрут успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/activate")
    fun activateRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка активации маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Route>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.activateRoute(UUID.fromString(id))
                        }
                    }
            }
            .map { route ->
                ApiResponse.success(route, "Маршрут активирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при активации маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка активации маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/deactivate")
    fun deactivateRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка деактивации маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Route>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.deactivateRoute(UUID.fromString(id))
                        }
                    }
            }
            .map { route ->
                ApiResponse.success(route, "Маршрут деактивирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при деактивации маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка деактивации маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/block")
    fun blockRoute(@PathVariable id: String): Mono<ApiResponse<Route>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка блокировки маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Route>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.blockRoute(UUID.fromString(id))
                        }
                    }
            }
            .map { route ->
                ApiResponse.success(route, "Маршрут заблокирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при блокировке маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Route>("Ошибка блокировки маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/stations")
    fun getRouteStations(@PathVariable id: String): Mono<ApiResponse<List<RouteStation>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к станциям маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.getRouteStations(UUID.fromString(id))
                                .collectList()
                        }
                    }
            }
            .map { stations ->
                ApiResponse.success(stations)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении станций маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<List<RouteStation>>("Ошибка получения станций маршрута: ${error.message}"))
            }
    }

    @GetMapping("/routes/{id}/stations/info")
    fun getRouteStationInfo(
        @PathVariable id: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "50") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        val routeId = UUID.fromString(id)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                routeService.getRouteById(routeId)
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к информации о станциях маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            return@flatMap Mono.error(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            Mono.zip(
                                routeService.getRouteStationInfo(routeId, page, size).collectList(),
                                routeService.getRouteStationInfoCount(routeId)
                            )
                        }
                    }
            }.map { res ->
                val totalPages = (res.t2 + size - 1) / size
                val response = mapOf<String, Any>(
                    "content" to res.t1,
                    "pagination" to mapOf<String, Any>(
                        "page" to page,
                        "size" to size,
                        "totalElements" to res.t2,
                        "totalPages" to totalPages,
                        "hasNext" to (page < totalPages - 1),
                        "hasPrevious" to (page > 0)
                    )
                )
                ApiResponse.success(response)
            }.onErrorResume { error ->
                logError("Ошибка при получении информации о станциях маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error("Ошибка получения информации о станциях маршрута: ${error.message}"))
            }
    }

    @PostMapping("/routes/{id}/stations")
    fun addStationToRoute(
        @PathVariable id: String,
        @RequestBody request: Map<String, String>
    ): Mono<ApiResponse<RouteStation>> {
        val stationId = UUID.fromString(request["stationId"] ?: "")
        
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка добавления станции к маршруту из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<RouteStation>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.addStationToRoute(UUID.fromString(id), stationId, currentUserId)
                        }
                    }
            }
            .map { routeStation ->
                ApiResponse.success(routeStation, "Станция добавлена к маршруту")
            }
            .onErrorResume { error ->
                logError("Ошибка при добавлении станции к маршруту: ${error.message}", error)
                Mono.just(ApiResponse.error<RouteStation>("Ошибка добавления станции к маршруту: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/stations/{stationId}")
    fun removeStationFromRoute(@PathVariable stationId: String): Mono<ApiResponse<String>> {
        return JwtUtils.getCurrentUserId()
            .flatMap { currentUserId ->
                routeService.removeStationFromRoute(UUID.fromString(stationId), currentUserId)
            }
            .map {
                ApiResponse.success("Станция удалена из маршрута")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении станции из маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления станции из маршрута: ${error.message}"))
            }
    }

    @PutMapping("/routes/{id}/stations/positions")
    fun updateStationPositions(
        @PathVariable id: String,
        @RequestBody stationPositions: Map<String, Int>
    ): Mono<ApiResponse<String>> {
        val positions = stationPositions.mapKeys { UUID.fromString(it.key) }
        
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                routeService.getRouteById(UUID.fromString(id))
                    .flatMap { existingRoute ->
                        if (tokenProjectId.isPresent && existingRoute.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления позиций станций маршрута из другого проекта", 
                                RuntimeException("Route projectId: ${existingRoute.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: маршрут принадлежит другому проекту"))
                        } else {
                            routeService.updateStationPositions(UUID.fromString(id), positions, currentUserId)
                        }
                    }
            }
            .map {
                ApiResponse.success("Позиции станций обновлены")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении позиций станций: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка обновления позиций станций: ${error.message}"))
            }
    }

    @GetMapping("/routes/check-number")
    fun checkRouteNumberUnique(
        @RequestParam projectId: String,
        @RequestParam number: String,
        @RequestParam(required = false) excludeId: String?
    ): Mono<ApiResponse<Boolean>> {
        val requestProjectUuid = UUID.fromString(projectId)
        val excludeUuid = excludeId?.let { UUID.fromString(it) }
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(requestProjectUuid)
                
                if (tokenProjectId.isPresent && requestProjectUuid != tokenProjectId.get()) {
                    logError("Попытка проверки уникальности номера маршрута в другом проекте", 
                        RuntimeException("Request projectId: $requestProjectUuid, Token projectId: $tokenProjectId"))
                    Mono.error<Boolean>(RuntimeException("Доступ запрещен: нельзя проверять уникальность в другом проекте"))
                } else {
                    routeService.isRouteNumberUnique(effectiveProjectId, number, excludeUuid)
                }
            }
            .map { isUnique ->
                ApiResponse.success(isUnique)
            }
            .onErrorResume { error ->
                logError("Ошибка при проверке уникальности номера маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<Boolean>("Ошибка проверки уникальности номера маршрута: ${error.message}"))
            }
    }
} 