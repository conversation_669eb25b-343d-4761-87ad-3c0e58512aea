package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.PaymentDto
import ru.sbertroika.tkp3.pro.controller.model.PaymentMethodDto
import ru.sbertroika.tkp3.pro.controller.output.*
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.PaymentFilterRequest
import ru.sbertroika.tkp3.pro.controller.service.PaymentService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import java.time.ZonedDateTime
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class PaymentController(
    private val paymentService: PaymentService
) : BaseController() {

    @GetMapping("/payments")
    fun getPayments(
        @RequestParam projectId: String,
        @RequestParam(required = false) createdAtFrom: String?,
        @RequestParam(required = false) createdAtTo: String?,
        @RequestParam(required = false) organizationId: String?,
        @RequestParam(required = false) routeId: String?,
        @RequestParam(required = false) ticketId: String?,
        @RequestParam(required = false) ticketSeries: String?,
        @RequestParam(required = false) ticketNumber: String?,
        @RequestParam(required = false) payMethod: String?,
        @RequestParam(required = false) trxId: String?,
        @RequestParam(required = false) terminalSerial: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") limit: Int,
        @RequestParam(defaultValue = "createdAt") sortField: String,
        @RequestParam(defaultValue = "desc") sortOrder: String
    ): Mono<ApiResponse<PaymentListResponse>> {
        logUserAction("запрашивает платежи проекта", projectId)

        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val filter = PaymentFilterRequest(
                    projectId = tokenProjectId.getOrDefault(UUID.fromString(projectId)),
                    createdAtFrom = createdAtFrom?.let { ZonedDateTime.parse(it) },
                    createdAtTo = createdAtTo?.let { ZonedDateTime.parse(it) },
                    organizationId = organizationId?.let { UUID.fromString(it) },
                    routeId = routeId?.let { UUID.fromString(it) },
                    ticketId = ticketId,
                    ticketSeries = ticketSeries,
                    ticketNumber = ticketNumber,
                    payMethod = payMethod,
                    trxId = trxId,
                    terminalSerial = terminalSerial
                )

                val pagination = Pagination(
                    page = page,
                    limit = limit,
                    sortField = sortField,
                    sortOrder = sortOrder
                )

                paymentService.getPaymentList(filter, pagination)
            }
            .map { response ->
                ApiResponse.success(response)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении платежей: ${error.message}", error)
                Mono.just(ApiResponse.error<PaymentListResponse>("Ошибка получения платежей: ${error.message}"))
            }
    }

    @GetMapping("/payments/examples")
    fun getPaymentExamples(
        @RequestParam projectId: String,
        @RequestParam(defaultValue = "20") count: Int
    ): Mono<ApiResponse<List<PaymentDto>>> {
        logUserAction("запрашивает примеры платежей проекта", projectId)
        
        return paymentService.getPaymentExamples(UUID.fromString(projectId), count)
            .map { examples ->
                ApiResponse.success(examples)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении примеров платежей: ${error.message}", error)
                Mono.just(ApiResponse.error<List<PaymentDto>>("Ошибка получения примеров платежей: ${error.message}"))
            }
    }

    @GetMapping("/payment-methods")
    fun getPaymentMethods(): Mono<ApiResponse<List<PaymentMethodDto>>> {
        logUserAction("запрашивает способы оплаты")
        
        return paymentService.getPaymentMethods()
            .map { methods ->
                ApiResponse.success(methods)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении способов оплаты: ${error.message}", error)
                Mono.just(ApiResponse.error<List<PaymentMethodDto>>("Ошибка получения способов оплаты: ${error.message}"))
            }
    }

    @GetMapping("/payments/stats")
    fun getPaymentStats(@RequestParam projectId: String): Mono<ApiResponse<PaymentStatsResponse>> {
        logUserAction("запрашивает статистику платежей проекта", projectId)
        
        return paymentService.getPaymentStats(UUID.fromString(projectId))
            .map { stats ->
                ApiResponse.success(stats)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении статистики платежей: ${error.message}", error)
                Mono.just(ApiResponse.error<PaymentStatsResponse>("Ошибка получения статистики платежей: ${error.message}"))
            }
    }

    @GetMapping("/payments/stats/methods")
    fun getPaymentMethodStats(@RequestParam projectId: String): Mono<ApiResponse<PaymentMethodStatsResponse>> {
        logUserAction("запрашивает статистику по способам оплаты проекта", projectId)
        
        return paymentService.getPaymentMethodStats(UUID.fromString(projectId))
            .map { stats ->
                ApiResponse.success(stats)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении статистики по способам оплаты: ${error.message}", error)
                Mono.just(ApiResponse.error<PaymentMethodStatsResponse>("Ошибка получения статистики по способам оплаты: ${error.message}"))
            }
    }

    @GetMapping("/payments/stats/periods")
    fun getPaymentPeriodStats(@RequestParam projectId: String): Mono<ApiResponse<PaymentPeriodStatsResponse>> {
        logUserAction("запрашивает статистику по периодам проекта", projectId)
        
        return paymentService.getPaymentPeriodStats(UUID.fromString(projectId))
            .map { stats ->
                ApiResponse.success(stats)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении статистики по периодам: ${error.message}", error)
                Mono.just(ApiResponse.error<PaymentPeriodStatsResponse>("Ошибка получения статистики по периодам: ${error.message}"))
            }
    }
} 