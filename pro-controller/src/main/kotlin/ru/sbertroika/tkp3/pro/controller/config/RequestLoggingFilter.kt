package ru.sbertroika.tkp3.pro.controller.config

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono

@Component
class RequestLoggingFilter : WebFilter {
    
    private val logger = LoggerFactory.getLogger(RequestLoggingFilter::class.java)
    
    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
        val request = exchange.request
        val path = request.path.toString()
        val method = request.method?.toString() ?: "UNKNOWN"
        
        // Логируем основные детали запроса
        val sb = StringBuilder()
        sb.append("\n=== Входящий запрос ===").append("\n")
        sb.append("Метод: $method").append("\n")
        sb.append("Путь: $path").append("\n")
        sb.append("Query параметры: ${request.queryParams}").append("\n")
        
        // Логируем Content-Type
        val contentType = request.headers.contentType
        if (contentType != null) {
            sb.append("Content-Type: $contentType").append("\n")
        }
        
        // Логируем заголовки (кроме Authorization)
//        sb.append("Заголовки:").append("\n")
//        request.headers.forEach { (name, values) ->
//            if (name.equals("authorization", ignoreCase = true)) {
//                // Маскируем JWT токен
//                values.forEach { value ->
//                    val maskedValue = if (value.startsWith("Bearer ")) {
//                        "Bearer ${value.substring(7).take(10)}..."
//                    } else {
//                        "***"
//                    }
//                    sb.append("  $name: $maskedValue").append("\n")
//                }
//            } else {
//                sb.append("  $name: $values").append("\n")
//            }
//        }
        
        // Проверяем наличие Authorization заголовка
        val authHeader = request.headers.getFirst("Authorization")
        if (authHeader == null) {
            if (method == "OPTIONS") {
                sb.append("ℹ️  OPTIONS запрос (CORS preflight) - Authorization не требуется").append("\n")
            } else {
                sb.append("⚠️  Отсутствует заголовок Authorization для запроса $method $path").append("\n")
            }
        } else {
            sb.append("✅ Заголовок Authorization присутствует").append("\n")
        }

        // Добавляем информацию о теле запроса
//        val shouldHaveBody = (method == "POST" || method == "PUT" || method == "PATCH") &&
//                contentType != null &&
//                (contentType.isCompatibleWith(MediaType.APPLICATION_JSON) ||
//                 contentType.isCompatibleWith(MediaType.APPLICATION_FORM_URLENCODED) ||
//                 contentType.isCompatibleWith(MediaType.TEXT_PLAIN))
//
//        if (shouldHaveBody) {
//            sb.append("📝 Запрос содержит тело (будет логировано в контроллере)").append("\n")
//        }

        sb.append("=====================")
        logger.info(sb.toString())
        
        return chain.filter(exchange)
    }
} 