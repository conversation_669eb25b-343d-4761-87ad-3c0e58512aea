package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.util.*

@Repository
interface VehicleRepository : ReactiveCrudRepository<Vehicle, UUID> {

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией (только последние версии)
     */
    @Query("""
        SELECT v.* FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE v_project_id = :projectId 
            AND (:type IS NULL OR vh_type = :type)
            AND (:status IS NULL OR vh_status = :status)
            AND (:organizationId IS NULL OR v_organization_id = :organizationId)
            AND (:search IS NULL OR vh_number ILIKE CONCAT('%', :search, '%'))
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.v_project_id = :projectId 
        AND v.vh_status != 'IS_DELETED'
        AND (:type IS NULL OR v.vh_type = :type)
        AND (:status IS NULL OR v.vh_status = :status)
        AND (:organizationId IS NULL OR v.v_organization_id = :organizationId)
        AND (:search IS NULL OR v.vh_number ILIKE CONCAT('%', :search, '%'))
        ORDER BY v.vh_number
        LIMIT :size OFFSET :offset
    """)
    fun findAllVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null,
        size: Int,
        offset: Int
    ): Flux<Vehicle>

    /**
     * Подсчитать количество транспортных средств с фильтрацией (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT v.vh_id) FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE v_project_id = :projectId 
            AND (:type IS NULL OR vh_type = :type)
            AND (:status IS NULL OR vh_status = :status)
            AND (:organizationId IS NULL OR v_organization_id = :organizationId)
            AND (:search IS NULL OR vh_number ILIKE CONCAT('%', :search, '%'))
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.v_project_id = :projectId 
        AND v.vh_status != 'IS_DELETED'
        AND (:type IS NULL OR v.vh_type = :type)
        AND (:status IS NULL OR v.vh_status = :status)
        AND (:organizationId IS NULL OR v.v_organization_id = :organizationId)
        AND (:search IS NULL OR v.vh_number ILIKE CONCAT('%', :search, '%'))
    """)
    fun countVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null
    ): Mono<Long>

    /**
     * Получить транспортное средство по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_id = :vehicleId 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_version DESC 
        LIMIT 1
    """)
    fun findVehicleById(vehicleId: UUID): Mono<Vehicle>

    /**
     * Получить все версии транспортного средства
     */
    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_id = :vehicleId 
        ORDER BY vh_version DESC
    """)
    fun findAllVersionsByVehicleId(vehicleId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по проекту (только последние версии)
     */
    @Query("""
        SELECT v.* FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE v_project_id = :projectId 
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.v_project_id = :projectId 
        AND v.vh_status != 'IS_DELETED'
        ORDER BY v.vh_number
    """)
    fun findByProjectId(projectId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по организации (только последние версии)
     */
    @Query("""
        SELECT v.* FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE v_organization_id = :organizationId 
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.v_organization_id = :organizationId 
        AND v.vh_status != 'IS_DELETED'
        ORDER BY v.vh_number
    """)
    fun findByOrganizationId(organizationId: UUID): Flux<Vehicle>

    /**
     * Получить транспортные средства по типу (только последние версии)
     */
    @Query("""
        SELECT v.* FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE vh_type = :type 
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.vh_type = :type 
        AND v.vh_status != 'IS_DELETED'
        ORDER BY v.vh_number
    """)
    fun findByType(type: VehicleType): Flux<Vehicle>

    /**
     * Подсчитать количество транспортных средств в проекте (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT v.vh_id) FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) as max_version
            FROM vehicle
            WHERE v_project_id = :projectId 
            GROUP BY vh_id
        ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
        WHERE v.v_project_id = :projectId 
        AND v.vh_status != 'IS_DELETED'
    """)
    fun countVehiclesByProjectId(projectId: UUID): Mono<Long>

    /**
     * Получить транспортные средства по статусу (только последние версии)
     */
    @Query("""
        SELECT DISTINCT ON (vh_id) * FROM vehicle 
        WHERE vh_status = :status 
        ORDER BY vh_id, vh_version DESC
    """)
    fun findByStatus(status: VehicleStatus): Flux<Vehicle>

    /**
     * Проверить существование транспортного средства по номеру в проекте (только последние версии)
     */
    @Query("""
        SELECT EXISTS(
            SELECT 1 FROM vehicle v
            INNER JOIN (
                SELECT vh_id, MAX(vh_version) as max_version
                FROM vehicle
                WHERE v_project_id = :projectId 
                GROUP BY vh_id
            ) latest ON v.vh_id = latest.vh_id AND v.vh_version = latest.max_version
            WHERE v.v_project_id = :projectId 
            AND v.vh_number = :number 
            AND v.vh_status != 'IS_DELETED'
        )
    """)
    fun existsByProjectIdAndNumber(projectId: UUID, number: String): Mono<Boolean>
} 