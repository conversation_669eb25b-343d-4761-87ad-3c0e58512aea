package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.VehicleCreateRequest
import ru.sbertroika.tkp3.pro.controller.model.VehicleDto
import ru.sbertroika.tkp3.pro.controller.model.VehicleUpdateRequest
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.VehicleService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1/vehicles")
class VehicleController(
    private val vehicleService: VehicleService
) : BaseController() {

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    @GetMapping
    fun getVehicles(
        @RequestParam projectId: UUID,
        @RequestParam(required = false) type: VehicleType? = null,
        @RequestParam(required = false) status: VehicleStatus? = null,
        @RequestParam(required = false) organizationId: UUID? = null,
        @RequestParam(required = false) search: String? = null,
        @RequestParam(defaultValue = "0") page: Int = 0,
        @RequestParam(defaultValue = "10") size: Int = 10
    ): Mono<ResponseEntity<ApiResponse<Map<String, Any>>>> {
        logUserAction("запрашивает список транспортных средств проекта", projectId.toString())
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(projectId)
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                vehicleService.getVehiclesWithFilters(
                    projectId = effectiveProjectId,
                    type = type,
                    status = status,
                    organizationId = organizationId,
                    search = search,
                    page = page,
                    size = size
                )
            }.map { result ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = result,
                    message = "Транспортные средства получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортных средств: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<Map<String, Any>>(
                    success = false,
                    message = "Ошибка получения транспортных средств: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортное средство по ID
     */
    @GetMapping("/{id}")
    fun getVehicleById(@PathVariable id: UUID): Mono<ResponseEntity<ApiResponse<VehicleDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                vehicleService.getVehicleById(id)
                    .flatMap { vehicle ->
                        if (tokenProjectId.isPresent && vehicle.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к транспортному средству из другого проекта", 
                                RuntimeException("Vehicle projectId: ${vehicle.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<VehicleDto>(RuntimeException("Доступ запрещен: транспортное средство принадлежит другому проекту"))
                        } else {
                            vehicleService.getVehicleDtoById(id)
                        }
                    }
            }.map { vehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicle,
                    message = "Транспортное средство получено успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортного средства: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<VehicleDto>(
                    success = false,
                    message = "Ошибка получения транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Получить все версии транспортного средства
     */
    @GetMapping("/{id}/versions")
    fun getVehicleVersions(@PathVariable id: UUID): Mono<ResponseEntity<ApiResponse<List<VehicleDto>>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                vehicleService.getVehicleById(id)
                    .flatMap { vehicle ->
                        if (tokenProjectId.isPresent && vehicle.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к версиям транспортного средства из другого проекта", 
                                RuntimeException("Vehicle projectId: ${vehicle.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<List<VehicleDto>>(RuntimeException("Доступ запрещен: транспортное средство принадлежит другому проекту"))
                        } else {
                            vehicleService.getAllVersionsDtoByVehicleId(id)
                                .collectList()
                        }
                    }
            }.map { versions ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = versions,
                    message = "Версии транспортного средства получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении версий транспортного средства: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<VehicleDto>>(
                    success = false,
                    message = "Ошибка получения версий транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Создать новое транспортное средство
     */
    @PostMapping
    fun createVehicle(
        @RequestBody request: VehicleCreateRequest
    ): Mono<ResponseEntity<ApiResponse<VehicleDto>>> {
        logUserAction("создает новое транспортное средство", request.projectId.toString())
        
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                if (tokenProjectId.isPresent && request.projectId != tokenProjectId.get()) {
                    logError("Попытка создания транспортного средства в другом проекте", 
                        RuntimeException("Request projectId: ${request.projectId}, Token projectId: $tokenProjectId"))
                    Mono.error<VehicleDto>(RuntimeException("Доступ запрещен: нельзя создавать транспортное средство в другом проекте"))
                } else {
                    val effectiveProjectId = tokenProjectId.getOrDefault(request.projectId)
                    val effectiveRequest = request.copy(projectId = effectiveProjectId)
                    vehicleService.createVehicleFromDto(effectiveRequest, currentUserId)
                }
            }.map { createdVehicle ->
                logUserAction("успешно создал транспортное средство", createdVehicle.number ?: "Без номера")
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = createdVehicle,
                    message = "Транспортное средство создано успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при создании транспортного средства: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<VehicleDto>(
                    success = false,
                    message = "Ошибка создания транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Обновить транспортное средство
     */
    @PutMapping("/{id}")
    fun updateVehicle(
        @PathVariable id: UUID,
        @RequestBody request: VehicleUpdateRequest
    ): Mono<ResponseEntity<ApiResponse<VehicleDto>>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                vehicleService.getVehicleById(id)
                    .flatMap { existingVehicle ->
                        if (tokenProjectId.isPresent && existingVehicle.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления транспортного средства из другого проекта", 
                                RuntimeException("Vehicle projectId: ${existingVehicle.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<VehicleDto>(RuntimeException("Доступ запрещен: транспортное средство принадлежит другому проекту"))
                        } else {
                            vehicleService.updateVehicleFromDto(id, request, currentUserId)
                        }
                    }
            }.map { updatedVehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = updatedVehicle,
                    message = "Транспортное средство обновлено успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при обновлении транспортного средства: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<VehicleDto>(
                    success = false,
                    message = "Ошибка обновления транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Удалить транспортное средство (логическое удаление)
     */
    @DeleteMapping("/{id}")
    fun deleteVehicle(@PathVariable id: UUID): Mono<ResponseEntity<ApiResponse<VehicleDto>>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                vehicleService.getVehicleById(id)
                    .flatMap { existingVehicle ->
                        if (tokenProjectId.isPresent && existingVehicle.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления транспортного средства из другого проекта", 
                                RuntimeException("Vehicle projectId: ${existingVehicle.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<VehicleDto>(RuntimeException("Доступ запрещен: транспортное средство принадлежит другому проекту"))
                        } else {
                            vehicleService.deleteVehicle(id, currentUserId)
                                .flatMap { vehicleService.getVehicleDtoById(id) }
                        }
                    }
            }.map { deletedVehicle ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = deletedVehicle,
                    message = "Транспортное средство удалено успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при удалении транспортного средства: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<VehicleDto>(
                    success = false,
                    message = "Ошибка удаления транспортного средства: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по проекту
     */
    @GetMapping("/project/{projectId}")
    fun getVehiclesByProject(@PathVariable projectId: UUID): Mono<ResponseEntity<ApiResponse<List<VehicleDto>>>> {
        logUserAction("запрашивает транспортные средства проекта", projectId.toString())
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(projectId)
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                vehicleService.getVehiclesDtoByProject(effectiveProjectId)
                    .collectList()
            }.map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства проекта получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортных средств проекта: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<VehicleDto>>(
                    success = false,
                    message = "Ошибка получения транспортных средств проекта: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по организации
     */
    @GetMapping("/organization/{organizationId}")
    fun getVehiclesByOrganization(@PathVariable organizationId: UUID): Mono<ResponseEntity<ApiResponse<List<VehicleDto>>>> {
        logUserAction("запрашивает транспортные средства организации", organizationId.toString())
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, фильтруем результаты по проекту
                    logUserAction("фильтрует транспортные средства организации по проекту", tokenProjectId.get().toString())
                    vehicleService.getVehiclesDtoByOrganization(organizationId)
                        .filter { vehicle -> vehicle.projectId == tokenProjectId.get() }
                        .collectList()
                } else {
                    // Если нет projectId в токене, возвращаем все транспортные средства организации
                    logUserAction("получает все транспортные средства организации без фильтрации по проекту")
                    vehicleService.getVehiclesDtoByOrganization(organizationId)
                        .collectList()
                }
            }.map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства организации получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортных средств организации: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<VehicleDto>>(
                    success = false,
                    message = "Ошибка получения транспортных средств организации: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по типу
     */
    @GetMapping("/type/{type}")
    fun getVehiclesByType(@PathVariable type: VehicleType): Mono<ResponseEntity<ApiResponse<List<VehicleDto>>>> {
        logUserAction("запрашивает транспортные средства по типу", type.name)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, фильтруем результаты по проекту
                    logUserAction("фильтрует транспортные средства по типу и проекту", "${type.name} - ${tokenProjectId.get()}")
                    vehicleService.getVehiclesDtoByType(type)
                        .filter { vehicle -> vehicle.projectId == tokenProjectId.get() }
                        .collectList()
                } else {
                    // Если нет projectId в токене, возвращаем все транспортные средства по типу
                    logUserAction("получает все транспортные средства по типу без фильтрации по проекту", type.name)
                    vehicleService.getVehiclesDtoByType(type)
                        .collectList()
                }
            }.map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства по типу получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортных средств по типу: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<VehicleDto>>(
                    success = false,
                    message = "Ошибка получения транспортных средств по типу: ${error.message}"
                )))
            }
    }

    /**
     * Получить транспортные средства по статусу
     */
    @GetMapping("/status/{status}")
    fun getVehiclesByStatus(@PathVariable status: VehicleStatus): Mono<ResponseEntity<ApiResponse<List<VehicleDto>>>> {
        logUserAction("запрашивает транспортные средства по статусу", status.name)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, фильтруем результаты по проекту
                    logUserAction("фильтрует транспортные средства по статусу и проекту", "${status.name} - ${tokenProjectId.get()}")
                    vehicleService.getVehiclesDtoByStatus(status)
                        .filter { vehicle -> vehicle.projectId == tokenProjectId.get() }
                        .collectList()
                } else {
                    // Если нет projectId в токене, возвращаем все транспортные средства по статусу
                    logUserAction("получает все транспортные средства по статусу без фильтрации по проекту", status.name)
                    vehicleService.getVehiclesDtoByStatus(status)
                        .collectList()
                }
            }.map { vehicles ->
                ResponseEntity.ok(ApiResponse(
                    success = true,
                    data = vehicles,
                    message = "Транспортные средства по статусу получены успешно"
                ))
            }.onErrorResume { error ->
                logError("Ошибка при получении транспортных средств по статусу: ${error.message}", error)
                Mono.just(ResponseEntity.badRequest().body(ApiResponse<List<VehicleDto>>(
                    success = false,
                    message = "Ошибка получения транспортных средств по статусу: ${error.message}"
                )))
            }
    }
} 