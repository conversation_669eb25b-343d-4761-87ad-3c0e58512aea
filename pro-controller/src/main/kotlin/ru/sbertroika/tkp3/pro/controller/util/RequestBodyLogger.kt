package ru.sbertroika.tkp3.pro.controller.util

import org.slf4j.Logger
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.http.MediaType
import org.springframework.http.server.reactive.ServerHttpRequest
import java.nio.charset.StandardCharsets

/**
 * Утилита для логирования тела запроса в контроллерах
 */
object RequestBodyLogger {
    
    /**
     * Логирует тело запроса для указанного логгера
     */
    fun logRequestBody(request: ServerHttpRequest, logger: Logger, operation: String) {
        val method = request.method?.toString() ?: "UNKNOWN"
        val path = request.path.toString()
        val contentType = request.headers.contentType
        
        val shouldLogBody = (method == "POST" || method == "PUT" || method == "PATCH") &&
                contentType != null &&
                (contentType.isCompatibleWith(MediaType.APPLICATION_JSON) ||
                 contentType.isCompatibleWith(MediaType.APPLICATION_FORM_URLENCODED) ||
                 contentType.isCompatibleWith(MediaType.TEXT_PLAIN))
        
        if (shouldLogBody) {
            DataBufferUtils.join(request.body)
                .subscribe(
                    { dataBuffer ->
                        try {
                            val body = dataBuffer.toString(StandardCharsets.UTF_8)
                            logger.info("📝 Тело запроса для $operation ($method $path):\n$body")
                        } finally {
                            DataBufferUtils.release(dataBuffer)
                        }
                    },
                    { error ->
                        logger.warn("⚠️ Ошибка чтения тела запроса для $operation ($method $path): ${error.message}")
                    }
                )
        }
    }
    
    /**
     * Логирует тело запроса с ограничением длины
     */
    fun logRequestBodyLimited(request: ServerHttpRequest, logger: Logger, operation: String, maxLength: Int = 1000) {
        val method = request.method?.toString() ?: "UNKNOWN"
        val path = request.path.toString()
        val contentType = request.headers.contentType
        
        val shouldLogBody = (method == "POST" || method == "PUT" || method == "PATCH") &&
                contentType != null &&
                (contentType.isCompatibleWith(MediaType.APPLICATION_JSON) ||
                 contentType.isCompatibleWith(MediaType.APPLICATION_FORM_URLENCODED) ||
                 contentType.isCompatibleWith(MediaType.TEXT_PLAIN))
        
        if (shouldLogBody) {
            DataBufferUtils.join(request.body)
                .subscribe(
                    { dataBuffer ->
                        try {
                            val body = dataBuffer.toString(StandardCharsets.UTF_8)
                            val truncatedBody = if (body.length > maxLength) {
                                body.take(maxLength) + "... (обрезано, полная длина: ${body.length})"
                            } else {
                                body
                            }
                            logger.info("📝 Тело запроса для $operation ($method $path):\n$truncatedBody")
                        } finally {
                            DataBufferUtils.release(dataBuffer)
                        }
                    },
                    { error ->
                        logger.warn("⚠️ Ошибка чтения тела запроса для $operation ($method $path): ${error.message}")
                    }
                )
        }
    }
} 