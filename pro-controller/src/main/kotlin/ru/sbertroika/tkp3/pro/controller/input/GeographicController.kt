package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.GeographicService
import ru.sbertroika.tkp3.pro.model.City
import ru.sbertroika.tkp3.pro.model.Country
import ru.sbertroika.tkp3.pro.model.District
import ru.sbertroika.tkp3.pro.model.Region
import java.util.*

@RestController
@RequestMapping("/api/pro/v1/geographic")
class GeographicController(
    private val geographicService: GeographicService
) {

    @GetMapping("/countries")
    fun searchCountries(@RequestParam(required = false, defaultValue = "") name: String): Flux<ApiResponse<Country>> {
        return geographicService.searchCountries(name)
            .map { ApiResponse.success(it) }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<Country>("Ошибка поиска стран: ${error.message}"))
            }
    }

    @GetMapping("/regions")
    fun searchRegions(
        @RequestParam(required = false, defaultValue = "") name: String,
        @RequestParam(required = false) countryId: String?
    ): Flux<ApiResponse<Region>> {
        val countryUUID = countryId?.let { UUID.fromString(it) }
        return geographicService.searchRegions(name, countryUUID)
            .map { ApiResponse.success(it) }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<Region>("Ошибка поиска регионов: ${error.message}"))
            }
    }

    @GetMapping("/cities")
    fun searchCities(
        @RequestParam(required = false, defaultValue = "") name: String,
        @RequestParam(required = false) regionId: String?
    ): Flux<ApiResponse<City>> {
        val regionUUID = regionId?.let { UUID.fromString(it) }
        return geographicService.searchCities(name, regionUUID)
            .map { ApiResponse.success(it) }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<City>("Ошибка поиска городов: ${error.message}"))
            }
    }

    @GetMapping("/districts")
    fun searchDistricts(
        @RequestParam(required = false, defaultValue = "") name: String,
        @RequestParam(required = false) cityId: String?
    ): Flux<ApiResponse<District>> {
        val cityUUID = cityId?.let { UUID.fromString(it) }
        return geographicService.searchDistricts(name, cityUUID)
            .map { ApiResponse.success(it) }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<District>("Ошибка поиска районов: ${error.message}"))
            }
    }

    @PostMapping("/countries")
    fun createCountry(@RequestBody country: Country): Flux<ApiResponse<Country>> {
        return geographicService.findOrCreateCountry(country.name ?: "")
            .flux()
            .map { ApiResponse.success(it, "Страна создана или найдена") }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<Country>("Ошибка создания страны: ${error.message}"))
            }
    }

    @PostMapping("/regions")
    fun createRegion(@RequestBody region: Region): Flux<ApiResponse<Region>> {
        val countryId = region.countryId ?: return Flux.just(ApiResponse.error<Region>("ID страны обязателен"))
        return geographicService.findOrCreateRegion(region.name ?: "", countryId)
            .flux()
            .map { ApiResponse.success(it, "Регион создан или найден") }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<Region>("Ошибка создания региона: ${error.message}"))
            }
    }

    @PostMapping("/cities")
    fun createCity(@RequestBody city: City): Flux<ApiResponse<City>> {
        val regionId = city.regionId ?: return Flux.just(ApiResponse.error<City>("ID региона обязателен"))
        return geographicService.findOrCreateCity(city.name ?: "", regionId)
            .flux()
            .map { ApiResponse.success(it, "Город создан или найден") }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<City>("Ошибка создания города: ${error.message}"))
            }
    }

    @PostMapping("/districts")
    fun createDistrict(@RequestBody district: District): Flux<ApiResponse<District>> {
        val cityId = district.cityId ?: return Flux.just(ApiResponse.error<District>("ID города обязателен"))
        return geographicService.findOrCreateDistrict(district.name ?: "", cityId)
            .flux()
            .map { ApiResponse.success(it, "Район создан или найден") }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<District>("Ошибка создания района: ${error.message}"))
            }
    }
} 