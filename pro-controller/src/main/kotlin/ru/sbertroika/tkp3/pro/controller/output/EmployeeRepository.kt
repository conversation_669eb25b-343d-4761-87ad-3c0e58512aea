package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Employee
import ru.sbertroika.tkp3.pro.model.EmployeePK
import java.util.*

@Repository
interface EmployeeRepository : ReactiveCrudRepository<Employee, EmployeePK> {

    /**
     * Получить сотрудников проекта с фильтрацией и пагинацией (только последние версии)
     */
    @Query("""
        SELECT e.* FROM employee e
        INNER JOIN (
            SELECT e_profile_id, MAX(e_version) as max_version
            FROM employee
            WHERE e_project_id = :projectId 
            AND e_is_deleted = false
            AND (:fullName IS NULL OR CONCAT(e_surname, ' ', e_name, ' ', COALESCE(e_middle_name, '')) ILIKE '%' || :fullName || '%')
            AND (:personalNumber IS NULL OR e_personal_number ILIKE '%' || :personalNumber || '%')
            AND (:organizationId IS NULL OR e_organization_id = :organizationId)
            AND (:role IS NULL OR e_role = :role)
            GROUP BY e_profile_id
        ) latest ON e.e_profile_id = latest.e_profile_id AND e.e_version = latest.max_version
        WHERE e.e_project_id = :projectId 
        AND e.e_is_deleted = false
        AND (:fullName IS NULL OR CONCAT(e.e_surname, ' ', e.e_name, ' ', COALESCE(e.e_middle_name, '')) ILIKE '%' || :fullName || '%')
        AND (:personalNumber IS NULL OR e.e_personal_number ILIKE '%' || :personalNumber || '%')
        AND (:organizationId IS NULL OR e.e_organization_id = :organizationId)
        AND (:role IS NULL OR e.e_role = :role)
        ORDER BY e.e_surname, e.e_name
        LIMIT :size OFFSET :offset
    """)
    fun findEmployeesByProjectWithFilters(
        projectId: UUID,
        fullName: String?,
        personalNumber: String?,
        organizationId: UUID?,
        role: String?,
        size: Int,
        offset: Int
    ): Flux<Employee>

    /**
     * Подсчитать количество сотрудников проекта с фильтрацией (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT e.e_profile_id) FROM employee e
        WHERE e.e_project_id = :projectId 
        AND e.e_is_deleted = false
        AND e.e_version = (
            SELECT MAX(e2.e_version) 
            FROM employee e2 
            WHERE e2.e_profile_id = e.e_profile_id
        )
        AND (:fullName IS NULL OR CONCAT(e.e_surname, ' ', e.e_name, ' ', COALESCE(e.e_middle_name, '')) ILIKE '%' || :fullName || '%')
        AND (:personalNumber IS NULL OR e.e_personal_number ILIKE '%' || :personalNumber || '%')
        AND (:organizationId IS NULL OR e.e_organization_id = :organizationId)
        AND (:role IS NULL OR e.e_role = :role)
    """)
    fun countEmployeesByProjectWithFilters(
        projectId: UUID,
        fullName: String?,
        personalNumber: String?,
        organizationId: UUID?,
        role: String?
    ): Mono<Long>

    /**
     * Получить сотрудника по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM employee 
        WHERE e_profile_id = :id 
        ORDER BY e_version DESC 
        LIMIT 1
    """)
    fun findEmployeeById(id: UUID): Mono<Employee>

    /**
     * Получить все версии сотрудника по ID
     */
    @Query("""
        SELECT * FROM employee 
        WHERE e_profile_id = :id 
        ORDER BY e_version DESC
    """)
    fun findAllVersionsByEmployeeId(id: UUID): Flux<Employee>

    /**
     * Подсчитать количество сотрудников в проекте (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT e.e_profile_id) FROM employee e
        WHERE e.e_project_id = :projectId 
        AND e.e_is_deleted = false
        AND e.e_version = (
            SELECT MAX(e2.e_version) 
            FROM employee e2 
            WHERE e2.e_profile_id = e.e_profile_id
        )
    """)
    fun countEmployeesByProjectId(projectId: UUID): Mono<Long>

    /**
     * Проверить существование сотрудника с табельным номером в проекте
     */
    @Query("""
        SELECT EXISTS(
            SELECT 1 FROM employee 
            WHERE e_project_id = :projectId 
            AND e_personal_number = :personalNumber
            AND e_is_deleted = false
            AND e_version = (
                SELECT MAX(e2.e_version) 
                FROM employee e2 
                WHERE e2.e_profile_id = employee.e_profile_id
            )
        )
    """)
    fun existsByProjectIdAndPersonalNumber(projectId: UUID, personalNumber: String): Mono<Boolean>
} 