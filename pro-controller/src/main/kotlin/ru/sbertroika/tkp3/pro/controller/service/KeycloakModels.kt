package ru.sbertroika.tkp3.pro.controller.service

import com.fasterxml.jackson.annotation.JsonProperty

// Модели для аутентификации в Keycloak
data class KeycloakTokenResponse(
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("token_type")
    val tokenType: String,
    @JsonProperty("expires_in")
    val expiresIn: Int,
    @JsonProperty("refresh_expires_in")
    val refreshExpiresIn: Int? = null,
    @JsonProperty("refresh_token")
    val refreshToken: String? = null,
    @JsonProperty("scope")
    val scope: String? = null
)

// Модели для управления пользователями
data class KeycloakUser(
    val id: String? = null,
    val username: String,
    val email: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val enabled: Boolean = true,
    val emailVerified: Boolean = false,
    val attributes: Map<String, List<String>>? = null,
    val credentials: List<KeycloakCredential>? = null,
    val groups: List<String>? = null,
    val realmRoles: List<String>? = null
)

data class KeycloakCredential(
    val type: String = "password",
    val value: String,
    val temporary: Boolean = false
)

data class KeycloakUserCreateRequest(
    val username: String,
    val email: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val enabled: Boolean = true,
    val emailVerified: Boolean = false,
    val credentials: List<KeycloakCredential>? = null,
    val attributes: Map<String, List<String>>? = null
)

data class KeycloakUserUpdateRequest(
    val username: String? = null,
    val email: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val enabled: Boolean? = null,
    val emailVerified: Boolean? = null,
    val attributes: Map<String, List<String>>? = null
)

// Модели для управления ролями
data class KeycloakRole(
    val id: String? = null,
    val name: String,
    val description: String? = null,
    val composite: Boolean = false,
    val clientRole: Boolean = false
)

data class KeycloakRoleAssignment(
    val roles: List<KeycloakRole>
) 