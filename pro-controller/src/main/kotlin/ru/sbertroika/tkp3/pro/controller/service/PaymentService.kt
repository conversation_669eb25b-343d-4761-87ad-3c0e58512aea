package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.PaymentDto
import ru.sbertroika.tkp3.pro.controller.model.PaymentMethodDto
import ru.sbertroika.tkp3.pro.controller.output.*
import ru.sbertroika.tkp3.pro.model.PaymentReport
import java.time.ZonedDateTime
import java.util.*

@Service
class PaymentService(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) {

    fun getPaymentList(filter: PaymentFilterRequest, pagination: Pagination): Mono<PaymentListResponse> {
        val search = mutableListOf<Criteria>()
        
        // Обязательный фильтр по projectId
        search.add(Criteria.where("projectId").`is`(filter.projectId))

        filter.createdAtFrom?.let {
            search.add(Criteria.where("createdAt").greaterThanOrEquals(it))
        }
        
        filter.createdAtTo?.let {
            search.add(Criteria.where("createdAt").lessThan(it))
        }

        filter.organizationId?.let {
            search.add(Criteria.where("orgId").`is`(it))
        }

        filter.routeId?.let {
            search.add(Criteria.where("routeId").`is`(it))
        }

        filter.ticketId?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("ticketId").`is`(it))
            }
        }

        filter.ticketSeries?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("ticketSeries").`is`(it))
            }
        }

        filter.ticketNumber?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("ticketNumber").`is`(it))
            }
        }

        filter.payMethod?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("payMethod").`is`(it))
            }
        }

        filter.trxId?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("trxId").`is`(it))
            }
        }

        filter.terminalSerial?.let {
            if (it.isNotEmpty()) {
                search.add(Criteria.where("terminalSerial").`is`(it))
            }
        }

        val countMono: Mono<Long> = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search)))
            .count()

        // Определяем направление сортировки
        val sort = when (pagination.sortOrder?.lowercase()) {
            "asc" -> Sort.by(pagination.sortField ?: "createdAt").ascending()
            else -> Sort.by(pagination.sortField ?: "createdAt").descending()
        }

        val paymentsMono: Mono<List<PaymentReport>> = entityTemplate.select(PaymentReport::class.java)
            .matching(
                Query.query(Criteria.from(search))
                    .sort(sort)
                    .offset((pagination.limit * pagination.page).toLong())
                    .limit(pagination.limit)
            ).all()
            .collectList()

        return Mono.zip(countMono, paymentsMono)
            .map { tuple ->
                val totalCount = tuple.t1
                val totalPage = if (totalCount % pagination.limit == 0L) totalCount / pagination.limit else totalCount / pagination.limit + 1

                PaymentListResponse(
                    payments = tuple.t2.map(this::mapToResponse),
                    pagination = pagination.copy(
                        totalCount = totalCount,
                        totalPage = totalPage
                    )
                )
            }
    }

    fun getPaymentExamples(projectId: UUID, count: Int = 20): Mono<List<PaymentDto>> {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("projectId").`is`(projectId))

        return entityTemplate.select(PaymentReport::class.java)
            .matching(
                Query.query(Criteria.from(search))
                    .sort(Sort.by("createdAt").descending())
                    .limit(count)
            ).all()
            .collectList()
            .map { payments ->
                payments.map(this::mapToResponse)
            }
    }

    fun getPaymentMethods(): Mono<List<PaymentMethodDto>> {
        return Mono.just(listOf(
            PaymentMethodDto("CASH", "Наличные"),
            PaymentMethodDto("CARD", "Карта"),
            PaymentMethodDto("MOBILE", "Мобильная оплата"),
            PaymentMethodDto("QR", "QR-код")
        ))
    }

    fun getPaymentStats(projectId: UUID): Mono<PaymentStatsResponse> {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("projectId").`is`(projectId))

        val countMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search)))
            .count()

        val sumMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search)))
            .all()
            .map { it.amount ?: 0.0f }
            .reduce(0.0f) { acc, amount -> acc + amount }
            .defaultIfEmpty(0.0f)

        // Получаем количество уникальных организаций
        val organizationsCountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search)))
            .all()
            .map { it.orgId }
            .distinct()
            .count()

        // Получаем количество уникальных маршрутов
        val routesCountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search)))
            .all()
            .map { it.routeId }
            .distinct()
            .count()

        return Mono.zip(countMono, sumMono, organizationsCountMono, routesCountMono)
            .map { tuple ->
                PaymentStatsResponse(
                    totalPayments = tuple.t1,
                    totalAmount = tuple.t2,
                    organizationsCount = tuple.t3,
                    routesCount = tuple.t4
                )
            }
    }

    fun getPaymentMethodStats(projectId: UUID): Mono<PaymentMethodStatsResponse> {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("projectId").`is`(projectId))

        val cardPaymentsMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search).and(Criteria.where("payMethod").`is`("Безналичные(БК)"))))
            .count()

        val cashPaymentsMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search).and(Criteria.where("payMethod").`is`("Наличные"))))
            .count()

        val mobilePaymentsMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search).and(Criteria.where("payMethod").`is`("MOBILE"))))
            .count()

        val qrPaymentsMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(search).and(Criteria.where("payMethod").`is`("QR"))))
            .count()

        return Mono.zip(cardPaymentsMono, cashPaymentsMono, mobilePaymentsMono, qrPaymentsMono)
            .map { tuple ->
                PaymentMethodStatsResponse(
                    cardPayments = tuple.t1,
                    cashPayments = tuple.t2,
                    mobilePayments = tuple.t3,
                    qrPayments = tuple.t4
                )
            }
    }

    fun getPaymentPeriodStats(projectId: UUID): Mono<PaymentPeriodStatsResponse> {
        val now = ZonedDateTime.now()
        val todayStart = now.toLocalDate().atStartOfDay(now.zone)
        val yesterdayStart = todayStart.minusDays(1)
        val weekStart = todayStart.minusDays(7)
        val monthStart = todayStart.minusDays(30)

        val baseSearch = mutableListOf<Criteria>()
        baseSearch.add(Criteria.where("projectId").`is`(projectId))

        val todayAmountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(baseSearch).and(Criteria.where("createdAt").greaterThanOrEquals(todayStart))))
            .all()
            .map { it.amount ?: 0.0f }
            .reduce(0.0f) { acc, amount -> acc + amount }
            .defaultIfEmpty(0.0f)

        val yesterdayAmountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(baseSearch)
                .and(Criteria.where("createdAt").greaterThanOrEquals(yesterdayStart))
                .and(Criteria.where("createdAt").lessThan(todayStart))))
            .all()
            .map { it.amount ?: 0.0f }
            .reduce(0.0f) { acc, amount -> acc + amount }
            .defaultIfEmpty(0.0f)

        val weekAmountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(baseSearch).and(Criteria.where("createdAt").greaterThanOrEquals(weekStart))))
            .all()
            .map { it.amount ?: 0.0f }
            .reduce(0.0f) { acc, amount -> acc + amount }
            .defaultIfEmpty(0.0f)

        val monthAmountMono = entityTemplate.select(PaymentReport::class.java)
            .matching(Query.query(Criteria.from(baseSearch).and(Criteria.where("createdAt").greaterThanOrEquals(monthStart))))
            .all()
            .map { it.amount ?: 0.0f }
            .reduce(0.0f) { acc, amount -> acc + amount }
            .defaultIfEmpty(0.0f)

        return Mono.zip(todayAmountMono, yesterdayAmountMono, weekAmountMono, monthAmountMono)
            .map { tuple ->
                PaymentPeriodStatsResponse(
                    todayAmount = tuple.t1,
                    yesterdayAmount = tuple.t2,
                    weekAmount = tuple.t3,
                    monthAmount = tuple.t4
                )
            }
    }

    private fun mapToResponse(payment: PaymentReport): PaymentDto {
        return PaymentDto(
            projectId = payment.projectId,
            orgId = payment.orgId,
            orgName = payment.orgName,
            createdAt = payment.createdAt,
            trxId = payment.trxId,
            ticketId = payment.ticketId,
            ticketSeries = payment.ticketSeries,
            ticketNumber = payment.ticketNumber,
            routeId = payment.routeId,
            routeNumber = payment.routeNumber,
            routeName = payment.routeName,
            amount = payment.amount,
            payMethod = payment.payMethod,
            driverFio = payment.driverFio,
            conductorFio = payment.conductorFio,
            terminalSerial = payment.terminalSerial,
            shiftNum = payment.shiftNum,
            ern = payment.ern,
            stantionFrom = payment.stantionFrom,
            stantionTo = payment.stantionTo,
            tariffName = payment.tariffName,
            productName = payment.productName,
            vhNumber = payment.vhNumber,
            vhType = payment.vhType,
            maskedPan = payment.maskedPan,
            paySystem = payment.paySystem,
            cardUid = payment.cardUid,
            recordAt = payment.recordAt
        )
    }
}

data class PaymentFilterRequest(
    val projectId: UUID,
    val createdAtFrom: ZonedDateTime? = null,
    val createdAtTo: ZonedDateTime? = null,
    val organizationId: UUID? = null,
    val routeId: UUID? = null,
    val ticketId: String? = null,
    val ticketSeries: String? = null,
    val ticketNumber: String? = null,
    val payMethod: String? = null,
    val trxId: String? = null,
    val terminalSerial: String? = null
) 