package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Station
import ru.sbertroika.tkp3.pro.model.StationPK
import java.util.*

@Repository
interface StationRepository : ReactiveCrudRepository<Station, StationPK> {



    @Query("""
        SELECT 
            s.*,
            co.c_name as country,
            r.r_name as region,
            c.city_name as city,
            d.d_name as district,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN true ELSE false END as hasCoordinates,
            CASE WHEN s.lat IS NOT NULL AND s.long IS NOT NULL THEN s.lat || ', ' || s.long ELSE NULL END as coordinates
        FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE p_id = :projectId AND st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.p_id = :projectId 
        AND s.st_status != 'IS_DELETED'
        AND (:name IS NULL OR s.st_name ILIKE '%' || :name || '%')
        AND (:city IS NULL OR c.city_name ILIKE '%' || :city || '%')
        AND (:region IS NULL OR r.r_name ILIKE '%' || :region || '%')
        AND (:country IS NULL OR co.c_name ILIKE '%' || :country || '%')
        ORDER BY s.st_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findByProjectIdWithFilters(
        projectId: UUID,
        name: String? = null,
        city: String? = null,
        region: String? = null,
        country: String? = null,
        limit: Int,
        offset: Int
    ): Flux<Station>

    @Query("""
        SELECT COUNT(*) FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE p_id = :projectId AND st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        LEFT JOIN country co ON s.st_country_id = co.c_id
        LEFT JOIN region r ON s.st_region_id = r.r_id
        LEFT JOIN city c ON s.st_city_id = c.city_id
        LEFT JOIN district d ON s.st_district_id = d.d_id
        WHERE s.p_id = :projectId 
        AND s.st_status != 'IS_DELETED'
        AND (:name IS NULL OR s.st_name ILIKE '%' || :name || '%')
        AND (:city IS NULL OR c.city_name ILIKE '%' || :city || '%')
        AND (:region IS NULL OR r.r_name ILIKE '%' || :region || '%')
        AND (:country IS NULL OR co.c_name ILIKE '%' || :country || '%')
    """)
    fun countByProjectIdWithFilters(
        projectId: UUID,
        name: String? = null,
        city: String? = null,
        region: String? = null,
        country: String? = null
    ): Mono<Long>

    @Query("""
        SELECT * FROM station 
        WHERE st_id = :id 
        AND st_version = (
            SELECT MAX(st_version) 
            FROM station 
            WHERE st_id = :id AND st_status != 'IS_DELETED'
        )
        AND st_status != 'IS_DELETED'
    """)
    fun findStationById(id: UUID): Mono<Station>

    @Query("""
        SELECT * FROM station s
        INNER JOIN (
            SELECT st_id, MAX(st_version) as max_version
            FROM station
            WHERE st_id = ANY(:ids) AND st_status != 'IS_DELETED'
            GROUP BY st_id
        ) latest ON s.st_id = latest.st_id AND s.st_version = latest.max_version
        WHERE s.st_status != 'IS_DELETED'
    """)
    fun findByIds(ids: List<UUID>): Flux<Station>

    /**
     * Подсчитать количество остановок в проекте (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT s.st_id) FROM station s
        WHERE s.p_id = :projectId 
        AND s.st_status != 'IS_DELETED'
        AND s.st_version = (
            SELECT MAX(s2.st_version) 
            FROM station s2 
            WHERE s2.st_id = s.st_id
        )
    """)
    fun countStationsByProjectId(projectId: UUID): Mono<Long>
} 