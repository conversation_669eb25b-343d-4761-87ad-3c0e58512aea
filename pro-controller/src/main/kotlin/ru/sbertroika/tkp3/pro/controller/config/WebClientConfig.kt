package ru.sbertroika.tkp3.pro.controller.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.function.client.WebClient

@Configuration
open class WebClientConfig {

    @Value("\${tms.api.base-url:http://localhost:8081}")
    private lateinit var tmsBaseUrl: String
    
    @Value("\${keycloak.auth-server-url:http://localhost:8080}")
    private lateinit var keycloakBaseUrl: String

    @Bean
    open fun tmsWebClient(): WebClient {
        return WebClient.builder()
            .baseUrl(tmsBaseUrl)
            .build()
    }
    
    @Bean
    open fun keycloakWebClient(): WebClient {
        return WebClient.builder()
            .baseUrl(keycloakBaseUrl)
            .build()
    }
} 