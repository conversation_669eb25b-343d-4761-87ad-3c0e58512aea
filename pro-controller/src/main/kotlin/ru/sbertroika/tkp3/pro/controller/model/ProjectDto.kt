package ru.sbertroika.tkp3.pro.controller.model

import ru.sbertroika.tkp3.pro.model.OperatorOrganization
import ru.sbertroika.tkp3.pro.model.ProjectStatus
import java.sql.Timestamp
import java.time.LocalDate
import java.util.UUID

/**
 * DTO модель проекта для UI
 * Содержит все поля проекта, включая вычисляемые
 */
data class ProjectDto(

    /** Уникальный идентификатор проекта */
    var id: UUID? = null,

    /** Версия записи проекта для поддержки версионирования */
    var version: Int? = null,

    /** Дата и время создания версии */
    var versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    var versionCreatedBy: UUID? = null,

    /** Наименование проекта */
    var name: String? = null,

    /** Статус проекта (ACTIVE, DRAFT, COMPLETED и т.д.) */
    var status: ProjectStatus? = null,

    /** Идентификатор связанного договора (UUID) */
    var contractId: UUID? = null,

    /** Описание проекта */
    var description: String? = null,

    /** Общий бюджет проекта в копейках */
    var totalBudget: Long? = null,

    /** Потраченный бюджет проекта в копейках */
    var spentBudget: Long? = null,

    /** Прогресс выполнения проекта (0-100) */
    var progress: Int? = null,

    /** Дата последней синхронизации с внешними системами */
    var lastSyncDate: Timestamp? = null,

    /** Статус синхронизации (pending, synced, error) */
    var syncStatus: String? = null,

    /** Порядковый номер проекта в системе СТ */
    var index: Int? = null,

    /** Теги проекта в формате JSON */
    var tags: String? = null,

    /** Операторская организация (заполняется из связанной таблицы project_operator) */
    var operatorOrganization: OperatorOrganization? = null,

    /** Активные терминалы (вычисляемое поле) */
    var activeTerminals: Int = 0,

    /** Общее количество терминалов (вычисляемое поле) */
    var terminals: Int = 0,

    /** Количество транспортных средств (вычисляемое поле) */
    var vehicles: Int = 0,

    /** Маршруты (вычисляемое поле) */
    var routes: List<String> = emptyList(),

    /** Количество транзакций в месяц (вычисляемое поле) */
    var monthlyTransactions: Int = 0,

    /** Доход в месяц в копейках (вычисляемое поле) */
    var monthlyRevenue: Long = 0
)