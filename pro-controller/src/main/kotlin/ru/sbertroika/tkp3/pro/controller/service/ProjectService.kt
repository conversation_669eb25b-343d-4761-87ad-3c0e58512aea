package ru.sbertroika.tkp3.pro.controller.service

import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.manifest.model.pro.OperationStatus
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.pro.controller.model.ProjectDto
import ru.sbertroika.tkp3.pro.controller.model.ProjectStatsDto
import ru.sbertroika.tkp3.pro.controller.output.*
import ru.sbertroika.tkp3.pro.model.ProjectStatus
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.atomic.AtomicInteger

@Service
class ProjectService(
    private val projectRepository: ProjectRepository,
    private val projectOperatorRepository: ProjectOperatorRepository,
    private val projectMapper: ProjectMapper,
    private val manifestService: ManifestService,
    val manifestEventService: ManifestEventService,
    private val participantOrganizationRepository: ParticipantOrganizationRepository,
    private val employeeRepository: EmployeeRepository,
    private val routeRepository: RouteRepository,
    private val vehicleRepository: VehicleRepository,
    private val stationRepository: StationRepository
) {

    private val logger = LoggerFactory.getLogger(ProjectService::class.java)

    /**
     * Получить все проекты с фильтрацией и пагинацией
     */
    fun getAllProjects(
        status: String?,
        page: Int,
        size: Int
    ): Flux<ProjectDto> {
        val offset = page * size
        return projectRepository.findAllProjectsWithFilters(
            status,
            size,
            offset
        ).flatMap { project ->
            // Преобразуем в DTO и заполняем операторскую организацию
            val dto = projectMapper.toDto(project)
            projectOperatorRepository.findLatestByProjectId(project.id!!)
                .next()
                .map { operator ->
                    val operatorOrg = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                        id = operator.operatorId,
                        name = operator.operatorName,
                        role = operator.operatorRole
                    )
                    projectMapper.fillComputedFields(dto, operatorOrg)
                }
                .defaultIfEmpty(projectMapper.fillComputedFields(dto))
        }
    }

    /**
     * Получить общее количество проектов с фильтрацией
     */
    fun getProjectsCount(
        status: String?
    ): Mono<Long> {
        return projectRepository.countProjectsWithFilters(
            status
        ).next()
    }

    /**
     * Получить проект по ID
     */
    fun getProjectById(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                // Преобразуем в DTO и заполняем операторскую организацию
                val dto = projectMapper.toDto(project)
                projectOperatorRepository.findLatestByProjectId(project.id!!)
                    .next()
                    .map { operator ->
                        val operatorOrg = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                            id = operator.operatorId,
                            name = operator.operatorName,
                            role = operator.operatorRole
                        )
                        projectMapper.fillComputedFields(dto, operatorOrg)
                    }
                    .defaultIfEmpty(projectMapper.fillComputedFields(dto))
            }
    }

    /**
     * Создать новый проект
     */
    fun createProject(projectDto: ProjectDto, createdBy: UUID): Mono<ProjectDto> {
        val project = projectMapper.toEntity(projectDto)
        val now = Timestamp.valueOf(LocalDateTime.now())
        project.version = 1
        project.versionCreatedAt = now
        project.versionCreatedBy = createdBy
        project.status = ProjectStatus.ACTIVE

        return projectRepository.findMaxIndex()
            .next()
            .flatMap { maxIndex ->
                project.index = maxIndex + 1
                projectRepository.save(project)
            }
            .map { savedProject -> projectMapper.toDto(savedProject) }

    }

    /**
     * Обновить проект
     */
    fun updateProject(id: UUID, updatedProjectDto: ProjectDto, updatedBy: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { existingProject ->
                val updatedProject = projectMapper.toEntity(updatedProjectDto)
                val now = Timestamp.valueOf(LocalDateTime.now())
                updatedProject.id = id
                updatedProject.version = (existingProject.version ?: 0) + 1
                updatedProject.versionCreatedAt = now
                updatedProject.versionCreatedBy = updatedBy

                projectRepository.save(updatedProject)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Удалить проект (логическое удаление)
     */
    fun deleteProject(id: UUID): Mono<Void> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.IS_DELETED
                projectRepository.save(project)
            }
            .then()
    }

    /**
     * Синхронизировать проект с договором
     */
    fun syncProjectWithContract(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Активировать проект
     */
    fun activateProject(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.ACTIVE
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Завершить проект
     */
    fun completeProject(id: UUID): Mono<ProjectDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.COMPLETED
                projectRepository.save(project)
                    .map { savedProject -> projectMapper.toDto(savedProject) }
            }
    }

    /**
     * Получить проекты по статусу
     */
    fun getProjectsByStatus(status: String): Flux<ProjectDto> {
        return projectRepository.findByStatus(status)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по региону
     */
    fun getProjectsByRegion(region: String): Flux<ProjectDto> {
        return projectRepository.findByRegion(region)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по типу
     */
    fun getProjectsByType(projectType: String): Flux<ProjectDto> {
        return projectRepository.findByProjectType(projectType)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить проекты по договору
     */
    fun getProjectsByContract(contractId: UUID): Flux<ProjectDto> {
        return projectRepository.findByContractId(contractId)
            .map { project -> projectMapper.toDto(project) }
    }

    /**
     * Получить организации проекта
     */
    fun getProjectOrganizations(id: UUID): Mono<List<Map<String, Any>>> {
        return projectRepository.findProjectById(id)
            .map {
                // TODO: Реализовать получение организаций из базы данных
                // Пока возвращаем пустой список
                emptyList<Map<String, Any>>()
            }
    }

    /**
     * Получить статистику проекта
     */
    fun getProjectStats(id: UUID, manifestVersion: Int = 0): Mono<ProjectStatsDto> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                // Получаем статистику из всех репозиториев параллельно
                Mono.zip(
                    participantOrganizationRepository.countUniqueOrganizationsByProjectId(id),
                    employeeRepository.countEmployeesByProjectId(id),
                    routeRepository.countRoutesByProjectId(id),
                    vehicleRepository.countVehiclesByProjectId(id),
                    stationRepository.countStationsByProjectId(id),
                    if (manifestVersion > 0) Mono.just(manifestVersion) else getManifestVersion(id)
                ).map { result ->
                    val organizations = result.t1
                    val employees = result.t2
                    val routes = result.t3
                    val vehicles = result.t4
                    val stations = result.t5
                    val manifestVersion = result.t6

                    ProjectStatsDto(
                        organizations = organizations.toInt(),
                        employees = employees.toInt(),
                        routes = routes.toInt(),
                        vehicles = vehicles.toInt(),
                        stations = stations.toInt(),
                        manifestVersion = manifestVersion
                    )
                }
            }
    }

    /**
     * Получить версию манифеста проекта
     */
    private fun getManifestVersion(projectId: UUID): Mono<Int> {
        return Mono.fromCallable {
            runBlocking {
                manifestService.getManifestByProject(projectId.toString()).fold(
                    { error ->
                        // В случае ошибки возвращаем версию 1
                        logger.warn("Ошибка получения манифеста для проекта $projectId: ${error.message}")
                        1
                    },
                    { manifest ->
                        // Если манифест найден, возвращаем его версию, иначе версию 1
                        manifest?.version ?: 1
                    }
                )
            }
        }
    }

    /**
     * Обновить справочники проекта
     */
    fun updateManifest(id: UUID): Mono<ProjectStatsDto> {
        val version = AtomicInteger(0)
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                runBlocking {
                    try {
                        // Проверяем, не идет ли уже обновление для этого проекта
                        if (manifestEventService.isUpdateActive(id)) {
                            logger.info("Обновление манифеста для проекта $id уже активно, подписываемся на существующее обновление")
                        } else {
                            // Запускаем новое обновление манифеста
                            manifestEventService.startUpdate(id)
                            manifestService.resetManifest(id.toString())
                            logger.info("Запущено новое обновление манифеста для проекта $id")
                        }

                        // Ожидаем событие обновления с таймаутом 30 секунд
                        val event = manifestEventService.waitForManifestUpdate(id, 30000)

                        logger.info("Получено событие обновления манифеста для проекта $id: status=${event.status}")

                        // Проверяем статус события
                        if (event.status == OperationStatus.FAIL) {
                            val errorMessage = event.errors.joinToString(", ")
                            throw RuntimeException("Ошибка обновления справочников: $errorMessage")
                        }

                        version.set(event.manifestVersion ?: 0)
                    } catch (e: kotlinx.coroutines.TimeoutCancellationException) {
                        logger.error("Таймаут ожидания события обновления манифеста для проекта $id")
                        // Отмечаем завершение обновления в случае таймаута
                        manifestEventService.finishUpdate(id)
                        throw RuntimeException("Не получен ответ об обновлении справочников за отведенное время (30 секунд)")
                    } catch (e: Exception) {
                        logger.error("Ошибка обновления манифеста для проекта $id: ${e.message}", e)
                        // Отмечаем завершение обновления в случае ошибки
                        manifestEventService.finishUpdate(id)
                        throw e
                    }
                }

                // После успешного обновления справочников возвращаем обновленную статистику
                getProjectStats(id, version.get())
            }
    }
} 