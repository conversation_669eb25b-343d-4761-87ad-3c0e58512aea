package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Route
import java.util.*

@Repository
interface RouteRepository : ReactiveCrudRepository<Route, ru.sbertroika.tkp3.pro.model.RoutePK> {

    /**
     * Получить все маршруты с фильтрацией и пагинацией (только последние версии)
     */
    @Query("""
        SELECT r.* FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_status != 'IS_DELETED'
            AND (:projectId IS NULL OR r_project_id = :projectId)
            AND (:status IS NULL OR r_status = :status)
            AND (:scheme IS NULL OR r_scheme = :scheme)
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_status != 'IS_DELETED'
        ORDER BY r.r_project_id, r.r_index, r.r_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllRoutesWithFilters(
        projectId: UUID?,
        status: String?,
        scheme: String?,
        limit: Int,
        offset: Int
    ): Flux<Route>

    /**
     * Получить общее количество маршрутов с фильтрацией (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT r_id) FROM route 
        WHERE r_status != 'IS_DELETED'
        AND (:projectId IS NULL OR r_project_id = :projectId)
        AND (:status IS NULL OR r_status = :status)
        AND (:scheme IS NULL OR r_scheme = :scheme)
    """)
    fun countRoutesWithFilters(
        projectId: UUID?,
        status: String?,
        scheme: String?
    ): Flux<Long>

    /**
     * Получить маршрут по ID (последняя версия)
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_id = :id 
        AND r_status != 'IS_DELETED'
        ORDER BY r_version DESC 
        LIMIT 1
    """)
    fun findRouteById(id: UUID): Mono<Route>

    /**
     * Получить все версии маршрута по ID
     */
    @Query("""
        SELECT * FROM route 
        WHERE r_id = :id 
        ORDER BY r_version DESC
    """)
    fun findAllVersionsByRouteId(id: UUID): Flux<Route>

    /**
     * Получить маршруты по проекту (только последние версии)
     */
    @Query("""
        SELECT r.* FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_project_id = :projectId 
            AND r_status != 'IS_DELETED'
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_project_id = :projectId 
        AND r.r_status != 'IS_DELETED'
        ORDER BY r.r_index, r.r_version_created_at DESC
    """)
    fun findByProjectId(projectId: UUID): Flux<Route>

    /**
     * Получить маршруты по статусу (только последние версии)
     */
    @Query("""
        SELECT r.* FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_status = :status 
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_status = :status 
        ORDER BY r.r_project_id, r.r_index, r.r_version_created_at DESC
    """)
    fun findByStatus(status: String): Flux<Route>

    /**
     * Получить маршруты по схеме (только последние версии)
     */
    @Query("""
        SELECT r.* FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_scheme = :scheme 
            AND r_status != 'IS_DELETED'
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_scheme = :scheme 
        AND r.r_status != 'IS_DELETED'
        ORDER BY r.r_project_id, r.r_index, r.r_version_created_at DESC
    """)
    fun findByScheme(scheme: String): Flux<Route>

    /**
     * Подсчитать количество маршрутов в проекте (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT r.r_id) FROM route r
        WHERE r.r_project_id = :projectId 
        AND r.r_status != 'IS_DELETED'
        AND r.r_version = (
            SELECT MAX(r2.r_version) 
            FROM route r2 
            WHERE r2.r_id = r.r_id
        )
    """)
    fun countRoutesByProjectId(projectId: UUID): Mono<Long>

    /**
     * Получить максимальный индекс маршрута в проекте (только последние версии)
     */
    @Query("""
        SELECT COALESCE(MAX(r.r_index), 0) FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_project_id = :projectId 
            AND r_status != 'IS_DELETED'
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_project_id = :projectId 
        AND r.r_status != 'IS_DELETED'
    """)
    fun findMaxIndexByProjectId(projectId: UUID): Flux<Int>

    /**
     * Проверить существование маршрута с номером в проекте (только последние версии)
     */
    @Query("""
        SELECT COUNT(*) > 0 FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) as max_version
            FROM route
            WHERE r_project_id = :projectId 
            AND r_status != 'IS_DELETED'
            GROUP BY r_id
        ) latest ON r.r_id = latest.r_id AND r.r_version = latest.max_version
        WHERE r.r_project_id = :projectId 
        AND r.r_number = :number 
        AND r.r_status != 'IS_DELETED'
        AND r.r_id != :excludeId
    """)
    fun existsByProjectIdAndNumber(
        projectId: UUID,
        number: String,
        excludeId: UUID? = null
    ): Flux<Boolean>

    /**
     * Подсчитать количество маршрутов для проекта (только последние версии)
     */
    @Query("""
        SELECT COUNT(DISTINCT r_id) FROM route 
        WHERE r_project_id = :projectId 
        AND r_status != 'IS_DELETED'
    """)
    fun countByProjectId(projectId: UUID): Mono<Long>
} 