package ru.sbertroika.tkp3.pro.controller.model

import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.sql.Timestamp
import java.util.*

data class VehicleDto(
    val id: UUID? = null,
    val version: Int? = null,
    val versionCreatedAt: Timestamp? = null,
    val versionCreatedBy: UUID? = null,
    val projectId: UUID? = null,
    val organizationId: UUID? = null,
    val type: String? = null,
    val number: String? = null,
    val status: String? = null,
    val tags: String? = null
)

data class VehicleCreateRequest(
    val projectId: UUID,
    val organizationId: UUID,
    val type: String = VehicleType.BUS.name,
    val number: String,
    val status: String = VehicleStatus.ACTIVE.name,
    val tags: String? = null
)

data class VehicleUpdateRequest(
    val organizationId: UUID? = null,
    val type: String? = null,
    val number: String? = null,
    val status: String? = null,
    val tags: String? = null
)

data class VehicleListResponse(
    val content: List<VehicleDto>,
    val pagination: PaginationInfo
)

data class VehicleFilters(
    val type: String? = null,
    val status: String? = null,
    val organizationId: UUID? = null,
    val search: String? = null
) 