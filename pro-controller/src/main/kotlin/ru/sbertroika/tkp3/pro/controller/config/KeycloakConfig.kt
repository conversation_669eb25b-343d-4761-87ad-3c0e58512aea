package ru.sbertroika.tkp3.pro.controller.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "keycloak")
open class KeycloakConfig(
    var authServerUrl: String = "http://localhost:8080",
    var realm: String = "master",
    var clientAdminId: String = "admin-cli",
    var clientSecret: String = "",
    var adminUsername: String = "admin",
    var adminPassword: String = "admin"
) 