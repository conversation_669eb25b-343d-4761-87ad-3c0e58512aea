package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.input.TerminalUserCreateRequest
import ru.sbertroika.tkp3.pro.controller.input.TerminalUserUpdateRequest
import java.util.*

@Service
class TmsIntegrationService(
    private val tmsWebClient: WebClient
) {
    
    /**
     * Создает пользователя терминала в TMS
     */
    fun createTerminalUser(request: TerminalUserCreateRequest, jwtToken: String): Mono<Void> {
        return tmsWebClient.post()
            .uri("/api/tms/v1/terminal-users")
            .header("Authorization", "Bearer $jwtToken")
            .bodyValue(request)
            .retrieve()
            .bodyToMono(Void::class.java)
    }
    
    /**
     * Обновляет пользователя терминала в TMS
     */
    fun updateTerminalUser(userId: UUID, request: TerminalUserUpdateRequest, jwtToken: String): Mono<Void> {
        return tmsWebClient.put()
            .uri("/api/tms/v1/terminal-users/{id}", userId)
            .header("Authorization", "Bearer $jwtToken")
            .bodyValue(request)
            .retrieve()
            .bodyToMono(Void::class.java)
    }
    
    /**
     * Удаляет пользователя терминала в TMS
     */
    fun deleteTerminalUser(userId: UUID, jwtToken: String): Mono<Void> {
        return tmsWebClient.delete()
            .uri("/api/tms/v1/terminal-users/{id}", userId)
            .header("Authorization", "Bearer $jwtToken")
            .retrieve()
            .bodyToMono(Void::class.java)
    }
} 