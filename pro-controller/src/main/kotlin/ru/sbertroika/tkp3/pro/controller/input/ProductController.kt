package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.ProductDto
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.ProductService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.model.Product
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class ProductController(
    private val productService: ProductService
) : BaseController() {

    @GetMapping("/products")
    fun getAllProducts(): Flux<ApiResponse<ProductDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMapMany { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, получаем продукты только этого проекта
                    productService.getProductsByProject(tokenProjectId.get(), 0, 1000, null, null)
                        .flatMapMany { result ->
                            @Suppress("UNCHECKED_CAST")
                            val products = result["content"] as? List<Map<String, Any>> ?: emptyList()
                            Flux.fromIterable(products.map { productMap ->
                                ProductDto(
                                    id = UUID.fromString(productMap["id"] as String),
                                    version = productMap["version"] as? Int,
                                    versionCreatedAt = productMap["versionCreatedAt"] as? java.sql.Timestamp,
                                    versionCreatedBy = productMap["versionCreatedBy"]?.let { UUID.fromString(it as String) },
                                    projectId = UUID.fromString(productMap["projectId"] as String),
                                    name = productMap["name"] as? String,
                                    status = productMap["status"] as? String,
                                    tags = productMap["tags"] as? String ?: ""
                                )
                            })
                        }
                } else {
                    // Если нет projectId в токене, получаем все продукты
                    productService.getAllProducts()
                }
            }
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<ProductDto>("Ошибка получения продуктов: ${error.message}"))
            }
    }

    @GetMapping("/products/{id}")
    fun getProductById(@PathVariable id: String): Mono<ApiResponse<ProductDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                productService.getProductById(UUID.fromString(id))
                    .flatMap { product ->
                        if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к продукту из другого проекта", 
                                RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<ProductDto>(RuntimeException("Доступ запрещен: продукт принадлежит другому проекту"))
                        } else {
                            Mono.just(product)
                        }
                    }
            }
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<ProductDto>("Ошибка получения продукта: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/products")
    fun getProductsByProject(
        @PathVariable projectId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) status: String?
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список продуктов проекта", projectId)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(UUID.fromString(projectId))
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                productService.getProductsByProject(
                    effectiveProjectId, page, size, name, status
                )
            }
            .map { result ->
                ApiResponse.success(result)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении продуктов проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения продуктов проекта: ${error.message}"))
            }
    }

    @PostMapping("/products/batch")
    fun getProductsByIds(@RequestBody request: Map<String, List<String>>): Flux<ApiResponse<ProductDto>> {
        val productIds = request["productIds"]?.map { UUID.fromString(it) } ?: emptyList()
        
        return JwtUtils.getCurrentProjectId()
            .flatMapMany { tokenProjectId ->
                productService.getProductsByIds(productIds)
                    .filter { product ->
                        if (tokenProjectId.isPresent) {
                            product.projectId == tokenProjectId.get()
                        } else {
                            true
                        }
                    }
            }
            .map { product ->
                ApiResponse.success(product)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<ProductDto>("Ошибка получения продуктов по ID: ${error.message}"))
            }
    }

    @PostMapping("/projects/{projectId}/products")
    fun createProduct(@PathVariable projectId: String, @RequestBody product: Product): Mono<ApiResponse<Product>> {
        logUserAction("создает новый продукт в проекте", projectId)
        
        val requestProjectUuid = UUID.fromString(projectId)
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                val effectiveProjectId = tokenProjectId.getOrDefault(requestProjectUuid)
                
                if (tokenProjectId.isPresent && requestProjectUuid != tokenProjectId.get()) {
                    logError("Попытка создания продукта в другом проекте", 
                        RuntimeException("Request projectId: $requestProjectUuid, Token projectId: $tokenProjectId"))
                    Mono.error<Product>(RuntimeException("Доступ запрещен: нельзя создавать продукт в другом проекте"))
                } else {
                    productService.createProduct(effectiveProjectId, product, currentUserId)
                }
            }
            .map { createdProduct ->
                logUserAction("успешно создал продукт", createdProduct.name ?: "Без названия")
                ApiResponse.success(createdProduct, "Продукт успешно создан")
            }
            .onErrorResume { error ->
                logError("Ошибка при создании продукта: ${error.message}", error)
                Mono.just(ApiResponse.error<Product>("Ошибка создания продукта: ${error.message}"))
            }
    }

    @PutMapping("/products/{id}")
    fun updateProduct(@PathVariable id: String, @RequestBody product: Product): Mono<ApiResponse<Product>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                productService.getProductById(UUID.fromString(id))
                    .flatMap { existingProduct ->
                        if (tokenProjectId.isPresent && existingProduct.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления продукта из другого проекта", 
                                RuntimeException("Product projectId: ${existingProduct.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Product>(RuntimeException("Доступ запрещен: продукт принадлежит другому проекту"))
                        } else {
                            val projectIdForUpdate = existingProduct.projectId ?: 
                                throw RuntimeException("Продукт не привязан к проекту")
                            productService.updateProduct(UUID.fromString(id), product.copy(projectId = projectIdForUpdate), currentUserId)
                        }
                    }
            }
            .map { updatedProduct ->
                ApiResponse.success(updatedProduct, "Продукт успешно обновлен")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении продукта: ${error.message}", error)
                Mono.just(ApiResponse.error<Product>("Ошибка обновления продукта: ${error.message}"))
            }
    }

    @DeleteMapping("/products/{id}")
    fun deleteProduct(@PathVariable id: String): Mono<ApiResponse<String>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                productService.getProductById(UUID.fromString(id))
                    .flatMap { existingProduct ->
                        if (tokenProjectId.isPresent && existingProduct.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления продукта из другого проекта", 
                                RuntimeException("Product projectId: ${existingProduct.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: продукт принадлежит другому проекту"))
                        } else {
                            productService.deleteProduct(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map {
                ApiResponse.success("Продукт успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении продукта: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления продукта: ${error.message}"))
            }
    }
} 