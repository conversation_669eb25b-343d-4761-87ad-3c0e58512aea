package ru.sbertroika.tkp3.pro.controller.service

import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.manifest.model.pro.JournalOperationType
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.starter.mapper
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Service
class ManifestEventService {
    
    private val logger = LoggerFactory.getLogger(ManifestEventService::class.java)
    
    // Хранилище каналов для ожидания событий по projectId
    private val waitingChannels = ConcurrentHashMap<UUID, MutableList<Channel<ManifestJournal>>>()
    
    // Хранилище активных обновлений по projectId
    private val activeUpdates = ConcurrentHashMap<UUID, Boolean>()
    
    /**
     * Ожидать событие обновления манифеста для проекта с таймаутом
     */
    suspend fun waitForManifestUpdate(projectId: UUID, timeoutMs: Long = 30000): ManifestJournal {
        return withTimeout(timeoutMs) {
            val channel = Channel<ManifestJournal>(1)
            
            // Добавляем канал в список ожидающих для данного проекта
            waitingChannels.computeIfAbsent(projectId) { mutableListOf() }.add(channel)
            
            try {
                logger.info("Ожидание события обновления манифеста для проекта $projectId (таймаут: ${timeoutMs}ms)")
                channel.receive()
            } finally {
                // Удаляем канал из списка ожидающих
                waitingChannels[projectId]?.remove(channel)
                if (waitingChannels[projectId]?.isEmpty() == true) {
                    waitingChannels.remove(projectId)
                }
                channel.close()
            }
        }
    }
    
    /**
     * Проверить, активно ли обновление для проекта
     */
    fun isUpdateActive(projectId: UUID): Boolean {
        return activeUpdates.containsKey(projectId)
    }
    
    /**
     * Отметить начало обновления для проекта
     */
    fun startUpdate(projectId: UUID) {
        activeUpdates[projectId] = true
        logger.info("Начато обновление манифеста для проекта $projectId")
    }
    
    /**
     * Отметить завершение обновления для проекта
     */
    fun finishUpdate(projectId: UUID) {
        activeUpdates.remove(projectId)
        logger.info("Завершено обновление манифеста для проекта $projectId")
    }
    
    /**
     * Kafka listener для событий обновления манифеста
     */
    @KafkaListener(topics = ["\${spring.kafka.operation_journal_topic}"], groupId="pro_controller_manifest_update_group")
    fun handleManifestEvent(message: String) {
        try {
            val journal = mapper().readValue(message, ManifestJournal::class.java)
            
            // Проверяем, что это событие обновления манифеста для проекта
            if (journal.type == JournalOperationType.CHANGE_MANIFEST_RESULT && journal.projectId != null) {
                
                logger.info("Получено событие обновления манифеста: projectId=${journal.projectId}, status=${journal.status}")
                
                // Отправляем событие всем ожидающим каналам для этого проекта
                val projectId = journal.projectId
                if (projectId != null) {
                    val channels = waitingChannels[projectId]
                    if (channels != null && channels.isNotEmpty()) {
                        runBlocking {
                            channels.forEach { channel ->
                                if (!channel.isClosedForSend) {
                                    try {
                                        channel.send(journal)
                                        logger.debug("Событие отправлено в канал для проекта $projectId")
                                    } catch (e: Exception) {
                                        logger.warn("Ошибка отправки события в канал: ${e.message}")
                                    }
                                }
                            }
                        }
                        logger.info("Событие отправлено в ${channels.size} каналов для проекта $projectId")
                    } else {
                        logger.debug("Нет ожидающих каналов для проекта $projectId")
                    }
                    
                    // Отмечаем завершение обновления
                    finishUpdate(projectId)
                } else {
                    logger.warn("Получено событие без projectId")
                }
            }
        } catch (e: Exception) {
            logger.error("Ошибка обработки события манифеста: ${e.message}", e)
        }
    }
    
    /**
     * Проверить, есть ли ожидающие каналы для проекта
     */
    fun hasWaitingChannels(projectId: UUID): Boolean {
        return waitingChannels.containsKey(projectId) && waitingChannels[projectId]?.isNotEmpty() == true
    }
    
    /**
     * Получить количество ожидающих каналов для проекта
     */
    fun getWaitingChannelsCount(projectId: UUID): Int {
        return waitingChannels[projectId]?.size ?: 0
    }
    
    /**
     * Получить общее количество проектов с ожидающими каналами
     */
    fun getProjectsWithWaitingChannelsCount(): Int {
        return waitingChannels.size
    }
    
    /**
     * Получить количество активных обновлений
     */
    fun getActiveUpdatesCount(): Int {
        return activeUpdates.size
    }
} 