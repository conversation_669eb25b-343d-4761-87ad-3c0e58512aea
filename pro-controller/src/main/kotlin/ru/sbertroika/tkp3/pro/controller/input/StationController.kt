package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.StationDto
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.StationService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.model.Station
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class StationController(
    private val stationService: StationService
) : BaseController() {

    @GetMapping("/stations")
    fun getAllStations(): Flux<ApiResponse<StationDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMapMany { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, получаем станции только этого проекта
                    stationService.getStationsByProject(tokenProjectId.get(), 0, 1000, null, null, null, null)
                        .flatMapMany { result ->
                            @Suppress("UNCHECKED_CAST")
                            val stations = result["content"] as? List<Map<String, Any>> ?: emptyList()
                            Flux.fromIterable(stations.map { stationMap ->
                                StationDto(
                                    id = UUID.fromString(stationMap["id"] as String),
                                    version = stationMap["version"] as? Int,
                                    versionCreatedAt = stationMap["versionCreatedAt"] as? java.sql.Timestamp,
                                    versionCreatedBy = stationMap["versionCreatedBy"]?.let { UUID.fromString(it as String) },
                                    projectId = UUID.fromString(stationMap["projectId"] as String),
                                    tariffZoneId = stationMap["tariffZoneId"]?.let { UUID.fromString(it as String) },
                                    name = stationMap["name"] as? String,
                                    latinName = stationMap["latinName"] as? String,
                                    status = stationMap["status"] as? String,
                                    latitude = stationMap["latitude"] as? Double,
                                    longitude = stationMap["longitude"] as? Double,
                                    cityId = stationMap["cityId"]?.let { UUID.fromString(it as String) },
                                    districtId = stationMap["districtId"]?.let { UUID.fromString(it as String) },
                                    regionId = stationMap["regionId"]?.let { UUID.fromString(it as String) },
                                    countryId = stationMap["countryId"]?.let { UUID.fromString(it as String) },
                                    tags = stationMap["tags"] as? String ?: "",
                                    lastSyncDate = stationMap["lastSyncDate"] as? java.sql.Timestamp,
                                    syncStatus = stationMap["syncStatus"] as? String
                                )
                            })
                        }
                } else {
                    // Если нет projectId в токене, получаем все станции
                    stationService.getAllStations()
                }
            }
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<StationDto>("Ошибка получения станций: ${error.message}"))
            }
    }

    @GetMapping("/stations/{id}")
    fun getStationById(@PathVariable id: String): Mono<ApiResponse<StationDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                stationService.getStationById(UUID.fromString(id))
                    .flatMap { station ->
                        if (tokenProjectId.isPresent && station.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к станции из другого проекта", 
                                RuntimeException("Station projectId: ${station.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<StationDto>(RuntimeException("Доступ запрещен: станция принадлежит другому проекту"))
                        } else {
                            Mono.just(station)
                        }
                    }
            }
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении станции: ${error.message}", error)
                Mono.just(ApiResponse.error<StationDto>("Ошибка получения станции: ${error.message}"))
            }
    }

    @GetMapping("/projects/{projectId}/stations")
    fun getStationsByProject(
        @PathVariable projectId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) name: String?,
        @RequestParam(required = false) city: String?,
        @RequestParam(required = false) region: String?,
        @RequestParam(required = false) country: String?
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список станций проекта", projectId)
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(UUID.fromString(projectId))
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                stationService.getStationsByProject(
                    effectiveProjectId, page, size, name, city, region, country
                )
            }
            .map { result ->
                ApiResponse.success(result)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении станций проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения станций проекта: ${error.message}"))
            }
    }

    @PostMapping("/stations/batch")
    fun getStationsByIds(@RequestBody request: Map<String, List<String>>): Flux<ApiResponse<StationDto>> {
        val stationIds = request["stationIds"]?.map { UUID.fromString(it) } ?: emptyList()
        
        return JwtUtils.getCurrentProjectId()
            .flatMapMany { tokenProjectId ->
                stationService.getStationsByIds(stationIds)
                    .filter { station ->
                        if (tokenProjectId.isPresent) {
                            station.projectId == tokenProjectId.get()
                        } else {
                            true
                        }
                    }
            }
            .map { station ->
                ApiResponse.success(station)
            }
            .onErrorResume { error ->
                Flux.just(ApiResponse.error<StationDto>("Ошибка получения станций по ID: ${error.message}"))
            }
    }

    @PostMapping("/projects/{projectId}/stations")
    fun createStation(@PathVariable projectId: String, @RequestBody station: Station): Mono<ApiResponse<Station>> {
        logUserAction("создает новую станцию в проекте", projectId)
        
        val requestProjectUuid = UUID.fromString(projectId)
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                val effectiveProjectId = tokenProjectId.getOrDefault(requestProjectUuid)
                
                if (tokenProjectId.isPresent && requestProjectUuid != tokenProjectId.get()) {
                    logError("Попытка создания станции в другом проекте", 
                        RuntimeException("Request projectId: $requestProjectUuid, Token projectId: $tokenProjectId"))
                    Mono.error<Station>(RuntimeException("Доступ запрещен: нельзя создавать станцию в другом проекте"))
                } else {
                    stationService.createStation(effectiveProjectId, station, currentUserId)
                }
            }
            .map { createdStation ->
                logUserAction("успешно создал станцию", createdStation.name ?: "Без названия")
                ApiResponse.success(createdStation, "Станция успешно создана")
            }
            .onErrorResume { error ->
                logError("Ошибка при создании станции: ${error.message}", error)
                Mono.just(ApiResponse.error<Station>("Ошибка создания станции: ${error.message}"))
            }
    }

    @PutMapping("/stations/{id}")
    fun updateStation(@PathVariable id: String, @RequestBody station: Station): Mono<ApiResponse<Station>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                stationService.getStationById(UUID.fromString(id))
                    .flatMap { existingStation ->
                        if (tokenProjectId.isPresent && existingStation.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления станции из другого проекта", 
                                RuntimeException("Station projectId: ${existingStation.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Station>(RuntimeException("Доступ запрещен: станция принадлежит другому проекту"))
                        } else {
                            val projectIdForUpdate = existingStation.projectId ?: 
                                throw RuntimeException("Станция не привязана к проекту")
                            stationService.updateStation(UUID.fromString(id), station.copy(projectId = projectIdForUpdate), currentUserId)
                        }
                    }
            }
            .map { updatedStation ->
                ApiResponse.success(updatedStation, "Станция успешно обновлена")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении станции: ${error.message}", error)
                Mono.just(ApiResponse.error<Station>("Ошибка обновления станции: ${error.message}"))
            }
    }

    @DeleteMapping("/stations/{id}")
    fun deleteStation(@PathVariable id: String): Mono<ApiResponse<String>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                stationService.getStationById(UUID.fromString(id))
                    .flatMap { existingStation ->
                        if (tokenProjectId.isPresent && existingStation.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления станции из другого проекта", 
                                RuntimeException("Station projectId: ${existingStation.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: станция принадлежит другому проекту"))
                        } else {
                            stationService.deleteStation(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map {
                ApiResponse.success("Станция успешно удалена")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении станции: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления станции: ${error.message}"))
            }
    }

    @PostMapping("/stations/{id}/sync")
    fun syncStationWithContract(@PathVariable id: String): Mono<ApiResponse<Station>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                stationService.getStationById(UUID.fromString(id))
                    .flatMap { existingStation ->
                        if (tokenProjectId.isPresent && existingStation.projectId != tokenProjectId.get()) {
                            logError("Попытка синхронизации станции из другого проекта", 
                                RuntimeException("Station projectId: ${existingStation.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Station>(RuntimeException("Доступ запрещен: станция принадлежит другому проекту"))
                        } else {
                            stationService.syncStationWithContract(UUID.fromString(id))
                        }
                    }
            }
            .map { station ->
                ApiResponse.success(station, "Станция синхронизирована с договором")
            }
            .onErrorResume { error ->
                logError("Ошибка при синхронизации станции: ${error.message}", error)
                Mono.just(ApiResponse.error<Station>("Ошибка синхронизации станции: ${error.message}"))
            }
    }
} 