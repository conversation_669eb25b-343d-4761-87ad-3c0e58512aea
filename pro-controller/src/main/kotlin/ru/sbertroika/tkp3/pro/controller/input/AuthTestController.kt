package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils

@RestController
@RequestMapping("/api/pro/v1/auth")
class AuthTestController : BaseController() {

    @GetMapping("/test")
    fun testAuth(): Mono<ApiResponse<Map<String, Any>>> {
        return JwtUtils.getCurrentUserInfo()
            .map { userInfo ->
                logUserAction("тестирует авторизацию")
                
                val response = mapOf(
                    "message" to "Авторизация успешна",
                    "user" to mapOf(
                        "id" to userInfo.id.toString(),
                        "username" to userInfo.username,
                        "email" to userInfo.email,
                        "firstName" to userInfo.firstName,
                        "lastName" to userInfo.lastName,
                        "roles" to userInfo.roles
                    ),
                    "timestamp" to System.currentTimeMillis()
                )
                
                ApiResponse.success(response)
            }
            .onErrorResume { error ->
                logError("Ошибка при тестировании авторизации: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка авторизации: ${error.message}"))
            }
    }

    @GetMapping("/public")
    fun publicEndpoint(): Mono<ApiResponse<Map<String, Any>>> {
        val response = mapOf(
            "message" to "Публичный эндпоинт доступен без авторизации",
            "timestamp" to System.currentTimeMillis()
        )
        return Mono.just(ApiResponse.success(response))
    }
} 