package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.EmployeeService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.model.Employee
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class EmployeeController(
    private val employeeService: EmployeeService
) : BaseController() {

    @GetMapping("/projects/{projectId}/employees")
    fun getEmployeesByProject(
        @PathVariable projectId: String,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int,
        @RequestParam(required = false) fullName: String?,
        @RequestParam(required = false) personalNumber: String?,
        @RequestParam(required = false) organizationId: String?,
        @RequestParam(required = false) role: String?
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список сотрудников проекта", projectId)
        
        // Добавляем логирование параметров для отладки
        println("DEBUG: EmployeeController.getEmployeesByProject вызван с параметрами:")
        println("  projectId: $projectId")
        println("  page: $page")
        println("  size: $size")
        println("  fullName: $fullName")
        println("  personalNumber: $personalNumber")
        println("  organizationId: $organizationId")
        println("  role: $role")
        
        val organizationUuid = organizationId?.let { UUID.fromString(it) }
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(UUID.fromString(projectId))
                logUserAction("использует projectId для фильтрации", effectiveProjectId.toString())
                
                employeeService.getEmployeesByProject(
                    effectiveProjectId, page, size, fullName, personalNumber, organizationUuid, role
                )
            }.map { result ->
                println("DEBUG: EmployeeController получил результат: $result")
                ApiResponse.success(result)
            }.onErrorResume { error ->
                logError("Ошибка при получении сотрудников проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения сотрудников"))
            }
    }

    @GetMapping("/employees/{id}")
    fun getEmployeeById(@PathVariable id: String): Mono<ApiResponse<Employee>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                employeeService.getEmployeeById(UUID.fromString(id))
                    .flatMap { employee ->
                        if (tokenProjectId.isPresent && employee.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к сотруднику из другого проекта", 
                                RuntimeException("Employee projectId: ${employee.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Employee>(RuntimeException("Доступ запрещен: сотрудник принадлежит другому проекту"))
                        } else {
                            Mono.just(employee)
                        }
                    }
            }.map { employee ->
                ApiResponse.success(employee)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении сотрудника: ${error.message}", error)
                Mono.just(ApiResponse.error<Employee>("Ошибка получения сотрудника"))
            }
    }

    @PostMapping("/projects/{projectId}/employees")
    fun createEmployee(
        @PathVariable projectId: String,
        @RequestBody request: EmployeeCreateRequest
    ): Mono<ApiResponse<Employee>> {
        logUserAction("создает нового сотрудника в проекте", projectId)
        
        val requestProjectUuid = UUID.fromString(projectId)
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                val effectiveProjectId = tokenProjectId.getOrDefault(requestProjectUuid)
                
                if (tokenProjectId.isPresent && requestProjectUuid != tokenProjectId.get()) {
                    logError("Попытка создания сотрудника в другом проекте", 
                        RuntimeException("Request projectId: $requestProjectUuid, Token projectId: $tokenProjectId"))
                    Mono.error<Employee>(RuntimeException("Доступ запрещен: нельзя создавать сотрудника в другом проекте"))
                } else {
                    JwtUtils.getCurrentJwtToken()
                        .flatMap { jwtToken ->
                            employeeService.createEmployee(
                                projectId = effectiveProjectId,
                                name = request.name,
                                surname = request.surname,
                                middleName = request.middleName,
                                role = request.role,
                                personalNumber = request.personalNumber,
                                pinCode = request.pinCode,
                                organizationId = request.organizationId,
                                login = request.login,
                                email = request.email,
                                phone = request.phone,
                                jwtToken = jwtToken,
                                currentUserId = currentUserId
                            )
                        }
                }
            }.map { createdEmployee ->
                logUserAction("успешно создал сотрудника", createdEmployee.toFIO())
                ApiResponse.success(createdEmployee, "Сотрудник успешно создан")
            }.onErrorResume { error ->
                logError("Ошибка при создании сотрудника: ${error.message}", error)
                Mono.just(ApiResponse.error<Employee>("Ошибка создания сотрудника"))
            }
    }

    @PutMapping("/employees/{id}")
    fun updateEmployee(
        @PathVariable id: String,
        @RequestBody request: EmployeeUpdateRequest
    ): Mono<ApiResponse<Employee>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                employeeService.getEmployeeById(UUID.fromString(id))
                    .flatMap { existingEmployee ->
                        if (tokenProjectId.isPresent && existingEmployee.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления сотрудника из другого проекта", 
                                RuntimeException("Employee projectId: ${existingEmployee.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<Employee>(RuntimeException("Доступ запрещен: сотрудник принадлежит другому проекту"))
                        } else {
                            JwtUtils.getCurrentJwtToken()
                                .flatMap { jwtToken ->
                                    val projectIdForUpdate = existingEmployee.projectId ?: 
                                        throw RuntimeException("Сотрудник не привязан к проекту")
                                    employeeService.updateEmployee(
                                        employeeId = UUID.fromString(id),
                                        projectId = projectIdForUpdate,
                                        name = request.name,
                                        surname = request.surname,
                                        middleName = request.middleName,
                                        role = request.role,
                                        personalNumber = request.personalNumber,
                                        pinCode = request.pinCode,
                                        organizationId = request.organizationId,
                                        login = request.login,
                                        email = request.email,
                                        phone = request.phone,
                                        enabled = request.enabled,
                                        jwtToken = jwtToken,
                                        currentUserId = currentUserId
                                    )
                                }
                        }
                    }
            }.map { updatedEmployee ->
                ApiResponse.success(updatedEmployee, "Сотрудник успешно обновлен")
            }.onErrorResume { error ->
                logError("Ошибка при обновлении сотрудника: ${error.message}", error)
                Mono.just(ApiResponse.error<Employee>("Ошибка обновления сотрудника"))
            }
    }

    @DeleteMapping("/employees/{id}")
    fun deleteEmployee(@PathVariable id: String): Mono<ApiResponse<String>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                employeeService.getEmployeeById(UUID.fromString(id))
                    .flatMap { existingEmployee ->
                        if (tokenProjectId.isPresent && existingEmployee.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления сотрудника из другого проекта", 
                                RuntimeException("Employee projectId: ${existingEmployee.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: сотрудник принадлежит другому проекту"))
                        } else {
                            JwtUtils.getCurrentJwtToken()
                                .flatMap { jwtToken ->
                                    employeeService.deleteEmployee(UUID.fromString(id), jwtToken, currentUserId)
                                }
                        }
                    }
            }.map {
                ApiResponse.success("Сотрудник успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении сотрудника: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления сотрудника"))
            }
    }
    
    @GetMapping("/keycloak/roles")
    fun getAvailableRoles(): Mono<ApiResponse<List<KeycloakRoleResponse>>> {
        return employeeService.getAvailableKeycloakRoles()
            .map { roles ->
                val roleResponses = roles.map { role ->
                    KeycloakRoleResponse(
                        id = role.id ?: "",
                        name = role.name,
                        description = role.description ?: "",
                        composite = role.composite,
                        clientRole = role.clientRole
                    )
                }
                ApiResponse.success(roleResponses)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении ролей Keycloak: ${error.message}", error)
                Mono.just(ApiResponse.error("Ошибка получения ролей Keycloak"))
            }
    }
} 