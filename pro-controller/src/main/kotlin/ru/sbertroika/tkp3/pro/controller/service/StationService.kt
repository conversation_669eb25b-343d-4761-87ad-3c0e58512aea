package ru.sbertroika.tkp3.pro.controller.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.StationDto
import ru.sbertroika.tkp3.pro.controller.output.StationDisplayRepository
import ru.sbertroika.tkp3.pro.controller.output.StationRepository
import ru.sbertroika.tkp3.pro.model.Station
import ru.sbertroika.tkp3.pro.model.StationStatus
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class StationService(
    private val stationRepository: StationRepository,
    private val stationDisplayRepository: StationDisplayRepository,
    private val geographicService: GeographicService
) {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Получить все станции для отображения
     */
    fun getAllStations(): Flux<StationDto> {
        return stationDisplayRepository.findAll()
    }

    /**
     * Получить станции проекта с пагинацией и фильтрами
     */
    fun getStationsByProject(
        projectId: UUID,
        page: Int = 0,
        size: Int = 10,
        name: String? = null,
        city: String? = null,
        region: String? = null,
        country: String? = null
    ): Mono<Map<String, Any>> {
        val offset = page * size

        return Mono.zip(
            stationDisplayRepository.findByProjectIdWithFilters(
                projectId, name, city, region, country, size, offset
            ).collectList(),
            stationRepository.countByProjectIdWithFilters(projectId, name, city, region, country)
        ).map { result ->
            val stations = result.t1
            val total = result.t2
            val totalPages = if (total > 0) ((total - 1) / size) + 1 else 0

            mapOf(
                "content" to stations,
                "pagination" to mapOf(
                    "page" to page,
                    "size" to size,
                    "totalElements" to total,
                    "totalPages" to totalPages
                )
            )
        }
    }

    /**
     * Получить станцию по ID для отображения
     */
    fun getStationById(id: UUID): Mono<StationDto> {
        return stationDisplayRepository.findStationById(id)
    }

    /**
     * Получить станции по списку ID для отображения
     */
    fun getStationsByIds(ids: List<UUID>): Flux<StationDto> {
        return stationDisplayRepository.findByIds(ids)
    }

    /**
     * Создать станцию
     */
    fun createStation(projectId: UUID, station: Station, createdBy: UUID): Mono<Station> {
        logger.info("Создание станции: name=${station.name}, latinName=${station.latinName}")
        val now = Timestamp.valueOf(LocalDateTime.now())
        val newStation = Station(
            id = UUID.randomUUID(),
            version = 1,
            versionCreatedAt = now,
            versionCreatedBy = createdBy,
            projectId = projectId,
            tariffZoneId = station.tariffZoneId,
            name = station.name,
            latinName = station.latinName,
            status = StationStatus.ACTIVE.name,
            latitude = station.latitude,
            longitude = station.longitude,
            cityId = station.cityId,
            districtId = station.districtId,
            regionId = station.regionId,
            countryId = station.countryId,
            tags = station.tags,
            lastSyncDate = null,
            syncStatus = null
        )

        // Обработка географических данных
        return processGeographicData(newStation)
            .flatMap { processedStation ->
                stationRepository.save(processedStation)
            }
    }

    /**
     * Обновить станцию
     */
    fun updateStation(id: UUID, updatedStation: Station, updatedBy: UUID): Mono<Station> {
        logger.info("Обновление станции: name=${updatedStation.name}, latinName=${updatedStation.latinName}")
        return stationRepository.findStationById(id)
            .flatMap { existingStation ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                val newVersion = (existingStation.version ?: 0) + 1
                try {
                    // Создаем новую версию станции
                    val newStation = Station(
                        id = id,
                        version = newVersion,
                        versionCreatedAt = now,
                        versionCreatedBy = updatedBy,
                        projectId = existingStation.projectId,
                        tariffZoneId = updatedStation.tariffZoneId,
                        name = updatedStation.name,
                        latinName = updatedStation.latinName,
                        status = StationStatus.ACTIVE.name,
                        latitude = updatedStation.latitude,
                        longitude = updatedStation.longitude,
                        cityId = updatedStation.cityId,
                        districtId = updatedStation.districtId,
                        regionId = updatedStation.regionId,
                        countryId = updatedStation.countryId,
                        tags = updatedStation.tags,
                        lastSyncDate = existingStation.lastSyncDate,
                        syncStatus = existingStation.syncStatus
                    )

                    // Обработка географических данных

                    processGeographicData(newStation)
                        .flatMap { processedStation ->
                            stationRepository.save(processedStation)
                        }
                } catch (e: Exception) {
                    logger.error("err update", e)
                    throw e
                }
            }
    }

    /**
     * Удалить станцию (логическое удаление)
     */
    fun deleteStation(id: UUID, deletedBy: UUID): Mono<Void> {
        return stationRepository.findStationById(id)
            .flatMap { station ->
                station.status = StationStatus.IS_DELETED.name
                station.version = (station.version ?: 0) + 1
                station.versionCreatedBy = deletedBy
                station.versionCreatedAt = Timestamp.valueOf(LocalDateTime.now())
                stationRepository.save(station)
            }
            .then()
    }

    /**
     * Синхронизировать станцию с договором
     */
    fun syncStationWithContract(id: UUID): Mono<Station> {
        return stationRepository.findStationById(id)
            .flatMap { station ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                station.lastSyncDate = now
                station.syncStatus = "synced"
                stationRepository.save(station)
            }
    }

    /**
     * Обработка географических данных
     */
    private fun processGeographicData(station: Station): Mono<Station> {
        logger.info("Обработка географических данных для станции: name=${station.name}, latinName=${station.latinName}")
        // Поскольку мы убрали избыточные поля из модели,
        // географические данные должны обрабатываться на уровне UI
        // Здесь просто возвращаем станцию как есть
        return Mono.just(station)
    }
} 