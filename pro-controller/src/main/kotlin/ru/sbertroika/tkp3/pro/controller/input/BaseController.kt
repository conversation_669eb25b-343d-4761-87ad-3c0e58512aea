package ru.sbertroika.tkp3.pro.controller.input

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.server.reactive.ServerHttpRequest
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import ru.sbertroika.tkp3.pro.controller.util.RequestBodyLogger
import ru.sbertroika.tkp3.pro.controller.util.UserInfo

/**
 * Базовый контроллер с общей логикой для всех контроллеров
 */
abstract class BaseController {
    
    protected val logger: Logger = LoggerFactory.getLogger(this::class.java)
    
    /**
     * Получить информацию о текущем пользователе и выполнить действие
     */
    protected fun <T> withUserInfo(action: (UserInfo) -> Mono<T>): Mono<T> {
        return JwtUtils.getCurrentUserInfo()
            .flatMap(action)
    }
    
    /**
     * Логировать действие пользователя
     */
    protected fun logUserAction(action: String, details: String? = null) {
        JwtUtils.getCurrentUserInfo()
            .subscribe { userInfo ->
                val message = if (details != null) {
                    "Пользователь ${userInfo.username} (${userInfo.id}) $action: $details"
                } else {
                    "Пользователь ${userInfo.username} (${userInfo.id}) $action"
                }
                logger.info(message)
            }
    }
    
    /**
     * Логировать ошибку с информацией о пользователе
     */
    protected fun logError(message: String, error: Throwable) {
        JwtUtils.getCurrentUserInfo()
            .subscribe { userInfo ->
                logger.error("Ошибка для пользователя ${userInfo.username} (${userInfo.id}): $message", error)
            }
    }
    
    /**
     * Логировать тело запроса
     */
    protected fun logRequestBody(request: ServerHttpRequest, operation: String) {
        RequestBodyLogger.logRequestBody(request, logger, operation)
    }
    
    /**
     * Логировать тело запроса с ограничением длины
     */
    protected fun logRequestBodyLimited(request: ServerHttpRequest, operation: String, maxLength: Int = 1000) {
        RequestBodyLogger.logRequestBodyLimited(request, logger, operation, maxLength)
    }
} 