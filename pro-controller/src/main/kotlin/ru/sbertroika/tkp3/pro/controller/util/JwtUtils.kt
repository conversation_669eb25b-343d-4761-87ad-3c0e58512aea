package ru.sbertroika.tkp3.pro.controller.util

import org.slf4j.LoggerFactory
import org.springframework.security.core.context.ReactiveSecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import reactor.core.publisher.Mono
import java.util.*

object JwtUtils {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Получить UUID пользователя из JWT токена
     */
    fun getCurrentUserId(): Mono<UUID> {
        return ReactiveSecurityContextHolder.getContext()
            .doOnError { logger.error("getCurrentUserId: Ошибка получения контекста безопасности", it) }
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                UUID.fromString(jwt.subject)
            }
    }

    /**
     * Получить имя пользователя из JWT токена
     */
    fun getCurrentUsername(): Mono<String> {
        return ReactiveSecurityContextHolder.getContext()
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                jwt.getClaimAsString("preferred_username") ?: jwt.subject
            }
    }

    /**
     * Получить email пользователя из JWT токена
     */
    fun getCurrentUserEmail(): Mono<String?> {
        return ReactiveSecurityContextHolder.getContext()
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                jwt.getClaimAsString("email")
            }
    }

    /**
     * Получить роли пользователя из JWT токена
     */
    fun getCurrentUserRoles(): Mono<List<String>> {
        return ReactiveSecurityContextHolder.getContext()
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                val realmAccess = jwt.getClaimAsMap("realm_access")
                val roles = realmAccess?.get("roles") as? List<*>
                roles?.mapNotNull { it as? String } ?: emptyList()
            }
    }

    /**
     * Получить полную информацию о пользователе из JWT токена
     */
    fun getCurrentUserInfo(): Mono<UserInfo> {
        return ReactiveSecurityContextHolder.getContext()
            .doOnError { logger.error("getCurrentUserInfo: Ошибка получения контекста безопасности", it) }
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                UserInfo(
                    id = UUID.fromString(jwt.subject),
                    username = jwt.getClaimAsString("preferred_username") ?: jwt.subject,
                    email = jwt.getClaimAsString("email"),
                    firstName = jwt.getClaimAsString("given_name"),
                    lastName = jwt.getClaimAsString("family_name"),
                    roles = getRolesFromJwt(jwt)
                )
            }
    }

    private fun getRolesFromJwt(jwt: Jwt): List<String> {
        val realmAccess = jwt.getClaimAsMap("realm_access")
        val roles = realmAccess?.get("roles") as? List<*>
        return roles?.mapNotNull { it as? String } ?: emptyList()
    }

    /**
     * Получить JWT токен как строку
     */
    fun getCurrentJwtToken(): Mono<String> {
        return ReactiveSecurityContextHolder.getContext()
            .doOnError { logger.error("getCurrentJwtToken: Ошибка получения контекста безопасности", it) }
            .map { context ->
                val jwt = context.authentication.principal as Jwt
                jwt.tokenValue
            }
    }

    /**
     * Получить projectId из JWT токена
     */
    fun getCurrentProjectId(): Mono<Optional<UUID>> = ReactiveSecurityContextHolder.getContext()
        .doOnError { logger.error("getCurrentProjectId: Ошибка получения контекста безопасности", it) }
        .map { context ->
            val jwt = context.authentication.principal as Jwt
            val projectIdString = jwt.getClaimAsString("projectId")
            val projectId = projectIdString?.let {
                try {
                    UUID.fromString(it)
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
            Optional.ofNullable(projectId)
        }
}

/**
 * Информация о пользователе
 */
data class UserInfo(
    val id: UUID,
    val username: String,
    val email: String?,
    val firstName: String?,
    val lastName: String?,
    val roles: List<String>
) 