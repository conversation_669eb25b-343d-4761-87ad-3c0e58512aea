package ru.sbertroika.tkp3.pro.controller.input

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.*

data class TerminalUserCreateRequest(
    @field:NotNull(message = "ID профиля обязателен")
    val profileId: UUID,

    @field:NotNull(message = "ID проекта обязателен")
    val projectId: UUID,

    @field:NotNull(message = "ID организации обязателен")
    val organizationId: UUID,

    @field:NotBlank(message = "Имя обязательно")
    val name: String,

    @field:NotBlank(message = "Фамилия обязательна")
    val surname: String,

    val middleName: String? = null,

    @field:NotBlank(message = "Роль обязательна")
    val role: String,

    val personalNumber: String? = null,

    @field:NotBlank(message = "PIN-код обязателен")
    val pinCode: String,

    val login: String? = null,

    val email: String? = null,

    val phone: String? = null
) 