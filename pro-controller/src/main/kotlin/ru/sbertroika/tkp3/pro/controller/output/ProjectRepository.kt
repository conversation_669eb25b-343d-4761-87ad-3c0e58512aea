package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.Project
import java.util.*

@Repository
interface ProjectRepository : ReactiveCrudRepository<Project, UUID> {

    @Query("""
        SELECT * FROM project 
        WHERE pr_status != 'IS_DELETED'
        AND (:status IS NULL OR pr_status = :status)
        ORDER BY pr_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllProjectsWithFilters(
        status: String?,
        limit: Int,
        offset: Int
    ): Flux<Project>

    @Query("""
        SELECT COUNT(*) FROM project 
        WHERE pr_status != 'IS_DELETED'
        AND (:status IS NULL OR pr_status = :status)
    """)
    fun countProjectsWithFilters(
        status: String?
    ): Flux<Long>

    @Query("SELECT * FROM project WHERE pr_id = :id AND pr_status != 'IS_DELETED'")
    fun findProjectById(id: UUID): Mono<Project>

    @Query("SELECT * FROM project WHERE pr_status = :status ORDER BY pr_version_created_at DESC")
    fun findByStatus(status: String): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_contract_id = :contractId AND pr_status != 'IS_DELETED'")
    fun findByContractId(contractId: UUID): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_region = :region AND pr_status != 'IS_DELETED' ORDER BY pr_version_created_at DESC")
    fun findByRegion(region: String): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_project_type = :projectType AND pr_status != 'IS_DELETED' ORDER BY pr_version_created_at DESC")
    fun findByProjectType(projectType: String): Flux<Project>

    @Query("""
        SELECT COALESCE(MAX(p.p_index), 0) FROM project p
        INNER JOIN (
            SELECT pr_id, MAX(pr_version) as max_version
            FROM project
            GROUP BY pr_id
        ) latest ON p.pr_id = latest.pr_id AND p.pr_version = latest.max_version
    """)
    fun findMaxIndex(): Flux<Int>
} 