package ru.sbertroika.tkp3.pro.controller.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.VehicleCreateRequest
import ru.sbertroika.tkp3.pro.controller.model.VehicleDto
import ru.sbertroika.tkp3.pro.controller.model.VehicleUpdateRequest
import ru.sbertroika.tkp3.pro.controller.output.VehicleRepository
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import java.sql.Timestamp
import java.util.*

@Service
class VehicleService(
    private val vehicleRepository: VehicleRepository
) {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Получить все транспортные средства с фильтрацией и пагинацией
     */
    fun getVehiclesWithFilters(
        projectId: UUID,
        type: VehicleType? = null,
        status: VehicleStatus? = null,
        organizationId: UUID? = null,
        search: String? = null,
        page: Int = 0,
        size: Int = 10
    ): Mono<Map<String, Any>> {
        val offset = page * size
        
        return Mono.zip(
            vehicleRepository.findAllVehiclesWithFilters(
                projectId = projectId,
                type = type,
                status = status,
                organizationId = organizationId,
                search = search,
                size = size,
                offset = offset
            ).collectList(),
            vehicleRepository.countVehiclesWithFilters(
                projectId = projectId,
                type = type,
                status = status,
                organizationId = organizationId,
                search = search
            )
        ).map { tuple ->
            val vehicles = tuple.t1
            val total = tuple.t2
            mapOf(
                "content" to vehicles,
                "totalElements" to total,
                "totalPages" to ((total + size - 1) / size).toInt(),
                "currentPage" to page,
                "size" to size
            )
        }
    }

    /**
     * Получить транспортное средство по ID
     */
    fun getVehicleById(vehicleId: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
    }

    /**
     * Получить транспортное средство по ID (DTO)
     */
    fun getVehicleDtoById(vehicleId: UUID): Mono<VehicleDto> {
        return getVehicleById(vehicleId)
            .map { it.toDto() }
    }

    /**
     * Получить все версии транспортного средства
     */
    fun getAllVersionsByVehicleId(vehicleId: UUID): Flux<Vehicle> {
        return vehicleRepository.findAllVersionsByVehicleId(vehicleId)
    }

    /**
     * Получить все версии транспортного средства (DTO)
     */
    fun getAllVersionsDtoByVehicleId(vehicleId: UUID): Flux<VehicleDto> {
        return getAllVersionsByVehicleId(vehicleId)
            .map { it.toDto() }
    }

    /**
     * Создать новое транспортное средство
     */
    fun createVehicle(vehicle: Vehicle, createdBy: UUID): Mono<Vehicle> {
        return vehicleRepository.existsByProjectIdAndNumber(vehicle.projectId!!, vehicle.number!!)
            .flatMap { exists ->
                if (exists) {
                    Mono.error(RuntimeException("Транспортное средство с таким номером уже существует в проекте"))
                } else {
                    val newVehicle = vehicle.copy(
                        version = 1,
                        versionCreatedAt = Timestamp(System.currentTimeMillis()),
                        versionCreatedBy = createdBy,
                        status = VehicleStatus.ACTIVE
                    )
                    vehicleRepository.save(newVehicle)
                }
            }
    }

    /**
     * Создать новое транспортное средство из DTO
     */
    fun createVehicleFromDto(request: VehicleCreateRequest, createdBy: UUID): Mono<VehicleDto> {
        val vehicle = Vehicle(
            version = 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = createdBy,
            projectId = request.projectId,
            organizationId = request.organizationId,
            type = VehicleType.valueOf(request.type),
            number = request.number,
            status = VehicleStatus.valueOf(request.status),
            tags = request.tags
        )
        
        return createVehicle(vehicle, createdBy)
            .map { it.toDto() }
            .doOnError {
                logger.error("Error creating vehicle", it)
            }
    }

    /**
     * Обновить транспортное средство (создать новую версию)
     */
    fun updateVehicle(vehicleId: UUID, updatedVehicle: Vehicle, updatedBy: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
            .flatMap { existingVehicle ->
                // Проверяем, не занят ли номер другим транспортным средством
                if (updatedVehicle.number != existingVehicle.number) {
                    vehicleRepository.existsByProjectIdAndNumber(updatedVehicle.projectId!!, updatedVehicle.number!!)
                        .flatMap { exists ->
                            if (exists) {
                                Mono.error(RuntimeException("Транспортное средство с таким номером уже существует в проекте"))
                            } else {
                                createNewVersion(existingVehicle, updatedVehicle, updatedBy)
                            }
                        }
                } else {
                    createNewVersion(existingVehicle, updatedVehicle, updatedBy)
                }
            }
    }

    /**
     * Обновить транспортное средство из DTO
     */
    fun updateVehicleFromDto(vehicleId: UUID, request: VehicleUpdateRequest, updatedBy: UUID): Mono<VehicleDto> {
        return getVehicleById(vehicleId)
            .flatMap { existingVehicle ->
                val updatedVehicle = existingVehicle.copy(
                    organizationId = request.organizationId ?: existingVehicle.organizationId,
                    type = request.type?.let { VehicleType.valueOf(it) } ?: existingVehicle.type,
                    number = request.number ?: existingVehicle.number,
                    status = request.status?.let { VehicleStatus.valueOf(it) } ?: existingVehicle.status,
                    tags = request.tags ?: existingVehicle.tags
                )
                updateVehicle(vehicleId, updatedVehicle, updatedBy)
            }
            .map { it.toDto() }
    }

    /**
     * Удалить транспортное средство (логическое удаление)
     */
    fun deleteVehicle(vehicleId: UUID, deletedBy: UUID): Mono<Vehicle> {
        return vehicleRepository.findVehicleById(vehicleId)
            .switchIfEmpty(Mono.error(RuntimeException("Транспортное средство не найдено")))
            .flatMap { existingVehicle ->
                val deletedVehicle = existingVehicle.copy(
                    version = existingVehicle.version!! + 1,
                    versionCreatedAt = Timestamp(System.currentTimeMillis()),
                    versionCreatedBy = deletedBy,
                    status = VehicleStatus.IS_DELETED
                )
                vehicleRepository.save(deletedVehicle)
            }
    }

    /**
     * Получить транспортные средства по проекту
     */
    fun getVehiclesByProject(projectId: UUID): Flux<Vehicle> {
        return vehicleRepository.findByProjectId(projectId)
    }

    /**
     * Получить транспортные средства по проекту (DTO)
     */
    fun getVehiclesDtoByProject(projectId: UUID): Flux<VehicleDto> {
        return getVehiclesByProject(projectId)
            .map { it.toDto() }
    }

    /**
     * Получить транспортные средства по организации
     */
    fun getVehiclesByOrganization(organizationId: UUID): Flux<Vehicle> {
        return vehicleRepository.findByOrganizationId(organizationId)
    }

    /**
     * Получить транспортные средства по организации (DTO)
     */
    fun getVehiclesDtoByOrganization(organizationId: UUID): Flux<VehicleDto> {
        return getVehiclesByOrganization(organizationId)
            .map { it.toDto() }
    }

    /**
     * Получить транспортные средства по типу
     */
    fun getVehiclesByType(type: VehicleType): Flux<Vehicle> {
        return vehicleRepository.findByType(type)
    }

    /**
     * Получить транспортные средства по типу (DTO)
     */
    fun getVehiclesDtoByType(type: VehicleType): Flux<VehicleDto> {
        return getVehiclesByType(type)
            .map { it.toDto() }
    }

    /**
     * Получить транспортные средства по статусу
     */
    fun getVehiclesByStatus(status: VehicleStatus): Flux<Vehicle> {
        return vehicleRepository.findByStatus(status)
    }

    /**
     * Получить транспортные средства по статусу (DTO)
     */
    fun getVehiclesDtoByStatus(status: VehicleStatus): Flux<VehicleDto> {
        return getVehiclesByStatus(status)
            .map { it.toDto() }
    }

    /**
     * Создать новую версию транспортного средства
     */
    private fun createNewVersion(existingVehicle: Vehicle, updatedVehicle: Vehicle, updatedBy: UUID): Mono<Vehicle> {
        val newVehicle = existingVehicle.copy(
            version = existingVehicle.version!! + 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = updatedBy,
            type = updatedVehicle.type,
            number = updatedVehicle.number,
            status = updatedVehicle.status,
            organizationId = updatedVehicle.organizationId,
            tags = updatedVehicle.tags
        )
        return vehicleRepository.save(newVehicle)
    }

    /**
     * Конвертировать Vehicle в VehicleDto
     */
    fun Vehicle.toDto(): VehicleDto {
        return VehicleDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            projectId = this.projectId,
            organizationId = this.organizationId,
            type = this.type?.name,
            number = this.number,
            status = this.status?.name,
            tags = this.tags
        )
    }
} 