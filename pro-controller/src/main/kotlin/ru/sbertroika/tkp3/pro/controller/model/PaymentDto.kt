package ru.sbertroika.tkp3.pro.controller.model

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.ZonedDateTime
import java.util.*

data class PaymentDto(
    val projectId: UUID?,
    val orgId: UUID?,
    val orgName: String?,
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime?,
    
    val trxId: UUID?,
    val ticketId: UUID?,
    val ticketSeries: String?,
    val ticketNumber: String?,
    val routeId: UUID?,
    val routeNumber: String?,
    val routeName: String?,
    val amount: Float?,
    val payMethod: String?,
    val driverFio: String?,
    val conductorFio: String?,
    val terminalSerial: String?,
    val shiftNum: Int?,
    val ern: Int?,
    val stantionFrom: String?,
    val stantionTo: String?,
    val tariffName: String?,
    val productName: String?,
    val vhNumber: String?,
    val vhType: String?,
    val maskedPan: String?,
    val paySystem: String?,
    val cardUid: String?,
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val recordAt: ZonedDateTime?
)

data class PaymentMethodDto(
    val value: String,
    val label: String
) 