package ru.sbertroika.tkp3.pro.controller.util

import com.ongres.scram.common.bouncycastle.pbkdf2.KeyParameter
import com.ongres.scram.common.bouncycastle.pbkdf2.PKCS5S2ParametersGenerator
import com.ongres.scram.common.bouncycastle.pbkdf2.SHA256Digest
import java.security.SecureRandom

fun calculateHashByPin(password: String): String {
    val gen = PKCS5S2ParametersGenerator(SHA256Digest())
    val random = SecureRandom()
    val salt = ByteArray(16)
    random.nextBytes(salt)
    gen.init(password.toByteArray(), salt, 27500)
    val passwordHash = bytes2HexStr((gen.generateDerivedParameters(256) as KeyParameter).key)
    val saltStr = bytes2HexStr(salt)
    return "$passwordHash.$saltStr"
}

fun bytes2HexStr(bytes: ByteArray?, lts: Boolean = false): String? {
    if (bytes == null) {
        return null
    }

    var workBytes = bytes
    if (lts) {
        workBytes = bytes.reversedArray()
    }

    val sb = StringBuilder()
    var temp: String
    for (b in workBytes) {
        temp = Integer.toHexString(b.toPositiveInt())
        if (temp.length == 1) {
            temp = "0$temp"
        }

        sb.append(temp)
    }

    return sb.toString().uppercase()
}

fun Byte.toPositiveInt() = this.toInt() and 0xFF