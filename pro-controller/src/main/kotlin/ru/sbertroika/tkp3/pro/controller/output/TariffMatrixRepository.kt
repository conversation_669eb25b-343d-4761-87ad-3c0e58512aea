package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.ProductDictRowTariffMatrixPrice
import java.time.LocalDateTime
import java.util.*

@Service
open class TariffMatrixRepository(
    @Qualifier("mainR2dbcEntityOperations")
    private val template: R2dbcEntityOperations
) {

    /**
     * Найти все тарифные матрицы по ProductDictRow ID
     */
    fun findByProductDictRowId(productDictRowId: UUID): Flux<ProductDictRowTariffMatrixPrice> {
        val sql = """
            SELECT pdrtmp.*, 
                   st_from.st_name as station_from_name,
                   st_to.st_name as station_to_name
            FROM product_dict_row_tariff_matrix_price pdrtmp
            LEFT JOIN station st_from ON pdrtmp.st_from_id = st_from.st_id AND st_from.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_from.st_id
            )
            LEFT JOIN station st_to ON pdrtmp.st_to_id = st_to.st_id AND st_to.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_to.st_id
            )
            WHERE pdrtmp.pdr_id = :productDictRowId
            AND pdrtmp.pdrtmp_version = (
                SELECT MAX(pdrtmp2.pdrtmp_version) 
                FROM product_dict_row_tariff_matrix_price pdrtmp2 
                WHERE pdrtmp2.pdrtmp_id = pdrtmp.pdrtmp_id
            )
            ORDER BY st_from.st_name, st_to.st_name
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("productDictRowId", productDictRowId)
            .map { row, _ ->
                ProductDictRowTariffMatrixPrice(
                    id = row.get("pdrtmp_id", UUID::class.java),
                    version = row.get("pdrtmp_version", Int::class.java),
                    versionCreatedAt = row.get("pdrtmp_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdrtmp_version_created_by", UUID::class.java),
                    productDicRowId = row.get("pdr_id", UUID::class.java),
                    stationFromId = row.get("st_from_id", UUID::class.java),
                    stationToId = row.get("st_to_id", UUID::class.java),
                    amount = row.get("amount", Long::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти тарифную матрицу по ID последней версии
     */
    fun findByIdLatestVersion(id: UUID): Mono<ProductDictRowTariffMatrixPrice> {
        val sql = """
            SELECT pdrtmp.*, 
                   st_from.st_name as station_from_name,
                   st_to.st_name as station_to_name
            FROM product_dict_row_tariff_matrix_price pdrtmp
            LEFT JOIN station st_from ON pdrtmp.st_from_id = st_from.st_id AND st_from.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_from.st_id
            )
            LEFT JOIN station st_to ON pdrtmp.st_to_id = st_to.st_id AND st_to.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_to.st_id
            )
            WHERE pdrtmp.pdrtmp_id = :id
            AND pdrtmp.pdrtmp_version = (
                SELECT MAX(pdrtmp2.pdrtmp_version) 
                FROM product_dict_row_tariff_matrix_price pdrtmp2 
                WHERE pdrtmp2.pdrtmp_id = :id
            )
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ ->
                ProductDictRowTariffMatrixPrice(
                    id = row.get("pdrtmp_id", UUID::class.java),
                    version = row.get("pdrtmp_version", Int::class.java),
                    versionCreatedAt = row.get("pdrtmp_version_created_at", java.sql.Timestamp::class.java),
                    versionCreatedBy = row.get("pdrtmp_version_created_by", UUID::class.java),
                    productDicRowId = row.get("pdr_id", UUID::class.java),
                    stationFromId = row.get("st_from_id", UUID::class.java),
                    stationToId = row.get("st_to_id", UUID::class.java),
                    amount = row.get("amount", Long::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .one()
    }

    /**
     * Найти максимальную версию по ID
     */
    fun findMaxVersionById(id: UUID): Mono<Int?> {
        val sql = "SELECT MAX(pdrtmp_version) FROM product_dict_row_tariff_matrix_price WHERE pdrtmp_id = :id"
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ -> row.get(0, Int::class.java) }
            .one()
    }

    /**
     * Найти тарифную матрицу по ProductDictRow ID и станциям
     */
    fun findByProductDictRowAndStations(productDictRowId: UUID, stationFromId: UUID, stationToId: UUID): Flux<ProductDictRowTariffMatrixPrice> {
        val sql = """
            SELECT pdrtmp.*
            FROM product_dict_row_tariff_matrix_price pdrtmp
            WHERE pdrtmp.pdr_id = :productDictRowId
            AND pdrtmp.st_from_id = :stationFromId
            AND pdrtmp.st_to_id = :stationToId
            AND pdrtmp.pdrtmp_version = (
                SELECT MAX(pdrtmp2.pdrtmp_version) 
                FROM product_dict_row_tariff_matrix_price pdrtmp2 
                WHERE pdrtmp2.pdrtmp_id = pdrtmp.pdrtmp_id
            )
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("productDictRowId", productDictRowId)
            .bind("stationFromId", stationFromId)
            .bind("stationToId", stationToId)
            .map { row, _ ->
                ProductDictRowTariffMatrixPrice(
                    id = row.get("pdrtmp_id", UUID::class.java),
                    version = row.get("pdrtmp_version", Int::class.java),
                    versionCreatedAt = row.get("pdrtmp_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdrtmp_version_created_by", UUID::class.java),
                    productDicRowId = row.get("pdr_id", UUID::class.java),
                    stationFromId = row.get("st_from_id", UUID::class.java),
                    stationToId = row.get("st_to_id", UUID::class.java),
                    amount = row.get("amount", Long::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }

    /**
     * Сохранить тарифную матрицу
     */
    fun save(tariffMatrix: ProductDictRowTariffMatrixPrice): Mono<ProductDictRowTariffMatrixPrice> {
        return template.insert(ProductDictRowTariffMatrixPrice::class.java)
            .using(tariffMatrix)
    }

    /**
     * Удалить тарифную матрицу (логическое удаление)
     */
    fun delete(id: UUID): Mono<Void> {
        return findByIdLatestVersion(id)
            .flatMap { existing ->
                findMaxVersionById(id)
                    .flatMap { maxVersion ->
                        val deleted = existing.copy(
                            version = (maxVersion ?: 0) + 1,
                            versionCreatedAt = java.sql.Timestamp(System.currentTimeMillis()),
                            tags = "DELETED"
                        )
                        save(deleted).then()
                    }
            }
    }

    /**
     * Найти тарифные матрицы по маршруту
     */
    fun findByRouteId(routeId: UUID): Flux<ProductDictRowTariffMatrixPrice> {
        val sql = """
            SELECT pdrtmp.*, 
                   st_from.st_name as station_from_name,
                   st_to.st_name as station_to_name
            FROM product_dict_row_tariff_matrix_price pdrtmp
            LEFT JOIN station st_from ON pdrtmp.st_from_id = st_from.st_id AND st_from.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_from.st_id
            )
            LEFT JOIN station st_to ON pdrtmp.st_to_id = st_to.st_id AND st_to.st_version = (
                SELECT MAX(st2.st_version) FROM station st2 WHERE st2.st_id = st_to.st_id
            )
            INNER JOIN route_station rs_from ON st_from.st_id = rs_from.st_id AND rs_from.r_id = :routeId
            INNER JOIN route_station rs_to ON st_to.st_id = rs_to.st_id AND rs_to.r_id = :routeId
            WHERE pdrtmp.pdrtmp_version = (
                SELECT MAX(pdrtmp2.pdrtmp_version) 
                FROM product_dict_row_tariff_matrix_price pdrtmp2 
                WHERE pdrtmp2.pdrtmp_id = pdrtmp.pdrtmp_id
            )
            ORDER BY st_from.st_name, st_to.st_name
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("routeId", routeId)
            .map { row, _ ->
                ProductDictRowTariffMatrixPrice(
                    id = row.get("pdrtmp_id", UUID::class.java),
                    version = row.get("pdrtmp_version", Int::class.java),
                    versionCreatedAt = row.get("pdrtmp_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdrtmp_version_created_by", UUID::class.java),
                    productDicRowId = row.get("pdr_id", UUID::class.java),
                    stationFromId = row.get("st_from_id", UUID::class.java),
                    stationToId = row.get("st_to_id", UUID::class.java),
                    amount = row.get("amount", Long::class.java),
                    tags = row.get("tags", String::class.java)
                )
            }
            .all()
    }
} 