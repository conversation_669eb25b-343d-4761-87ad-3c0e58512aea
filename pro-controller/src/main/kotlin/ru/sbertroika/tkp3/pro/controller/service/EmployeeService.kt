package ru.sbertroika.tkp3.pro.controller.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.input.TerminalUserCreateRequest
import ru.sbertroika.tkp3.pro.controller.input.TerminalUserUpdateRequest
import ru.sbertroika.tkp3.pro.controller.output.EmployeeRepository
import ru.sbertroika.tkp3.pro.controller.util.calculateHashByPin
import ru.sbertroika.tkp3.pro.model.Employee
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class EmployeeService(
    private val employeeRepository: EmployeeRepository,
    private val keycloakIntegrationService: KeycloakIntegrationService,
    private val tmsIntegrationService: TmsIntegrationService
) {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    /**
     * Получить сотрудников проекта с фильтрацией и пагинацией
     */
    fun getEmployeesByProject(
        projectId: UUID,
        page: Int,
        size: Int,
        fullName: String?,
        personalNumber: String?,
        organizationId: UUID?,
        role: String?
    ): Mono<Map<String, Any>> {
        val offset = page * size
        
        // Добавляем логирование параметров для отладки
        println("DEBUG: EmployeeService.getEmployeesByProject вызван с параметрами:")
        println("  projectId: $projectId")
        println("  page: $page")
        println("  size: $size")
        println("  offset: $offset")
        println("  fullName: $fullName")
        println("  personalNumber: $personalNumber")
        println("  organizationId: $organizationId")
        println("  role: $role")

        return Mono.zip(
            employeeRepository.findEmployeesByProjectWithFilters(
                projectId = projectId,
                fullName = fullName,
                personalNumber = personalNumber,
                organizationId = organizationId,
                role = role,
                size = size,
                offset = offset
            ).collectList(),
            employeeRepository.countEmployeesByProjectWithFilters(
                projectId = projectId,
                fullName = fullName,
                personalNumber = personalNumber,
                organizationId = organizationId,
                role = role
            )
        ).map { tuple ->
            val employees = tuple.t1
            val total = tuple.t2
            val result = mapOf(
                "content" to employees,
                "totalElements" to total,
                "totalPages" to ((total + size - 1) / size).toInt(),
                "currentPage" to page,
                "size" to size
            )
            println("DEBUG: EmployeeService возвращает результат: $result")
            result
        }
    }

    /**
     * Получить сотрудника по ID
     */
    fun getEmployeeById(id: UUID): Mono<Employee> {
        return employeeRepository.findEmployeeById(id)
            .switchIfEmpty(Mono.error(RuntimeException("Сотрудник не найден")))
            .doOnError {
                logger.error("Error getEmployeeById", it)
            }
    }

    /**
     * Создать нового сотрудника
     */
    fun createEmployee(
        projectId: UUID,
        name: String,
        surname: String,
        middleName: String?,
        role: String,
        personalNumber: String?,
        pinCode: String,
        organizationId: UUID,
        login: String?,
        email: String?,
        phone: String?,
        jwtToken: String,
        currentUserId: UUID
    ): Mono<Employee> {
        val now = Timestamp.valueOf(LocalDateTime.now())
        val hashPin = calculateHashByPin(pinCode)

        val employeeRole = keycloakIntegrationService.getRealmRoleByEmployeeRole(role)
        return keycloakIntegrationService.registerUserInKeycloak(
            username = login ?: "${surname.lowercase()}.${name.lowercase()}",
            password = pinCode,
            firstName = name,
            lastName = surname,
            email = email,
            role = employeeRole,
            attributes = mapOf(
                "personalNumber" to (personalNumber ?: ""),
                "middleName" to (middleName ?: "")
            )
        ).flatMap { userId ->
            // Создаем запись в TMS
            val tmsRequest = TerminalUserCreateRequest(
                profileId = userId,
                projectId = projectId,
                organizationId = organizationId,
                name = name,
                surname = surname,
                middleName = middleName,
                role = employeeRole,
                personalNumber = personalNumber,
                pinCode = hashPin,
                login = login,
                email = email,
                phone = phone
            )

            tmsIntegrationService.createTerminalUser(tmsRequest, jwtToken)
                .then(
                    // Создаем запись в PRO
                    Mono.fromCallable {
                        Employee(
                            profileId = userId,
                            version = 1,
                            versionCreatedAt = now,
                            versionCreatedBy = currentUserId,
                            projectId = projectId,
                            organizationId = organizationId,
                            name = name,
                            surname = surname,
                            middleName = middleName,
                            personalNumber = personalNumber,
                            role = employeeRole,
                            enabled = true,
                            isDeleted = false,
                            pinHash = hashPin
                        )
                    }.flatMap { employee ->
                        employeeRepository.save(employee)
                    }
                )
        }
            .doOnError {
                logger.error("Error createEmployee", it)
            }
    }

    /**
     * Обновить сотрудника
     */
    fun updateEmployee(
        employeeId: UUID,
        projectId: UUID,
        name: String,
        surname: String,
        middleName: String?,
        role: String,
        personalNumber: String?,
        pinCode: String?,
        organizationId: UUID,
        login: String?,
        email: String?,
        phone: String?,
        enabled: Boolean,
        jwtToken: String,
        currentUserId: UUID
    ): Mono<Employee> {
        val hashPin: String? = pinCode?.let { calculateHashByPin(it) }

        return employeeRepository.findEmployeeById(employeeId)
            .flatMap { existingEmployee ->
                val now = Timestamp.valueOf(LocalDateTime.now())

                // Обновляем в Keycloak
                keycloakIntegrationService.updateUserInKeycloak(
                    userId = employeeId,
                    firstName = name,
                    lastName = surname,
                    email = email,
                    enabled = enabled,
                    attributes = mapOf(
                        "personalNumber" to (personalNumber ?: ""),
                        "middleName" to (middleName ?: "")
                    )
                ).then(
                    // Обновляем в TMS
                    Mono.defer {
                        val tmsRequest = TerminalUserUpdateRequest(
                            projectId = projectId,
                            organizationId = organizationId,
                            name = name,
                            surname = surname,
                            middleName = middleName,
                            role = role,
                            personalNumber = personalNumber,
                            pinCode = hashPin,
                            login = login,
                            email = email,
                            phone = phone,
                            enabled = enabled
                        )

                        tmsIntegrationService.updateTerminalUser(employeeId, tmsRequest, jwtToken)
                    }
                ).then(
                    // Обновляем в PRO
                    Mono.fromCallable {
                        existingEmployee.copy(
                            version = existingEmployee.version + 1,
                            versionCreatedAt = now,
                            versionCreatedBy = currentUserId,
                            projectId = projectId,
                            organizationId = organizationId,
                            name = name,
                            surname = surname,
                            middleName = middleName,
                            personalNumber = personalNumber,
                            role = role,
                            enabled = enabled,
                            pinHash = hashPin ?: existingEmployee.pinHash
                        )
                    }.flatMap { updatedEmployee ->
                        employeeRepository.save(updatedEmployee)
                    }
                )
            }
            .switchIfEmpty(Mono.error(RuntimeException("Сотрудник не найден")))
            .doOnError {
                logger.error("Error updateEmployee", it)
            }
    }

    /**
     * Удалить сотрудника
     */
    fun deleteEmployee(employeeId: UUID, jwtToken: String, currentUserId: UUID): Mono<Void> {
        return employeeRepository.findEmployeeById(employeeId)
            .flatMap { existingEmployee ->
                val now = Timestamp.valueOf(LocalDateTime.now())

                // Деактивируем в Keycloak
                keycloakIntegrationService.deactivateUserInKeycloak(employeeId)
                    .then(
                        // Удаляем в TMS
                        tmsIntegrationService.deleteTerminalUser(employeeId, jwtToken)
                    )
                    .then(
                        // Помечаем как удаленного в PRO
                        Mono.fromCallable {
                            existingEmployee.copy(
                                version = existingEmployee.version + 1,
                                versionCreatedAt = now,
                                versionCreatedBy = currentUserId,
                                isDeleted = true
                            )
                        }.flatMap { deletedEmployee ->
                            employeeRepository.save(deletedEmployee)
                        }
                    )
            }
            .then()
            .switchIfEmpty(Mono.error(RuntimeException("Сотрудник не найден")))
            .doOnError {
                logger.error("Error deleteEmployee", it)
            }
    }
    
    /**
     * Получить доступные роли из Keycloak
     */
    fun getAvailableKeycloakRoles(): Mono<List<ru.sbertroika.tkp3.pro.controller.service.KeycloakRole>> {
        return keycloakIntegrationService.getAvailableRoles()
            .doOnError {
                logger.error("Error getAvailableKeycloakRoles", it)
            }
    }
} 