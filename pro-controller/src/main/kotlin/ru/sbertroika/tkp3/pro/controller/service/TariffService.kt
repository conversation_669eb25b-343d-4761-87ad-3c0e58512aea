package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.*
import ru.sbertroika.tkp3.pro.controller.output.*
import ru.sbertroika.tkp3.pro.controller.output.model.ProductDictRowView
import ru.sbertroika.tkp3.pro.model.*
import java.sql.Timestamp
import java.util.*

@Service
class TariffService(
    private val tariffRepository: TariffRepository,
    private val productDictRowRepository: ProductDictRowRepository,
    private val tariffMatrixRepository: TariffMatrixRepository,
    private val routeRepository: RouteRepository,
    private val stationRepository: StationRepository,
    private val tariffConstraintRepository: TariffConstraintRepository,
    private val tariffConstraintExceptionRepository: TariffConstraintExceptionRepository
) {

    /**
     * Получить все тарифы с пагинацией
     */
    fun getAllTariffs(page: Int, size: Int): Flux<TariffDto> {
        val offset = page * size
        return tariffRepository.findAllLatestVersions(size, offset)
            .map { it.toDto() }
    }

    /**
     * Получить количество тарифов
     */
    fun getTariffsCount(): Mono<Long> {
        return tariffRepository.countAllLatestVersions()
    }

    /**
     * Получить тарифы по проекту с пагинацией
     */
    fun getTariffsByProject(projectId: UUID, page: Int, size: Int): Flux<TariffDto> {
        val offset = page * size
        return tariffRepository.findByProjectIdLatestVersions(projectId, size, offset)
            .map { it.toDto() }
    }

    /**
     * Получить количество тарифов по проекту
     */
    fun getTariffsCountByProject(projectId: UUID): Mono<Long> {
        return tariffRepository.countByProjectIdLatestVersions(projectId)
    }

    /**
     * Получить тариф по ID
     */
    fun getTariffById(id: UUID): Mono<TariffDto> {
        return tariffRepository.findByIdLatestVersion(id)
            .map { it.toDto() }
    }

    /**
     * Создать новый тариф
     */
    fun createTariff(request: TariffCreateRequest, createdBy: UUID): Mono<TariffDto> {
        val tariff = Tariff(
            id = UUID.randomUUID(),
            version = 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = createdBy,
            projectId = request.projectId,
            name = request.name,
            status = request.status,
            tags = request.tags
        )
        
        return tariffRepository.save(tariff)
            .map { it.toDto() }
    }

    /**
     * Обновить тариф (создание новой версии)
     */
    fun updateTariff(id: UUID, request: TariffUpdateRequest, updatedBy: UUID): Mono<TariffDto> {
        return tariffRepository.findByIdLatestVersion(id)
            .flatMap { existingTariff ->
                tariffRepository.findMaxVersionById(id)
                    .map { maxVersion -> maxVersion ?: 0 }
                    .flatMap { maxVersion ->
                        val updatedTariff = existingTariff.copy(
                            version = maxVersion + 1,
                            versionCreatedAt = Timestamp(System.currentTimeMillis()),
                            versionCreatedBy = updatedBy,
                            name = request.name ?: existingTariff.name,
                            status = request.status ?: existingTariff.status,
                            tags = request.tags ?: existingTariff.tags
                        )
                        tariffRepository.save(updatedTariff)
                    }
            }
            .map { it.toDto() }
    }

    /**
     * Удалить тариф (логическое удаление)
     */
    fun deleteTariff(id: UUID, deletedBy: UUID): Mono<TariffDto> {
        return tariffRepository.findByIdLatestVersion(id)
            .flatMap { existingTariff ->
                tariffRepository.findMaxVersionById(id)
                    .map { maxVersion -> maxVersion ?: 0 }
                    .flatMap { maxVersion ->
                        val deletedTariff = existingTariff.copy(
                            version = maxVersion + 1,
                            versionCreatedAt = Timestamp(System.currentTimeMillis()),
                            versionCreatedBy = deletedBy,
                            status = TariffStatus.IS_DELETED
                        )
                        tariffRepository.save(deletedTariff)
                    }
            }
            .map { it.toDto() }
    }

    /**
     * Активировать тариф
     */
    fun activateTariff(id: UUID, updatedBy: UUID): Mono<TariffDto> {
        return updateTariff(id, TariffUpdateRequest(status = TariffStatus.ACTIVE), updatedBy)
    }

    /**
     * Деактивировать тариф
     */
    fun deactivateTariff(id: UUID, updatedBy: UUID): Mono<TariffDto> {
        return updateTariff(id, TariffUpdateRequest(status = TariffStatus.DISABLED), updatedBy)
    }

    /**
     * Заблокировать тариф
     */
    fun blockTariff(id: UUID, updatedBy: UUID): Mono<TariffDto> {
        return updateTariff(id, TariffUpdateRequest(status = TariffStatus.BLOCKED), updatedBy)
    }

    private fun Tariff.toDto(): TariffDto {
        return TariffDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            projectId = this.projectId,
            name = this.name,
            status = this.status,
            tags = this.tags
        )
    }

    // Методы для работы с ProductDictRow

    /**
     * Получить все ProductDictRow для тарифа
     */
    fun getProductDictRowsByTariff(tariffId: UUID): Flux<ProductDictRowDto> {
        return productDictRowRepository.findViewByTariffId(tariffId)
            .map { it.toDto() }
    }

    /**
     * Создать новый ProductDictRow
     */
    fun createProductDictRow(request: ProductDictRowCreateRequest, createdBy: UUID): Mono<ProductDictRowDto> {
        val productDictRow = ProductDictRow(
            id = UUID.randomUUID(),
            version = 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = createdBy,
            projectId = request.projectId,
            productId = request.productId,
            tariffId = request.tariffId,
            paymentMethodType = request.paymentMethodType,
            isFixPrice = request.isFixPrice,
            price = request.price,
            tags = request.tags
        )
        
        return productDictRowRepository.save(productDictRow)
            .flatMap { saved ->
                productDictRowRepository.findViewByIdLatestVersion(saved.id!!)
                    .map { it.toDto() }
            }
    }

    /**
     * Обновить ProductDictRow
     */
    fun updateProductDictRow(id: UUID, request: ProductDictRowUpdateRequest, updatedBy: UUID): Mono<ProductDictRowDto> {
        return productDictRowRepository.findByIdLatestVersion(id)
            .doOnError { error ->
                println("Error finding existing: ${error.message}")
            }
            .flatMap { existing ->
                productDictRowRepository.findMaxVersionById(id)
                    .doOnError { error ->
                        println("Error finding max version: ${error.message}")
                    }
                    .flatMap { maxVersion ->
                        val nextVersion = (maxVersion ?: 0) + 1
                        
                        val updated = existing.copy(
                            version = nextVersion,
                            versionCreatedAt = Timestamp(System.currentTimeMillis()),
                            versionCreatedBy = updatedBy,
                            paymentMethodType = request.paymentMethodType ?: existing.paymentMethodType,
                            isFixPrice = request.isFixPrice ?: existing.isFixPrice,
                            price = request.price ?: existing.price,
                            tags = request.tags ?: existing.tags
                        )
                        println("Updated object: $updated")
                        
                        productDictRowRepository.save(updated)
                            .doOnError { error ->
                                println("Error saving: ${error.message}")
                            }
                            .flatMap { saved ->
                                productDictRowRepository.findViewByIdLatestVersion(saved.id!!)
                                    .map { it.toDto() }
                            }
                    }
            }
            .doOnError { error ->
                println("Error in updateProductDictRow: ${error.message}")
                println("Stack trace: ${error.stackTraceToString()}")
            }
    }

    /**
     * Получить ProductDictRow по ID
     */
    fun getProductDictRowById(id: UUID): Mono<ProductDictRowDto> {
        return productDictRowRepository.findByIdLatestVersion(id)
            .map { it.toDto() }
    }

    /**
     * Удалить ProductDictRow
     */
    fun deleteProductDictRow(id: UUID): Mono<Void> {
        return productDictRowRepository.delete(id)
    }

    // Методы для работы с тарифными матрицами

    /**
     * Получить тарифные матрицы по ProductDictRow ID
     */
    fun getTariffMatricesByProductDictRow(productDictRowId: UUID): Flux<TariffMatrixDto> {
        return tariffMatrixRepository.findByProductDictRowId(productDictRowId)
            .map { it.toDto() }
    }

    /**
     * Создать новую тарифную матрицу
     */
    fun createTariffMatrix(request: TariffMatrixCreateRequest, createdBy: UUID): Mono<TariffMatrixDto> {
        val tariffMatrix = ProductDictRowTariffMatrixPrice(
            id = UUID.randomUUID(),
            version = 1,
            versionCreatedAt = Timestamp(System.currentTimeMillis()),
            versionCreatedBy = createdBy,
            productDicRowId = request.productDictRowId,
            stationFromId = request.stationFromId,
            stationToId = request.stationToId,
            amount = request.amount,
            tags = null
        )
        
        return tariffMatrixRepository.save(tariffMatrix)
            .map { it.toDto() }
    }

    /**
     * Обновить тарифную матрицу
     */
    fun updateTariffMatrix(id: UUID, request: TariffMatrixUpdateRequest, updatedBy: UUID): Mono<TariffMatrixDto> {
        return tariffMatrixRepository.findByIdLatestVersion(id)
            .flatMap { existing ->
                tariffMatrixRepository.findMaxVersionById(id)
                    .map { maxVersion -> maxVersion ?: 0 }
                    .flatMap { maxVersion ->
                        val updated = existing.copy(
                            version = maxVersion + 1,
                            versionCreatedAt = Timestamp(System.currentTimeMillis()),
                            versionCreatedBy = updatedBy,
                            amount = request.amount
                        )
                        tariffMatrixRepository.save(updated)
                    }
            }
            .map { it.toDto() }
    }

    /**
     * Получить тарифную матрицу по ID
     */
    fun getTariffMatrixById(id: UUID): Mono<TariffMatrixDto> {
        return tariffMatrixRepository.findByIdLatestVersion(id)
            .map { it.toDto() }
    }

    /**
     * Удалить тарифную матрицу
     */
    fun deleteTariffMatrix(id: UUID): Mono<Void> {
        return tariffMatrixRepository.delete(id)
    }

    /**
     * Массовое обновление тарифной матрицы для продукта
     */
    fun updateTariffMatrixForProduct(productDictRowId: UUID, request: TariffMatrixBulkUpdateRequest, updatedBy: UUID): Flux<TariffMatrixDto> {
        return Flux.fromIterable(request.matrixData.entries)
            .flatMap { entry ->
                val (stationPair, amount) = entry
                val stations = stationPair.split("-")
                if (stations.size == 2) {
                    val stationFromName = stations[0]
                    val stationToName = stations[1]
                    
                    // Находим ID станций по названиям через API станций
                    // TODO: Добавить получение ID станций по названиям через StationRepository
                    // Пока используем UUID.randomUUID() как заглушку
                    val stationFromId = UUID.randomUUID()
                    val stationToId = UUID.randomUUID()
                    
                    // Проверяем, существует ли уже запись для этой пары станций
                    tariffMatrixRepository.findByProductDictRowAndStations(productDictRowId, stationFromId, stationToId)
                        .next()
                        .flatMap { existing ->
                            // Обновляем существующую запись
                            tariffMatrixRepository.findMaxVersionById(existing.id!!)
                                .map { maxVersion -> maxVersion ?: 0 }
                                .flatMap { maxVersion ->
                                    val updated = existing.copy(
                                        version = maxVersion + 1,
                                        versionCreatedAt = Timestamp(System.currentTimeMillis()),
                                        versionCreatedBy = updatedBy,
                                        amount = amount
                                    )
                                    tariffMatrixRepository.save(updated)
                                }
                        }
                        .switchIfEmpty(
                            // Создаем новую запись
                            Mono.defer {
                                val newMatrix = ProductDictRowTariffMatrixPrice(
                                    id = UUID.randomUUID(),
                                    version = 1,
                                    versionCreatedAt = Timestamp(System.currentTimeMillis()),
                                    versionCreatedBy = updatedBy,
                                    productDicRowId = productDictRowId,
                                    stationFromId = stationFromId,
                                    stationToId = stationToId,
                                    amount = amount,
                                    tags = stationPair // Сохраняем пару станций в tags для отладки
                                )
                                tariffMatrixRepository.save(newMatrix)
                            }
                        )
                } else {
                    Mono.empty<ProductDictRowTariffMatrixPrice>()
                }
            }
            .map { it.toDto() }
    }

    // Методы для получения комплексной информации о тарифе

    /**
     * Получить полную информацию о тарифе
     */
    fun getTariffFullInfo(tariffId: UUID): Mono<TariffFullInfoDto> {
        return Mono.zip(
            tariffRepository.findByIdLatestVersion(tariffId),
            getProductDictRowsByTariff(tariffId).collectList(),
            getTariffMatricesByTariff(tariffId).collectList(),
            getTariffConstraintsByTariff(tariffId).collectList(),
            getTariffConstraintExceptionsByTariff(tariffId).collectList()
        ).map { result ->
            val tariff = result.t1
            val productDictRows = result.t2
            val tariffMatrices = result.t3
            val constraints = result.t4
            val constraintExceptions = result.t5
            TariffFullInfoDto(
                tariff = tariff.toDto(),
                productDictRows = productDictRows,
                tariffMatrices = tariffMatrices,
                constraints = constraints,
                constraintExceptions = constraintExceptions
            )
        }
    }

    /**
     * Получить тарифные матрицы по тарифу
     */
    private fun getTariffMatricesByTariff(tariffId: UUID): Flux<TariffMatrixDto> {
        return productDictRowRepository.findViewByTariffId(tariffId)
            .flatMap { productDictRow ->
                tariffMatrixRepository.findByProductDictRowId(productDictRow.id!!)
            }
            .map { it.toDto() }
    }

    /**
     * Получить ограничения тарифа
     */
    private fun getTariffConstraintsByTariff(tariffId: UUID): Flux<TariffConstraintDto> {
        return tariffConstraintRepository.findByTariffId(tariffId)
            .map { it.toDto() }
    }

    /**
     * Получить исключения ограничений тарифа
     */
    private fun getTariffConstraintExceptionsByTariff(tariffId: UUID): Flux<TariffConstraintExceptionDto> {
        return tariffConstraintRepository.findByTariffId(tariffId)
            .flatMap { constraint ->
                tariffConstraintExceptionRepository.findByTariffConstraintId(constraint.id!!)
            }
            .map { it.toDto() }
    }

    // Методы для работы с маршрутами и тарифами

    /**
     * Получить тарифы по маршрутам
     */
    fun getRouteTariffs(projectId: UUID? = null): Flux<RouteTariffsResponse> {
        return if (projectId != null) {
            routeRepository.findByProjectId(projectId)
        } else {
            routeRepository.findAllRoutesWithFilters(null, null, null, 1000, 0)
        }.flatMap { route ->
            // Находим исключения, где exceptionId = ID маршрута
            tariffConstraintExceptionRepository.findByExceptionId(route.id!!)
                .flatMap { exception ->
                    // Находим ограничение для этого исключения
                    tariffConstraintRepository.findByIdLatestVersion(exception.tariffConstraintId!!)
                        .filter { it.type == TariffConstraintType.ROUTE && it.baseRule?.name == "ALLOW" }
                        .flatMap { constraint ->
                            // Получаем информацию о тарифе
                            tariffRepository.findByIdLatestVersion(constraint.tariffId!!)
                        }
                }
                .collectList()
                .flatMap { tariffs ->
                    // Для каждого тарифа загружаем способы оплаты
                    Flux.fromIterable(tariffs)
                        .flatMap { tariff ->
                            getProductDictRowsByTariff(tariff.id!!)
                                .collectList()
                                .map { productDictRows ->
                                    // Группируем продукты по способам оплаты
                                    val paymentMethods = productDictRows
                                        .groupBy { it.paymentMethodType }
                                        .map { (paymentMethodType, products) ->
                                            TariffPaymentMethodDto(
                                                id = paymentMethodType?.name ?: "UNKNOWN",
                                                type = paymentMethodType?.name ?: "UNKNOWN",
                                                name = getPaymentMethodDisplayName(paymentMethodType),
                                                products = emptyList() // Пока оставляем пустым для стабильности
                                            )
                                        }
                                    
                                    TariffWithPaymentMethodsDto(
                                        id = tariff.id,
                                        name = tariff.name,
                                        description = null,
                                        status = tariff.status?.name,
                                        tags = tariff.tags,
                                        paymentMethods = paymentMethods
                                    )
                                }
                        }
                        .collectList()
                        .map { tariffList ->
                            val tariffDtos = tariffList.associateBy(
                                keySelector = { it.id?.toString() ?: UUID.randomUUID().toString() },
                                valueTransform = { it }
                            )
                            RouteTariffsResponse(
                                routeId = route.id!!,
                                routeName = route.name,
                                routeDescription = route.number,
                                tariffs = tariffDtos
                            )
                        }
                }
                .defaultIfEmpty(RouteTariffsResponse(
                    routeId = route.id!!,
                    routeName = route.name,
                    routeDescription = route.number,
                    tariffs = emptyMap()
                ))
        }
    }

    /**
     * Назначить тариф на маршрут
     */
    fun assignTariffToRoute(request: RouteTariffAssignmentCreateRequest, createdBy: UUID): Mono<RouteTariffAssignmentDto> {
        return Mono.defer {
            // Проверяем, есть ли уже ограничение типа ROUTE для этого тарифа
            tariffConstraintRepository.findByTariffIdAndType(request.tariffId, TariffConstraintType.ROUTE)
                .filter { it.baseRule?.name == "ALLOW" }
                .next()
                .switchIfEmpty(
                    // Создаем новое ограничение, если его нет
                    Mono.defer {
                        val constraint = TariffConstraint(
                            id = UUID.randomUUID(),
                            version = 1,
                            versionCreatedAt = Timestamp(System.currentTimeMillis()),
                            versionCreatedBy = createdBy,
                            tariffId = request.tariffId,
                            type = TariffConstraintType.ROUTE,
                            baseRule = BaseRule.ALLOW,
                            tags = null
                        )
                        tariffConstraintRepository.save(constraint)
                    }
                )
                .flatMap { constraint ->
                    // Создаем исключение для маршрута
                    val exception = TariffConstraintException(
                        id = UUID.randomUUID(),
                        version = 1,
                        versionCreatedAt = Timestamp(System.currentTimeMillis()),
                        versionCreatedBy = createdBy,
                        tariffConstraintId = constraint.id!!,
                        exceptionId = request.routeId,
                        tags = null
                    )
                    tariffConstraintExceptionRepository.save(exception)
                        .map { savedException ->
                            RouteTariffAssignmentDto(
                                routeId = request.routeId,
                                tariffId = request.tariffId,
                                assignedAt = savedException.versionCreatedAt
                            )
                        }
                }
        }
    }

    /**
     * Удалить тариф с маршрута
     */
    fun removeTariffFromRoute(routeId: UUID, tariffId: UUID): Mono<Boolean> {
        return tariffConstraintRepository.findByTariffIdAndType(tariffId, TariffConstraintType.ROUTE)
            .filter { it.baseRule?.name == "ALLOW" }
            .next()
            .doOnNext { constraint ->
                println("DEBUG: Найдено ограничение тарифа: ${constraint.id}")
            }
            .flatMap { constraint ->
                tariffConstraintExceptionRepository.findByTariffConstraintId(constraint.id!!)
                    .filter { it.exceptionId == routeId }
                    .next()
                    .doOnNext { exception ->
                        println("DEBUG: Найдено исключение для маршрута: ${exception.id}")
                    }
                    .flatMap { exception ->
                        // Используем логическое удаление через версионирование
                        println("DEBUG: Выполняем удаление исключения: ${exception.id}")
                        tariffConstraintExceptionRepository.delete(exception.id!!)
                            .then(Mono.just(true))
                    }
                    .switchIfEmpty(Mono.error(RuntimeException("Тариф не назначен на указанный маршрут")))
            }
            .switchIfEmpty(Mono.error(RuntimeException("Не найдено ограничение типа ROUTE для тарифа")))
            .doOnSuccess { success ->
                println("DEBUG: Удаление тарифа с маршрута завершено успешно: $success")
            }
            .doOnError { error ->
                println("DEBUG: Ошибка при удалении тарифа с маршрута: ${error.message}")
            }
    }

    // Вспомогательные методы для преобразования в DTO

    /**
     * Получить отображаемое название способа оплаты
     */
    private fun getPaymentMethodDisplayName(paymentMethodType: PayMethodType?): String {
        return when (paymentMethodType) {
            PayMethodType.CASH -> "Наличные"
            PayMethodType.EMV -> "Банковская карта"
            PayMethodType.TROIKA_TICKET -> "Тройка (билет)"
            else -> "Неизвестный способ"
        }
    }

    private fun ProductDictRow.toDto(): ProductDictRowDto {
        return ProductDictRowDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            projectId = this.projectId,
            productId = this.productId,
            tariffId = this.tariffId,
            paymentMethodType = this.paymentMethodType,
            isFixPrice = this.isFixPrice,
            price = this.price,
            tags = this.tags,
            productName = null, // ProductDictRow не содержит эти поля
            tariffName = null
        )
    }

    private fun ProductDictRowView.toDto(): ProductDictRowDto {
        return ProductDictRowDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            projectId = this.projectId,
            productId = this.productId,
            tariffId = this.tariffId,
            paymentMethodType = this.paymentMethodType,
            isFixPrice = this.isFixPrice,
            price = this.price,
            tags = this.tags,
            productName = this.productName,
            tariffName = this.tariffName
        )
    }

    private fun ProductDictRowTariffMatrixPrice.toDto(): TariffMatrixDto {
        return TariffMatrixDto(
            id = this.id,
            productDictRowId = this.productDicRowId,
            stationFromId = this.stationFromId,
            stationToId = this.stationToId,
            amount = this.amount,
            stationFromName = null, // TODO: добавить получение названия станции
            stationToName = null, // TODO: добавить получение названия станции
            tags = this.tags
        )
    }

    private fun TariffConstraint.toDto(): TariffConstraintDto {
        return TariffConstraintDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            tariffId = this.tariffId,
            type = this.type?.name,
            baseRule = this.baseRule?.name,
            tags = this.tags
        )
    }

    private fun TariffConstraintException.toDto(): TariffConstraintExceptionDto {
        return TariffConstraintExceptionDto(
            id = this.id,
            version = this.version,
            versionCreatedAt = this.versionCreatedAt,
            versionCreatedBy = this.versionCreatedBy,
            tariffConstraintId = this.tariffConstraintId,
            exceptionId = this.exceptionId,
            tags = this.tags
        )
    }
} 