package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.ProjectDto
import ru.sbertroika.tkp3.pro.controller.model.ProjectStatsDto
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.ProjectService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import java.util.*

@RestController
@RequestMapping("/api/pro/v1")
class ProjectController(
    private val projectService: ProjectService
) : BaseController() {

    @GetMapping("/projects")
    fun getProjects(
        @RequestParam(required = false) status: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список проектов")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем только этот проект
                    logUserAction("использует projectId для фильтрации", tokenProjectId.get().toString())
                    projectService.getProjectById(tokenProjectId.get())
                        .flatMap { project ->
                            val response = mapOf(
                                "content" to listOf(project),
                                "pagination" to mapOf(
                                    "page" to 0,
                                    "size" to 1,
                                    "totalElements" to 1L,
                                    "totalPages" to 1,
                                    "hasNext" to false,
                                    "hasPrevious" to false
                                )
                            )
                            Mono.just(ApiResponse.success(response))
                        }
                } else {
                    // Если в токене нет projectId, показываем все проекты
                    Mono.zip(
                        projectService.getAllProjects(status, page, size).collectList(),
                        projectService.getProjectsCount(status)
                    ).map { result ->
                        val projects = result.t1
                        val totalCount = result.t2
                        val totalPages = (totalCount + size - 1) / size
                        val response = mapOf(
                            "content" to projects,
                            "pagination" to mapOf(
                                "page" to page,
                                "size" to size,
                                "totalElements" to totalCount,
                                "totalPages" to totalPages,
                                "hasNext" to (page < totalPages - 1),
                                "hasPrevious" to (page > 0)
                            )
                        )
                        ApiResponse.success(response)
                    }
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении проектов: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения проектов"))
            }
    }

    @GetMapping("/projects/{id}")
    fun getProjectById(@PathVariable id: String): Mono<ApiResponse<ProjectDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка доступа к проекту из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.getProjectById(projectId)
                }
            }.map { project ->
                ApiResponse.success(project)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка получения проекта"))
            }
    }

    @PostMapping("/projects")
    fun createProject(@RequestBody project: ProjectDto): Mono<ApiResponse<ProjectDto>> {
        logUserAction("создает новый проект", project.name)
        
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                
                if (tokenProjectId.isPresent) {
                    logError("Попытка создания проекта при ограниченном доступе", 
                        RuntimeException("Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: нельзя создавать проекты при ограниченном доступе"))
                } else {
                    projectService.createProject(project, currentUserId)
                }
            }.map { createdProject ->
                logUserAction("успешно создал проект", createdProject.name)
                ApiResponse.success(createdProject)
            }
            .onErrorResume { error ->
                logError("Ошибка при создании проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка создания проекта: ${error.message}"))
            }
    }

    @PutMapping("/projects/{id}")
    fun updateProject(@PathVariable id: String, @RequestBody project: ProjectDto): Mono<ApiResponse<ProjectDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка обновления проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.updateProject(projectId, project, currentUserId)
                }
            }.map { updatedProject ->
                ApiResponse.success(updatedProject)
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка обновления проекта: ${error.message}"))
            }
    }

    @DeleteMapping("/projects/{id}")
    fun deleteProject(@PathVariable id: String): Mono<ApiResponse<String>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка удаления проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<String>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.deleteProject(projectId)
                }
            }.map {
                ApiResponse.success("Проект успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/sync")
    fun syncProjectWithContract(@PathVariable id: String): Mono<ApiResponse<ProjectDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка синхронизации проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.syncProjectWithContract(projectId)
                }
            }.map { project ->
                ApiResponse.success(project, "Проект синхронизирован с договором")
            }
            .onErrorResume { error ->
                logError("Ошибка при синхронизации проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка синхронизации проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/activate")
    fun activateProject(@PathVariable id: String): Mono<ApiResponse<ProjectDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка активации проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.activateProject(projectId)
                }
            }.map { project ->
                ApiResponse.success(project, "Проект активирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при активации проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка активации проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/complete")
    fun completeProject(@PathVariable id: String): Mono<ApiResponse<ProjectDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка завершения проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.completeProject(projectId)
                }
            }.map { project ->
                ApiResponse.success(project, "Проект завершен")
            }
            .onErrorResume { error ->
                logError("Ошибка при завершении проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectDto>("Ошибка завершения проекта: ${error.message}"))
            }
    }

    @GetMapping("/projects/status/{status}")
    fun getProjectsByStatus(@PathVariable status: String): Mono<ApiResponse<List<ProjectDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем только этот проект с указанным статусом
                    projectService.getProjectById(tokenProjectId.get())
                        .filter { it.status?.name == status }
                        .map { listOf(it) }
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                } else {
                    // Если в токене нет projectId, показываем все проекты с указанным статусом
                    projectService.getProjectsByStatus(status)
                        .collectList()
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении проектов по статусу: ${error.message}", error)
                Mono.just(ApiResponse.error<List<ProjectDto>>("Ошибка получения проектов по статусу"))
            }
    }

    @GetMapping("/projects/region/{region}")
    fun getProjectsByRegion(@PathVariable region: String): Mono<ApiResponse<List<ProjectDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем только этот проект
                    projectService.getProjectById(tokenProjectId.get())
                        .map { listOf(it) }
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                } else {
                    // Если в токене нет projectId, показываем все проекты по региону
                    projectService.getProjectsByRegion(region)
                        .collectList()
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении проектов по региону: ${error.message}", error)
                Mono.just(ApiResponse.error<List<ProjectDto>>("Ошибка получения проектов по региону"))
            }
    }

    @GetMapping("/projects/type/{projectType}")
    fun getProjectsByType(@PathVariable projectType: String): Mono<ApiResponse<List<ProjectDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем только этот проект
                    projectService.getProjectById(tokenProjectId.get())
                        .map { listOf(it) }
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                } else {
                    // Если в токене нет projectId, показываем все проекты по типу
                    projectService.getProjectsByType(projectType)
                        .collectList()
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении проектов по типу: ${error.message}", error)
                Mono.just(ApiResponse.error<List<ProjectDto>>("Ошибка получения проектов по типу"))
            }
    }

    @GetMapping("/projects/contract/{contractId}")
    fun getProjectsByContract(@PathVariable contractId: String): Mono<ApiResponse<List<ProjectDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем только этот проект
                    projectService.getProjectById(tokenProjectId.get())
                        .map { listOf(it) }
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                } else {
                    // Если в токене нет projectId, показываем все проекты по договору
                    projectService.getProjectsByContract(UUID.fromString(contractId))
                        .collectList()
                        .map { projects ->
                            ApiResponse.success(projects)
                        }
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении проектов по договору: ${error.message}", error)
                Mono.just(ApiResponse.error<List<ProjectDto>>("Ошибка получения проектов по договору"))
            }
    }

    @GetMapping("/projects/{id}/organizations")
    fun getProjectOrganizations(@PathVariable id: String): Mono<ApiResponse<List<Map<String, Any>>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка получения организаций проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<List<Map<String, Any>>>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.getProjectOrganizations(projectId)
                }
            }.map { organizations ->
                ApiResponse.success(organizations)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении организаций проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<List<Map<String, Any>>>("Ошибка получения организаций проекта: ${error.message}"))
            }
    }

    @GetMapping("/projects/{id}/stats")
    fun getProjectStats(@PathVariable id: String): Mono<ApiResponse<ProjectStatsDto>> {
        logUserAction("запрашивает статистику проекта")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка получения статистики проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectStatsDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.getProjectStats(projectId)
                }
            }.map { stats ->
                ApiResponse.success(stats)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении статистики проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectStatsDto>("Ошибка получения статистики проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/manifest/update")
    fun updateManifest(@PathVariable id: String): Mono<ApiResponse<ProjectStatsDto>> {
        logUserAction("обновляет справочники проекта")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка обновления справочников проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ProjectStatsDto>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    projectService.updateManifest(projectId)
                }
            }.map { stats ->
                logUserAction("успешно обновил справочники проекта")
                ApiResponse.success(stats, "Справочники проекта успешно обновлены")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении справочников проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<ProjectStatsDto>("Ошибка обновления справочников проекта: ${error.message}"))
            }
    }
    
    @GetMapping("/projects/{id}/manifest/status")
    fun getManifestUpdateStatus(@PathVariable id: String): Mono<ApiResponse<Map<String, Any>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val projectId = UUID.fromString(id)
                
                if (tokenProjectId.isPresent && tokenProjectId.get() != projectId) {
                    logError("Попытка получения статуса справочников проекта из другого проекта", 
                        RuntimeException("Request projectId: $projectId, Token projectId: ${tokenProjectId.get()}"))
                    Mono.error<ApiResponse<Map<String, Any>>>(RuntimeException("Доступ запрещен: проект принадлежит другому проекту"))
                } else {
                    val isActive = projectService.manifestEventService.isUpdateActive(projectId)
                    val waitingCount = projectService.manifestEventService.getWaitingChannelsCount(projectId)
                    
                    val status = mapOf<String, Any>(
                        "isActive" to isActive,
                        "waitingCount" to waitingCount,
                        "projectId" to projectId.toString()
                    )
                    
                    Mono.just(ApiResponse.success(status))
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении статуса справочников проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения статуса справочников проекта: ${error.message}"))
            }
    }
    
    @GetMapping("/manifest/updates/status")
    fun getManifestUpdatesStatus(): Mono<ApiResponse<Map<String, Any>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если в токене есть projectId, показываем статус только для этого проекта
                    val projectId = tokenProjectId.get()
                    val isActive = projectService.manifestEventService.isUpdateActive(projectId)
                    val waitingCount = projectService.manifestEventService.getWaitingChannelsCount(projectId)
                    
                    val status = mapOf<String, Any>(
                        "activeUpdatesCount" to (if (isActive) 1 else 0),
                        "projectsWithWaitingChannelsCount" to (if (waitingCount > 0) 1 else 0)
                    )
                    
                    Mono.just(ApiResponse.success(status))
                } else {
                    // Если в токене нет projectId, показываем общий статус
                    val activeUpdatesCount = projectService.manifestEventService.getActiveUpdatesCount()
                    val projectsWithWaitingChannelsCount = projectService.manifestEventService.getProjectsWithWaitingChannelsCount()
                    
                    val status = mapOf<String, Any>(
                        "activeUpdatesCount" to activeUpdatesCount,
                        "projectsWithWaitingChannelsCount" to projectsWithWaitingChannelsCount
                    )
                    
                    Mono.just(ApiResponse.success(status))
                }
            }.onErrorResume { error ->
                logError("Ошибка при получении статуса обновлений справочников: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения статуса обновлений справочников: ${error.message}"))
            }
    }
} 