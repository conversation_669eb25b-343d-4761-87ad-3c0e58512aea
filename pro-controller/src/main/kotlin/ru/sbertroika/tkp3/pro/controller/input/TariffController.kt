package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.model.*
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.TariffService
import ru.sbertroika.tkp3.pro.controller.util.JwtUtils
import java.util.*
import kotlin.jvm.optionals.getOrDefault

@RestController
@RequestMapping("/api/pro/v1")
class TariffController(
    private val tariffService: TariffService
) : BaseController() {

    @GetMapping("/tariffs")
    fun getTariffs(
        @RequestParam(required = false) projectId: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список тарифов", projectId ?: "все проекты")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                if (tokenProjectId.isPresent) {
                    // Если есть projectId в токене, получаем тарифы только этого проекта
                    logUserAction("использует projectId из токена для фильтрации", tokenProjectId.get().toString())
                    Mono.zip(
                        tariffService.getTariffsByProject(tokenProjectId.get(), page, size).collectList(),
                        tariffService.getTariffsCountByProject(tokenProjectId.get())
                    ).map { result ->
                        val tariffs = result.t1
                        val totalCount = result.t2
                        val totalPages = (totalCount + size - 1) / size
                        val response = mapOf(
                            "content" to tariffs,
                            "pagination" to mapOf(
                                "page" to page,
                                "size" to size,
                                "totalElements" to totalCount,
                                "totalPages" to totalPages,
                                "hasNext" to (page < totalPages - 1),
                                "hasPrevious" to (page > 0)
                            )
                        )
                        ApiResponse.success(response)
                    }
                } else {
                    // Если нет projectId в токене, используем projectId из запроса или получаем все тарифы
                    val effectiveProjectId = projectId?.let { UUID.fromString(it) }
                    if (effectiveProjectId != null) {
                        logUserAction("использует projectId из запроса для фильтрации", effectiveProjectId.toString())
                        Mono.zip(
                            tariffService.getTariffsByProject(effectiveProjectId, page, size).collectList(),
                            tariffService.getTariffsCountByProject(effectiveProjectId)
                        ).map { result ->
                            val tariffs = result.t1
                            val totalCount = result.t2
                            val totalPages = (totalCount + size - 1) / size
                            val response = mapOf(
                                "content" to tariffs,
                                "pagination" to mapOf(
                                    "page" to page,
                                    "size" to size,
                                    "totalElements" to totalCount,
                                    "totalPages" to totalPages,
                                    "hasNext" to (page < totalPages - 1),
                                    "hasPrevious" to (page > 0)
                                )
                            )
                            ApiResponse.success(response)
                        }
                    } else {
                        // Получаем все тарифы
                        logUserAction("получает все тарифы без фильтрации по проекту")
                        Mono.zip(
                            tariffService.getAllTariffs(page, size).collectList(),
                            tariffService.getTariffsCount()
                        ).map { result ->
                            val tariffs = result.t1
                            val totalCount = result.t2
                            val totalPages = (totalCount + size - 1) / size
                            val response = mapOf(
                                "content" to tariffs,
                                "pagination" to mapOf(
                                    "page" to page,
                                    "size" to size,
                                    "totalElements" to totalCount,
                                    "totalPages" to totalPages,
                                    "hasNext" to (page < totalPages - 1),
                                    "hasPrevious" to (page > 0)
                                )
                            )
                            ApiResponse.success(response)
                        }
                    }
                }
            }
            .onErrorResume { error ->
                logError("Ошибка при получении тарифов: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения тарифов: ${error.message}"))
            }
    }

    @GetMapping("/tariffs/{id}")
    fun getTariffById(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { tariff ->
                        if (tokenProjectId.isPresent && tariff.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к тарифу из другого проекта", 
                                RuntimeException("Tariff projectId: ${tariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            Mono.just(tariff)
                        }
                    }
            }
            .map { tariff ->
                ApiResponse.success(tariff)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка получения тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs")
    fun createTariff(@RequestBody request: TariffCreateRequest): Mono<ApiResponse<TariffDto>> {
        logUserAction("создает новый тариф", request.projectId.toString())

        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                if (tokenProjectId.isPresent && request.projectId != tokenProjectId.get()) {
                    logError("Попытка создания тарифа в другом проекте", 
                        RuntimeException("Request projectId: ${request.projectId}, Token projectId: $tokenProjectId"))
                    Mono.error<TariffDto>(RuntimeException("Доступ запрещен: нельзя создавать тариф в другом проекте"))
                } else {
                    val effectiveProjectId = tokenProjectId.getOrDefault(request.projectId)
                    tariffService.createTariff(request.copy(projectId = effectiveProjectId), currentUserId)
                }
            }
            .map { tariff ->
                logUserAction("успешно создал тариф", tariff.name ?: "Без названия")
                ApiResponse.success(tariff, "Тариф успешно создан")
            }
            .onErrorResume { error ->
                logError("Ошибка при создании тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка создания тарифа: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/{id}")
    fun updateTariff(
        @PathVariable id: String,
        @RequestBody request: TariffUpdateRequest
    ): Mono<ApiResponse<TariffDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { existingTariff ->
                        if (tokenProjectId.isPresent && existingTariff.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${existingTariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            val projectIdForUpdate = existingTariff.projectId ?: 
                                throw RuntimeException("Тариф не привязан к проекту")
                            tariffService.updateTariff(UUID.fromString(id), request, currentUserId)
                        }
                    }
            }
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно обновлен")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка обновления тарифа: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/{id}")
    fun deleteTariff(@PathVariable id: String): Mono<ApiResponse<String>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { existingTariff ->
                        if (tokenProjectId.isPresent && existingTariff.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${existingTariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.deleteTariff(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map {
                ApiResponse.success("Тариф успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/activate")
    fun activateTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2
                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { existingTariff ->
                        if (tokenProjectId.isPresent && existingTariff.projectId != tokenProjectId.get()) {
                            logError("Попытка активации тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${existingTariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.activateTariff(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно активирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при активации тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка активации тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/deactivate")
    fun deactivateTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { existingTariff ->
                        if (tokenProjectId.isPresent && existingTariff.projectId != tokenProjectId.get()) {
                            logError("Попытка деактивации тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${existingTariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.deactivateTariff(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно деактивирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при деактивации тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка деактивации тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/{id}/block")
    fun blockTariff(@PathVariable id: String): Mono<ApiResponse<TariffDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { existingTariff ->
                        if (tokenProjectId.isPresent && existingTariff.projectId != tokenProjectId.get()) {
                            logError("Попытка блокировки тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${existingTariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.blockTariff(UUID.fromString(id), currentUserId)
                        }
                    }
            }
            .map { tariff ->
                ApiResponse.success(tariff, "Тариф успешно заблокирован")
            }
            .onErrorResume { error ->
                logError("Ошибка при блокировке тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffDto>("Ошибка блокировки тарифа: ${error.message}"))
            }
    }

    // Endpoints для работы с полной информацией о тарифе

    @GetMapping("/tariffs/{id}/full")
    fun getTariffFullInfo(@PathVariable id: String): Mono<ApiResponse<TariffFullInfoDto>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getTariffById(UUID.fromString(id))
                    .flatMap { tariff ->
                        if (tokenProjectId.isPresent && tariff.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к полной информации о тарифе из другого проекта", 
                                RuntimeException("Tariff projectId: ${tariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffFullInfoDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.getTariffFullInfo(UUID.fromString(id))
                        }
                    }
            }
            .map { tariffInfo ->
                ApiResponse.success(tariffInfo)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении полной информации о тарифе: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffFullInfoDto>("Ошибка получения полной информации о тарифе: ${error.message}"))
            }
    }

    // Endpoints для работы с ProductDictRow

    @GetMapping("/tariffs/{tariffId}/products")
    fun getTariffProducts(@PathVariable tariffId: String): Mono<ApiResponse<List<ProductDictRowDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getTariffById(UUID.fromString(tariffId))
                    .flatMap { tariff ->
                        if (tokenProjectId.isPresent && tariff.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к продуктам тарифа из другого проекта", 
                                RuntimeException("Tariff projectId: ${tariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<List<ProductDictRowDto>>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.getProductDictRowsByTariff(UUID.fromString(tariffId))
                                .collectList()
                        }
                    }
            }
            .map { products ->
                ApiResponse.success(products)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении продуктов тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<List<ProductDictRowDto>>("Ошибка получения продуктов тарифа: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/products")
    fun createTariffProduct(@RequestBody request: ProductDictRowCreateRequest): Mono<ApiResponse<ProductDictRowDto>> {
        logUserAction("создает новый продукт тарифа", request.projectId.toString())

        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                if (tokenProjectId.isPresent && request.projectId != tokenProjectId.get()) {
                    logError("Попытка создания продукта тарифа в другом проекте", 
                        RuntimeException("Request projectId: ${request.projectId}, Token projectId: $tokenProjectId"))
                    Mono.error<ProductDictRowDto>(RuntimeException("Доступ запрещен: нельзя создавать продукт тарифа в другом проекте"))
                } else {
                    val effectiveProjectId = tokenProjectId.getOrDefault(request.projectId)
                    tariffService.createProductDictRow(request.copy(projectId = effectiveProjectId), currentUserId)
                }
            }
            .map { product ->
                logUserAction("успешно создал продукт тарифа", product.productName ?: "Без названия")
                ApiResponse.success(product, "Продукт тарифа успешно создан")
            }
            .onErrorResume { error ->
                logError("Ошибка при создании продукта тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<ProductDictRowDto>("Ошибка создания продукта тарифа: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/products/{id}")
    fun updateTariffProduct(
        @PathVariable id: String,
        @RequestBody request: ProductDictRowUpdateRequest
    ): Mono<ApiResponse<ProductDictRowDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getProductDictRowById(UUID.fromString(id))
                    .flatMap { existingProduct ->
                        if (tokenProjectId.isPresent && existingProduct.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления продукта тарифа из другого проекта", 
                                RuntimeException("Product projectId: ${existingProduct.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<ProductDictRowDto>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                        } else {
                            tariffService.updateProductDictRow(UUID.fromString(id), request, currentUserId)
                        }
                    }
            }
            .map { product ->
                val response = ApiResponse.success(product, "Продукт тарифа успешно обновлен")
                response
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении продукта тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<ProductDictRowDto>("Ошибка обновления продукта тарифа: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/products/{id}")
    fun deleteTariffProduct(@PathVariable id: String): Mono<ApiResponse<String>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getProductDictRowById(UUID.fromString(id))
                    .flatMap { existingProduct ->
                        if (tokenProjectId.isPresent && existingProduct.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления продукта тарифа из другого проекта", 
                                RuntimeException("Product projectId: ${existingProduct.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                        } else {
                            tariffService.deleteProductDictRow(UUID.fromString(id))
                        }
                    }
            }
            .map {
                ApiResponse.success("Продукт тарифа успешно удален")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении продукта тарифа: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления продукта тарифа: ${error.message}"))
            }
    }

    // Endpoints для работы с тарифными матрицами

    @GetMapping("/tariffs/products/{productDictRowId}/matrices")
    fun getTariffMatrices(@PathVariable productDictRowId: String): Mono<ApiResponse<List<TariffMatrixDto>>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getProductDictRowById(UUID.fromString(productDictRowId))
                    .flatMap { product ->
                        if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                            logError("Попытка доступа к тарифным матрицам из другого проекта", 
                                RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<List<TariffMatrixDto>>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                        } else {
                            tariffService.getTariffMatricesByProductDictRow(UUID.fromString(productDictRowId))
                                .collectList()
                        }
                    }
            }
            .map { matrices ->
                ApiResponse.success(matrices)
            }
            .onErrorResume { error ->
                logError("Ошибка при получении тарифных матриц: ${error.message}", error)
                Mono.just(ApiResponse.error<List<TariffMatrixDto>>("Ошибка получения тарифных матриц: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/matrices")
    fun createTariffMatrix(@RequestBody request: TariffMatrixCreateRequest): Mono<ApiResponse<TariffMatrixDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getProductDictRowById(request.productDictRowId)
                    .flatMap { product ->
                        if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                            logError("Попытка создания тарифной матрицы в другом проекте", 
                                RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<TariffMatrixDto>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                        } else {
                            tariffService.createTariffMatrix(request, currentUserId)
                        }
                    }
            }
            .map { matrix ->
                ApiResponse.success(matrix, "Тарифная матрица успешно создана")
            }
            .onErrorResume { error ->
                logError("Ошибка при создании тарифной матрицы: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffMatrixDto>("Ошибка создания тарифной матрицы: ${error.message}"))
            }
    }

    @PutMapping("/tariffs/matrices/{id}")
    fun updateTariffMatrix(
        @PathVariable id: String,
        @RequestBody request: TariffMatrixUpdateRequest
    ): Mono<ApiResponse<TariffMatrixDto>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getTariffMatrixById(UUID.fromString(id))
                    .flatMap { matrix ->
                        tariffService.getProductDictRowById(matrix.productDictRowId!!)
                            .flatMap { product ->
                                if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                                    logError("Попытка обновления тарифной матрицы из другого проекта", 
                                        RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                                    Mono.error<TariffMatrixDto>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                                } else {
                                    tariffService.updateTariffMatrix(UUID.fromString(id), request, currentUserId)
                                }
                            }
                    }
            }
            .map { matrix ->
                ApiResponse.success(matrix, "Тарифная матрица успешно обновлена")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении тарифной матрицы: ${error.message}", error)
                Mono.just(ApiResponse.error<TariffMatrixDto>("Ошибка обновления тарифной матрицы: ${error.message}"))
            }
    }

    @PostMapping("/tariffs/products/{productDictRowId}/matrix")
    fun updateTariffMatrixForProduct(
        @PathVariable productDictRowId: String,
        @RequestBody request: TariffMatrixBulkUpdateRequest
    ): Mono<ApiResponse<List<TariffMatrixDto>>> {
        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                tariffService.getProductDictRowById(UUID.fromString(productDictRowId))
                    .flatMap { product ->
                        if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                            logError("Попытка обновления тарифной матрицы для продукта из другого проекта", 
                                RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<List<TariffMatrixDto>>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                        } else {
                            tariffService.updateTariffMatrixForProduct(UUID.fromString(productDictRowId), request, currentUserId)
                                .collectList()
                        }
                    }
            }
            .map { matrices ->
                ApiResponse.success(matrices, "Тарифная матрица успешно обновлена")
            }
            .onErrorResume { error ->
                logError("Ошибка при обновлении тарифной матрицы для продукта: ${error.message}", error)
                Mono.just(ApiResponse.error<List<TariffMatrixDto>>("Ошибка обновления тарифной матрицы: ${error.message}"))
            }
    }

    @DeleteMapping("/tariffs/matrices/{id}")
    fun deleteTariffMatrix(@PathVariable id: String): Mono<ApiResponse<String>> {
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                tariffService.getTariffMatrixById(UUID.fromString(id))
                    .flatMap { matrix ->
                        tariffService.getProductDictRowById(matrix.productDictRowId!!)
                            .flatMap { product ->
                                if (tokenProjectId.isPresent && product.projectId != tokenProjectId.get()) {
                                    logError("Попытка удаления тарифной матрицы из другого проекта", 
                                        RuntimeException("Product projectId: ${product.projectId}, Token projectId: $tokenProjectId"))
                                    Mono.error<String>(RuntimeException("Доступ запрещен: продукт тарифа принадлежит другому проекту"))
                                } else {
                                    tariffService.deleteTariffMatrix(UUID.fromString(id))
                                }
                            }
                    }
            }
            .map {
                ApiResponse.success("Тарифная матрица успешно удалена")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении тарифной матрицы: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифной матрицы: ${error.message}"))
            }
    }

    // Endpoints для работы с маршрутами и тарифами

    @GetMapping("/routes/tariffs")
    fun getRouteTariffs(@RequestParam(required = false) projectId: String?): Mono<ApiResponse<Map<String, List<RouteTariffsResponse>>>> {
        logUserAction("запрашивает тарифы по маршрутам", projectId ?: "все проекты")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                val effectiveProjectId = tokenProjectId.getOrDefault(projectId?.let { UUID.fromString(it) })
                logUserAction("использует projectId для фильтрации тарифов по маршрутам", effectiveProjectId?.toString() ?: "все проекты")
                
                tariffService.getRouteTariffs(effectiveProjectId)
                    .collectList()
                    .map { routeTariffs ->
                        val response = mapOf(
                            "content" to routeTariffs
                        )
                        ApiResponse.success(response)
                    }
            }
            .onErrorResume { error ->
                logError("Ошибка при получении тарифов по маршрутам: ${error.message}", error)
                Mono.just(ApiResponse.error<Map<String, List<RouteTariffsResponse>>>("Ошибка получения тарифов по маршрутам: ${error.message}"))
            }
    }

    @PostMapping("/routes/tariffs/assign")
    fun assignTariffToRoute(@RequestBody request: RouteTariffAssignmentCreateRequest): Mono<ApiResponse<RouteTariffAssignmentDto>> {
        logUserAction("назначает тариф на маршрут", "Route: ${request.routeId}, Tariff: ${request.tariffId}")

        return Mono.zip(JwtUtils.getCurrentProjectId(), JwtUtils.getCurrentUserId())
            .flatMap { res ->
                val tokenProjectId = res.t1
                val currentUserId = res.t2

                // Проверяем, что тариф принадлежит проекту из токена
                tariffService.getTariffById(request.tariffId)
                    .flatMap { tariff ->
                        if (tokenProjectId.isPresent && tariff.projectId != tokenProjectId.get()) {
                            logError("Попытка назначения тарифа из другого проекта на маршрут", 
                                RuntimeException("Tariff projectId: ${tariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<RouteTariffAssignmentDto>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.assignTariffToRoute(request, currentUserId)
                        }
                    }
            }
            .map { assignment ->
                logUserAction("успешно назначил тариф на маршрут", "Route: ${assignment.routeId}, Tariff: ${assignment.tariffId}")
                ApiResponse.success(assignment, "Тариф успешно назначен на маршрут")
            }
            .onErrorResume { error ->
                logError("Ошибка при назначении тарифа на маршрут: ${error.message}", error)
                Mono.just(ApiResponse.error<RouteTariffAssignmentDto>("Ошибка назначения тарифа на маршрут: ${error.message}"))
            }
    }

    @DeleteMapping("/routes/{routeId}/tariffs/{tariffId}")
    fun removeTariffFromRoute(
        @PathVariable routeId: String,
        @PathVariable tariffId: String
    ): Mono<ApiResponse<String>> {
        logUserAction("удаляет тариф с маршрута", "Route: $routeId, Tariff: $tariffId")
        
        return JwtUtils.getCurrentProjectId()
            .flatMap { tokenProjectId ->
                // Проверяем, что тариф принадлежит проекту из токена
                tariffService.getTariffById(UUID.fromString(tariffId))
                    .flatMap { tariff ->
                        if (tokenProjectId.isPresent && tariff.projectId != tokenProjectId.get()) {
                            logError("Попытка удаления тарифа из другого проекта с маршрута", 
                                RuntimeException("Tariff projectId: ${tariff.projectId}, Token projectId: $tokenProjectId"))
                            Mono.error<String>(RuntimeException("Доступ запрещен: тариф принадлежит другому проекту"))
                        } else {
                            tariffService.removeTariffFromRoute(UUID.fromString(routeId), UUID.fromString(tariffId))
                        }
                    }
            }
            .map { success ->
                logUserAction("успешно удалил тариф с маршрута", "Route: $routeId, Tariff: $tariffId")
                ApiResponse.success("Тариф успешно удален с маршрута")
            }
            .onErrorResume { error ->
                logError("Ошибка при удалении тарифа с маршрута: ${error.message}", error)
                Mono.just(ApiResponse.error<String>("Ошибка удаления тарифа с маршрута: ${error.message}"))
            }
    }
} 