package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.config.KeycloakConfig
import java.util.*

@Service
class KeycloakIntegrationService(
    private val keycloakConfig: KeycloakConfig,
    private val keycloakWebClient: WebClient
) {
    
    private fun getAccessToken(): Mono<String> {
        return keycloakWebClient.post()
            .uri("/realms/${keycloakConfig.realm}/protocol/openid-connect/token")
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .body(BodyInserters.fromFormData("grant_type", "client_credentials")
                .with("client_id", keycloakConfig.clientAdminId)
                .with("client_secret", keycloakConfig.clientSecret))
            .retrieve()
            .bodyToMono(KeycloakTokenResponse::class.java)
            .doOnNext { response ->
                println("=== Keycloak Token Response ===")
                println("Access Token: ${response.accessToken.take(20)}...")
                println("Token Type: ${response.tokenType}")
                println("Expires In: ${response.expiresIn}")
                println("Refresh Expires In: ${response.refreshExpiresIn}")
                println("Refresh Token: ${response.refreshToken}")
                println("Scope: ${response.scope}")
                println("===============================")
            }
            .map { it.accessToken }
    }
    
    /**
     * Регистрирует пользователя в Keycloak
     */
    fun registerUserInKeycloak(
        username: String,
        password: String,
        firstName: String,
        lastName: String,
        email: String?,
        role: String,
        attributes: Map<String, String>
    ): Mono<UUID> {
        println("=== Creating Keycloak User ===")
        println("Username: $username")
        println("First Name: $firstName")
        println("Last Name: $lastName")
        println("Email: $email")
        println("Role: $role")
        println("Attributes: $attributes")
        println("=============================")
        
        return getAccessToken().flatMap { token ->
            val userRequest = KeycloakUserCreateRequest(
                username = username,
                email = email,
                firstName = firstName,
                lastName = lastName,
                enabled = true,
                attributes = attributes.mapValues { listOf(it.value) }
            )
            
            println("=== Keycloak User Request ===")
            println("Request: $userRequest")
            println("=============================")
            
            keycloakWebClient.post()
                .uri("/admin/realms/${keycloakConfig.realm}/users")
                .header("Authorization", "Bearer $token")
                .bodyValue(userRequest)
                .retrieve()
                .toBodilessEntity()
                .doOnNext { response ->
                    println("=== Keycloak User Creation Response ===")
                    println("Status: ${response.statusCode}")
                    println("Headers: ${response.headers}")
                    println("Location: ${response.headers.location}")
                    println("=======================================")
                }
                .flatMap { response ->
                    // Получаем ID созданного пользователя из заголовка Location
                    val location = response.headers.location
                    val userId = location?.path?.split("/")?.last()
                    
                    println("=== Extracted User ID ===")
                    println("Location: $location")
                    println("User ID: $userId")
                    println("========================")
                    
                    if (userId != null) {
                        // Назначаем роль пользователю
                        //TODO Разобраться почему не назначается роль
//                        assignRoleToUser(UUID.fromString(userId), role, token)
//                            .thenReturn(UUID.fromString(userId))
                        Mono.just(UUID.fromString(userId))
                    } else {
                        Mono.error(RuntimeException("Не удалось получить ID созданного пользователя"))
                    }
                }
        }
    }
    
    private fun assignRoleToUser(userId: UUID, role: String, token: String): Mono<Void> {
        val realmRole = getRealmRoleByEmployeeRole(role)
        
        println("=== Assigning Role to User ===")
        println("User ID: $userId")
        println("Employee Role: $role")
        println("Realm Role: $realmRole")
        println("=============================")
        
        return keycloakWebClient.get()
            .uri("/admin/realms/${keycloakConfig.realm}/roles/$realmRole")
            .header("Authorization", "Bearer $token")
            .retrieve()
            .onStatus({ status -> status.is4xxClientError || status.is5xxServerError }) { response ->
                println("=== Error getting role info ===")
                println("Status: ${response.statusCode()}")
                println("Role: $realmRole")
                println("==============================")
                response.bodyToMono(String::class.java)
                    .flatMap { body ->
                        println("Error body: $body")
                        Mono.error(RuntimeException("Failed to get role info for $realmRole: ${response.statusCode()} - $body"))
                    }
            }
            .bodyToMono(KeycloakRole::class.java)
            .doOnNext { roleInfo ->
                println("=== Keycloak Role Info ===")
                println("Role Info: $roleInfo")
                println("==========================")
            }
            .flatMap { roleInfo ->
                val roleAssignment = KeycloakRoleAssignment(
                    roles = listOf(roleInfo)
                )
                
                println("=== Role Assignment Request ===")
                println("Role Assignment: $roleAssignment")
                println("===============================")
                
                keycloakWebClient.post()
                    .uri("/admin/realms/${keycloakConfig.realm}/users/$userId/role-mappings/realm")
                    .header("Authorization", "Bearer $token")
                    .bodyValue(roleAssignment)
                    .retrieve()
                    .onStatus({ status -> status.is4xxClientError || status.is5xxServerError }) { response ->
                        println("=== Error assigning role ===")
                        println("Status: ${response.statusCode()}")
                        println("User ID: $userId")
                        println("Role: $realmRole")
                        println("==========================")
                        response.bodyToMono(String::class.java)
                            .flatMap { body ->
                                println("Error body: $body")
                                Mono.error(RuntimeException("Failed to assign role $realmRole to user $userId: ${response.statusCode()} - $body"))
                            }
                    }
                    .toBodilessEntity()
                    .doOnNext { response ->
                        println("=== Role Assignment Response ===")
                        println("Status: ${response.statusCode}")
                        println("Headers: ${response.headers}")
                        println("================================")
                    }
                    .then()
            }
    }
    
    /**
     * Обновляет данные пользователя в Keycloak
     */
    fun updateUserInKeycloak(
        userId: UUID,
        firstName: String?,
        lastName: String?,
        email: String?,
        enabled: Boolean?,
        attributes: Map<String, String>?
    ): Mono<Void> {
        return getAccessToken().flatMap { token ->
            val userRequest = KeycloakUserUpdateRequest(
                username = null, // Не обновляем username
                email = email,
                firstName = firstName,
                lastName = lastName,
                enabled = enabled,
                attributes = attributes?.mapValues { listOf(it.value) }
            )
            
            keycloakWebClient.put()
                .uri("/admin/realms/${keycloakConfig.realm}/users/$userId")
                .header("Authorization", "Bearer $token")
                .bodyValue(userRequest)
                .retrieve()
                .toBodilessEntity()
                .then()
        }
    }
    
    /**
     * Деактивирует пользователя в Keycloak
     */
    fun deactivateUserInKeycloak(userId: UUID): Mono<Void> {
        return getAccessToken().flatMap { token ->
            val userRequest = KeycloakUserUpdateRequest(enabled = false)
            
            keycloakWebClient.put()
                .uri("/admin/realms/${keycloakConfig.realm}/users/$userId")
                .header("Authorization", "Bearer $token")
                .bodyValue(userRequest)
                .retrieve()
                .toBodilessEntity()
                .then()
        }
    }
    
    /**
     * Получает realm role по роли сотрудника
     */
    fun getRealmRoleByEmployeeRole(employeeRole: String): String {
        return when (employeeRole) {
            "driver" -> "terminal_user_driver"
            "admin" -> "terminal_user_admin"
            "cashier" -> "terminal_user_cashier"
            "collector" -> "terminal_user_collector"
            "conductor" -> "terminal_user_conductor"
            "controller" -> "terminal_user_controler"
            else -> "terminal_user_driver"
        }
    }
    
    /**
     * Получает список всех доступных ролей в Keycloak
     */
    fun getAvailableRoles(): Mono<List<KeycloakRole>> {
        return getAccessToken().flatMap { token ->
            keycloakWebClient.get()
                .uri("/admin/realms/${keycloakConfig.realm}/roles")
                .header("Authorization", "Bearer $token")
                .retrieve()
                .onStatus({ status -> status.is4xxClientError || status.is5xxServerError }) { response ->
                    println("=== Error getting available roles ===")
                    println("Status: ${response.statusCode()}")
                    println("================================")
                    response.bodyToMono(String::class.java)
                        .flatMap { body ->
                            println("Error body: $body")
                            Mono.error(RuntimeException("Failed to get available roles: ${response.statusCode()} - $body"))
                        }
                }
                .bodyToMono(Array<KeycloakRole>::class.java)
                .map { it.toList() }
                .doOnNext { roles ->
                    println("=== Available Keycloak Roles ===")
                    roles.forEach { role ->
                        println("Role: ${role.name} (ID: ${role.id})")
                    }
                    println("================================")
                }
        }
    }
} 