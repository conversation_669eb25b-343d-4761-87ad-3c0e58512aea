server:
  port: 8085

spring:
  application:
    name: pro-controller
  
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # R2DBC конфигурация для local профиля
  r2dbc:
    url: r2dbc:${R2DB_URL:postgresql://postgres:postgres@localhost:5437/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  # ClickHouse конфигурация
  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://************:8123/dev_reports}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5437/pro}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

logging:
  level:
    ru.sbertroika.tkp3.pro.controller: INFO
    org.springframework.web: INFO
    reactor.netty: WARN
    org.flywaydb: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] [%X{userId}] %-5level %logger{36} - %msg%n"

# Конфигурация для контроллера
controller:
  pagination:
    default-page-size: 20
    max-page-size: 100

# Keycloak конфигурация
keycloak:
  auth-server-url: ${KEYCLOAK_URL:https://dev-auth.sbertroika.tech}
  realm: ${KEYCLOAK_REALM:test-asop}
  client-id: ${KEYCLOAK_CLIENT_ID:crm-ui-local} 