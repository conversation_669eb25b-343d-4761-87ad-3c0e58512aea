server:
  port: 8080

spring:
  application:
    name: pro-controller

  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    operation_journal_topic: ${MANIFEST_JOURNAL_TOPIC:PRO.MANIFEST.JOURNAL}
  
  # R2DBC конфигурация
  r2dbc:
    url: r2dbc:${R2DB_URL:postgresql://localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  # ClickHouse конфигурация
  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://click-0.tkp2.prod:8123/reports}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:false}


# Keycloak конфигурация
keycloak:
  auth-server-url: ${KEYCLOAK_URL:https://dev-auth.sbertroika.tech}
  realm: ${KEYCLOAK_REALM:test-asop}
  client-id: ${KEYCLOAK_CLIENT_ID:crm-ui-local}
  client-admin-id: ${KEYCLOAK_CLIENT_ADMIN_ID:admin-cli}
  client-secret: ${KEYCLOAK_CLIENT_SECRET:admin-cli}
  admin-username: ${KEYCLOAK_ADMIN_USER:admin}
  admin-password: ${KEYCLOAK_ADMIN_PASSWORD:admin}

# Настройки TMS API
tms:
  api:
    base-url: ${TMS_CONTROLLER_URL:http://localhost:8088}

s3:
  url: ${S3_URL:localhost:8080}
  access_key_id: ${S3_ACCESS_KEY_ID:fsdfsdf}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:fsdfsdfsd}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181,localhost:2182,localhost:2183}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'