# PRO System - Production Ports and Protocols Summary

## Overview
This document provides a comprehensive summary of all ports, protocols, and network configurations for the PRO (Production Resource Optimization) system in production environment.

## External Access Points

### Istio Ingress Gateway
**Namespace**: `istio-ingressgateway`  
**Service**: `istio-ingressgateway`  
**Type**: LoadBalancer

| Port | Protocol | Purpose | TLS | Notes |
|------|----------|---------|-----|-------|
| 80 | HTTP | Redirect to HTTPS | No | Automatic redirect to port 443 |
| 443 | HTTPS | Main external access | Yes | TLS termination, wildcard-sbertroika-ru |

**URLs**:
- `https://lko.sbertroika.ru` - Main production URL

## Internal Service Ports

### Frontend Services

#### PRO UI
**Namespace**: `pro-prod`  
**Service**: `pro-ui.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 80 | HTTP | Web interface | nginx:stable-alpine | Vue.js SPA, static files |

**Resources**:
- CPU: 500m
- Memory: 256Mi
- Replicas: 1

**Health Checks**: Disabled (Vue.js application)

### API Services

#### PRO Controller
**Namespace**: `pro-prod`  
**Service**: `pro-controller.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 8080 | HTTP | REST API | Kotlin Spring Boot | Main business logic API |

**Resources**:
- CPU: 1000m
- Memory: 512Mi
- Replicas: 1

**Health Checks**:
- Liveness: `/actuator/health/liveness`
- Readiness: `/actuator/health/readiness`
- Port: 8080

#### PRO Processing
**Namespace**: `pro-prod`  
**Service**: `pro-processing.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 8080 | HTTP | Processing API | Kotlin Spring Boot | Data processing services |

**Resources**:
- CPU: 1000m
- Memory: 512Mi
- Replicas: 1

**Health Checks**:
- Liveness: `/actuator/health/liveness`
- Readiness: `/actuator/health/readiness`
- Port: 8080

#### PRO Manifest Processing
**Namespace**: `pro-prod`  
**Service**: `pro-manifest-processing.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 8080 | HTTP | Manifest API | Kotlin Spring Boot | Manifest processing services |

**Resources**:
- CPU: 1000m
- Memory: 512Mi
- Replicas: 1

**Health Checks**:
- Liveness: `/actuator/health/liveness`
- Readiness: `/actuator/health/readiness`
- Port: 8080

### Gate Services

#### PRO Gate
**Namespace**: `pro-prod`  
**Service**: `pro-gate.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 8080 | HTTP | External API | Kotlin Spring Boot | Public API gateway |

**Resources**:
- CPU: 1000m
- Memory: 512Mi
- Replicas: 1

**Health Checks**:
- Liveness: `/actuator/health/liveness`
- Readiness: `/actuator/health/readiness`
- Port: 8080

#### PRO Gate Private
**Namespace**: `pro-prod`  
**Service**: `pro-gate-private.pro-prod.svc.cluster.local`

| Port | Protocol | Purpose | Container | Notes |
|------|----------|---------|-----------|-------|
| 8080 | HTTP | Private API | Kotlin Spring Boot | Internal API gateway |

**Resources**:
- CPU: 1000m
- Memory: 512Mi
- Replicas: 1

**Health Checks**:
- Liveness: `/actuator/health/liveness`
- Readiness: `/actuator/health/readiness`
- Port: 8080

## External Service Ports

### Database Services

#### PostgreSQL
**Type**: External managed service  
**Protocol**: TCP  
**Port**: 5432

| IP Address | Database | Purpose |
|------------|----------|---------|
| ************ | pro | Main production database |

**Connection Details**:
- Driver: JDBC/R2DBC
- Connection Pool: HikariCP
- Max Connections: 20
- Timeout: 30 seconds
- SSL: Required

### Message Broker Services

#### Kafka Cluster
**Type**: External cluster  
**Protocol**: TCP  
**Port**: 9092

| Broker | Host | Port |
|--------|------|------|
| 1 | kafka1-prod-new.tkp2.prod | 9092 |
| 2 | kafka2-prod-new.tkp2.prod | 9092 |
| 3 | kafka3-prod-new.tkp2.prod | 9092 |

**Configuration**:
- Replication Factor: 3
- Producer acks: all
- Consumer auto.offset.reset: earliest
- Topics: PRO.OPERATION.IN, PRO.MANIFEST.IN, PRO.PROCESSING.IN

#### RabbitMQ
**Type**: External service  
**Protocol**: AMQP  
**Port**: 5672

| IP Address | Port |
|------------|------|
| ************ | 5672 |

**Configuration**:
- Virtual Host: /
- Heartbeat: 60 seconds
- Connection Timeout: 30 seconds
- Channel Pool: 10

### Coordination Services

#### Zookeeper Ensemble
**Type**: External ensemble  
**Protocol**: TCP  
**Port**: 2181

| Node | Host | Port |
|------|------|------|
| 1 | kafka-zoo-01.st.corp | 2181 |
| 2 | kafka-zoo-02.st.corp | 2181 |
| 3 | kafka-zoo-03.st.corp | 2181 |

**Configuration**:
- Session Timeout: 6000ms
- Connection Timeout: 6000ms
- Purpose: Kafka coordination

### Analytics Services

#### ClickHouse
**Type**: External service  
**Protocol**: HTTP/TCP  
**Host**: click-0.tkp2.prod

| Port | Protocol | Purpose |
|------|----------|---------|
| 8123 | HTTP | HTTP interface |
| 9000 | Native | Native interface |

**Configuration**:
- Connection Pool: 5
- Query Timeout: 60 seconds
- Purpose: Analytics and reporting

### Authentication & Security

#### Keycloak
**Type**: External service  
**Protocol**: HTTPS  
**Port**: 8080

**Configuration**:
- Realm: release_asop
- URL: https://auth.sbertroika.ru
- Client ID: crm-ui
- Protocol: OAuth2/OIDC
- JWT Signing: RS256
- Access Token TTL: 5 minutes
- Refresh Token TTL: 30 minutes

#### Vault
**Type**: External service  
**Protocol**: HTTPS  
**Port**: 8200

**Configuration**:
- Auth Method: Kubernetes
- Secrets Engine: KV v2
- Lease TTL: 1 hour
- Service Account: pro-system
- Policies: pro-secrets-read

### Storage Services

#### OBS S3
**Type**: External service  
**Protocol**: HTTPS  
**Port**: 9000

**Configuration**:
- Endpoint: https://s3.sbertroika.ru
- Bucket: tkp3-manifest
- Region: ru-central1
- Access: Environment variables + Vault
- Operations: Multipart upload, streaming download

## Network Flow Summary

### External Traffic Flow
```
Internet → Istio Ingress Gateway (80/443) → Istio Gateway → Istio Virtual Service → Internal Services
```

### Internal Service Communication
- **PRO UI** → **PRO Controller**: HTTP 8080
- **PRO UI** → **PRO Processing**: HTTP 8080
- **PRO UI** → **PRO Manifest Processing**: HTTP 8080

### External Service Connections
- **All Services** → **PostgreSQL**: TCP 5432
- **All Services** → **Kafka**: TCP 9092 (3 brokers)
- **All Services** → **Zookeeper**: TCP 2181 (3 nodes)
- **PRO Controller** → **RabbitMQ**: AMQP 5672
- **PRO Controller** → **ClickHouse**: HTTP 8123
- **All Services** → **OBS S3**: HTTPS 9000
- **All Services** → **Keycloak**: HTTPS 8080
- **All Services** → **Vault**: HTTPS 8200

## Security Configuration

### TLS Requirements
- **External connections**: TLS 1.2+ required
- **Internal cluster**: mTLS via Istio
- **Certificate validation**: Required for all external services

### Authentication
- **User authentication**: Keycloak OAuth2/OIDC
- **Service-to-service**: mTLS certificates
- **API access**: JWT tokens with RS256 signing

### Authorization
- **Kubernetes**: RBAC with service accounts
- **Network policies**: Restricted pod-to-pod communication
- **Service mesh**: Istio authorization policies

## Monitoring & Observability

### Health Checks
- **Liveness**: `/actuator/health/liveness` (Spring Boot services)
- **Readiness**: `/actuator/health/readiness` (Spring Boot services)
- **Startup**: `/actuator/health/startup` (Spring Boot services)

### Metrics
- **Prometheus**: `/actuator/prometheus` endpoint
- **Custom metrics**: Business KPIs and application metrics
- **Istio metrics**: Traffic flow and service mesh metrics

### Logging
- **Format**: Structured logging (ECS format)
- **Centralization**: ELK Stack
- **Levels**: INFO, WARN, ERROR
- **Retention**: 30 days

### Tracing
- **Distributed tracing**: Istio + Jaeger
- **Sampling rate**: 10%
- **Trace context**: Propagated across services



## Deployment Notes

### Helm Charts
- **pro-ui**: Frontend deployment
- **pro-controller**: Main API controller
- **pro-processing**: Data processing services
- **pro-manifest-processing**: Manifest processing services
- **pro-gate**: External API gateway
- **pro-gate-private**: Private API gateway

### Namespace Strategy
- **pro-prod**: Main application services
- **istio-ingressgateway**: Gateway configuration

### Resource Allocation
- **Frontend**: 500m CPU, 256Mi RAM
- **Backend services**: 1000m CPU, 512Mi RAM
- **Scaling**: Horizontal pod autoscaling based on CPU/memory

## Troubleshooting

### Common Issues
1. **TLS certificate issues**: Verify credential names and certificate validity
2. **Connection timeouts**: Check external service availability and network policies
3. **Health check failures**: Verify actuator endpoints and service readiness

### Debug Commands
```bash
# Verify Istio configuration
istioctl analyze -n pro-prod

# Check service connectivity
kubectl exec -it <pod> -n pro-prod -- nc -zv <service> <port>

# View Istio proxy logs
kubectl logs <pod> -c istio-proxy -n pro-prod
```

## Performance Considerations

### Connection Pooling
- **Database**: HikariCP with 20 max connections
- **Kafka**: Producer/consumer connection pooling
- **HTTP clients**: Connection reuse and keep-alive

### Timeouts
- **HTTP requests**: 30 seconds default
- **Database queries**: 30 seconds
- **External calls**: 60 seconds for analytics

### Resource Limits
- **Memory**: JVM heap limited to 512Mi
- **CPU**: Burstable with 1000m requests
- **Network**: No specific bandwidth limits
