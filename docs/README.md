# PRO System - Production Deployment Documentation

Этот раздел содержит UML диаграммы развертывания для системы PRO (Production Resource Optimization) в продакшен среде.

## Диаграммы

### 1. PRO Production Deployment Diagram (`deployment-diagram.puml`)
Продакшен архитектурная диаграмма развертывания, показывающая:
- Все сервисы системы в Kubernetes
- Внешние зависимости и интеграции
- Основные связи между компонентами
- Стек технологий и ресурсы

### 2. Production Ports and Protocols (`ports-and-protocols.puml`)
Детальная диаграмма портов и протоколов для продакшена:
- Все порты (внешние и внутренние)
- Протоколы связи и безопасность
- Kubernetes ресурсы и ограничения
- Назначение каждого компонента

## Архитектура системы

### Основные компоненты

#### Load Balancer Layer (Единая точка входа)
- **Istio Ingress Gateway** - Единая точка входа для всех внешних запросов
  - Порт 80: HTTP (redirect)
  - Порт 443: HTTPS (TLS)
  - Функции: TLS терминация, Load Balancing, SSL offloading
  - **URLs & TLS**:
    - https://lko.sbertroika.ru (TLS: wildcard-sbertroika-ru)

#### Istio Service Mesh
- **Istio Gateway** - Правила маршрутизации трафика
  - Hosts: lko.sbertroika.ru
  - Протоколы: HTTPS

#### Gateway Configurations

##### 1. pro-ui-gateway (lko.sbertroika.ru)
```yaml
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: pro-ui-gateway
  namespace: istio-ingressgateway
spec:
  servers:
  - port:
      number: 443
      protocol: HTTPS
      name: https
    hosts:
    - lko.sbertroika.ru
    tls:
      mode: SIMPLE
      credentialName: wildcard-sbertroika-ru
  selector:
    istio: ingressgateway
```

- **Istio Virtual Service** - Внутренняя маршрутизация между сервисами
  - Load Balancing: Round Robin, Least Connections
  - Retry Policies: 3 attempts with exponential backoff
  - Circuit Breaker: Enabled for fault tolerance
  - Timeout Configuration: 30 seconds default
  - Traffic Splitting: A/B testing capabilities
  - Service Discovery: Kubernetes native

#### Frontend Layer
- **PRO UI** - Vue.js SPA приложение
  - Порт: 80 (HTTP)
  - Контейнер: nginx:stable-alpine
  - Kubernetes: Deployment + Service
  - Ресурсы: 500m CPU, 256Mi RAM
  - Назначение: Пользовательский интерфейс для управления производственными ресурсами

#### API Gateway Layer
- **PRO Controller** - Основной API сервис
  - Порт 8080: HTTP/REST API + Health checks
  - Технология: Kotlin + Spring Boot + JDK 17
  - Kubernetes: Deployment + Service
  - Ресурсы: 1000m CPU, 512Mi RAM
  - **Kubernetes Service**: pro-controller.pro-prod.svc.cluster.local

#### Processing Layer
- **PRO Processing** - Сервис обработки данных
  - Порт 8080: HTTP API + Health checks
  - Технология: Kotlin + Spring Boot + JDK 17
  - Kubernetes: Deployment + Service
  - Ресурсы: 1000m CPU, 512Mi RAM
  - **Kubernetes Service**: pro-processing.pro-prod.svc.cluster.local

#### Manifest Processing Layer
- **PRO Manifest Processing** - Сервис обработки манифестов
  - Порт 8080: HTTP API + Health checks
  - Технология: Kotlin + Spring Boot + JDK 17
  - Kubernetes: Deployment + Service
  - Ресурсы: 1000m CPU, 512Mi RAM
  - **Kubernetes Service**: pro-manifest-processing.pro-prod.svc.cluster.local

#### Gate Services
- **PRO Gate** - Внешний API сервис
  - Порт 8080: HTTP/REST API + Health checks
  - Технология: Kotlin + Spring Boot + JDK 17
  - Kubernetes: Deployment + Service
  - Ресурсы: 1000m CPU, 512Mi RAM
  - **Kubernetes Service**: pro-gate.pro-prod.svc.cluster.local

- **PRO Gate Private** - Приватный API сервис
  - Порт 8080: HTTP/REST API + Health checks
  - Технология: Kotlin + Spring Boot + JDK 17
  - Kubernetes: Deployment + Service
  - Ресурсы: 1000m CPU, 512Mi RAM
  - **Kubernetes Service**: pro-gate-private.pro-prod.svc.cluster.local

#### Infrastructure Services
- **Kafka Cluster** - Потоковая обработка сообщений
  - Порт: 9092
  - Тип: Внешний сервис
  - Брокеры: kafka1-prod-new.tkp2.prod:9092, kafka2-prod-new.tkp2.prod:9092, kafka3-prod-new.tkp2.prod:9092
  - Replication Factor: 3
- **ClickHouse** - Аналитическая база данных
  - Порт: 8123 (HTTP), 9000 (Native)
  - Тип: Внешний сервис
  - Особенности: High Availability, Column-oriented storage

#### Authentication & Security
- **Keycloak** - Управление идентификацией
  - Порт: 8080
  - Особенности: High Availability, OAuth2/JWT
  - База данных: postgres (внешняя)
  - Realm: https://auth.sbertroika.ru/realms/release_asop
- **Vault** - Управление секретами
  - Порт: 8200
  - Особенности: Secrets Management, Certificate Management

### External Services
- **PostgreSQL** - Основная база данных
  - Порт: 5432
  - Тип: Внешний управляемый сервис
  - Особенности: High Availability, Backup & Recovery
  - База: pro
  - **IP адрес**: ************
- **Zookeeper Ensemble** - Координация Kafka
  - Порт: 2181
  - Тип: Внешний сервис
  - Узлы: kafka-zoo-01.st.corp:2181, kafka-zoo-02.st.corp:2181, kafka-zoo-03.st.corp:2181
  - Назначение: Координация Kafka и управление метаданными
- **RabbitMQ** - Очереди сообщений
  - Порт: 5672 (AMQP), 15672 (Management)
  - Тип: Внешний сервис
  - **IP адрес**: ************

### Kafka Topics

#### Входные топики
- `PRO.OPERATION.IN` - Операции PRO
- `PRO.OPERATION.SHIFT.IN` - Сменные операции PRO
- `PRO.MANIFEST.IN` - Манифесты PRO
- `PRO.PROCESSING.IN` - Задачи обработки PRO

#### Выходные топики
- `PRO.EVENT.FAIL.OUT` - События ошибок PRO
- `PRO.JOURNAL.OUT` - Журнал событий PRO
- `PRO.ANALYTICS.OUT` - Аналитические данные PRO

### Внешние зависимости
- **Stop List Service** - Внешний сервис проверки стоп-листов
- **ClickHouse** - Внешняя аналитическая БД
- **Vault** - Внешний сервис управления секретами

## Развертывание

### Kubernetes
Основная платформа развертывания с использованием Helm charts:
- **pro-ui** - Развертывание Frontend
- **pro-controller** - Развертывание API Controller
- **pro-processing** - Развертывание Processing сервиса
- **pro-manifest-processing** - Развертывание Manifest Processing
- **pro-gate** - Развертывание External API Gateway
- **pro-gate-private** - Развертывание Private API Gateway

### Service Mesh
- **Istio Ingress Gateway** - Единая точка входа для всех внешних запросов
- **Istio Gateway** - Правила маршрутизации трафика
- **Istio Virtual Service** - Внутренняя маршрутизация между сервисами

### Мониторинг и логирование
- **Prometheus + Grafana** - Сбор и визуализация метрик
- **ELK Stack** - Централизованное логирование
- **Health Checks** - Проверка состояния сервисов

### Переменные окружения
Основные переменные для конфигурации:
- `DB_URL`, `DB_USER`, `DB_PASSWORD` - Настройки postgres
- `KAFKA_SERVERS` - Настройки Kafka (kafka1-prod-new.tkp2.prod:9092;kafka2-prod-new.tkp2.prod:9092;kafka3-prod-new.tkp2.prod:9092)
- `ZOOKEEPER_NODES` - Настройки Zookeeper (kafka-zoo-01.st.corp:2181,kafka-zoo-02.st.corp:2181,kafka-zoo-03.st.corp:2181)
- `RABBITMQ_HOST` - Настройки RabbitMQ (************:5672)
- `CLICKHOUSE_HOST` - Настройки ClickHouse (click-0.tkp2.prod:8123)
- `KEYCLOAK_*` - Настройки аутентификации

## Безопасность
- Аутентификация через Keycloak OAuth2/JWT
- TLS шифрование для всех внешних соединений
- Управление секретами через Vault
- Network Policies для контроля трафика
- RBAC для управления доступом

## Локальная разработка
Для локальной разработки используется `docker-compose.yml`, который включает:
- Все необходимые сервисы для разработки
- Локальные порты для доступа
- Упрощенная конфигурация без продакшен настроек

## Мониторинг
- Health checks на портах 8080 для всех сервисов
- Метрики Spring Boot Actuator
- Логирование в структурированном формате (ECS)
- Трейсинг через Istio
- Алерты и дашборды в Grafana

## Архитектура трафика

### Поток внешних запросов
```
External Clients → Istio Ingress Gateway (80/443) → Istio Gateway → Istio Virtual Service → Services
     ↓
HTTPS/HTTP → TLS termination → Load balancing → Protocol routing → Service discovery
```

### Istio Virtual Service - Правила маршрутизации

#### PRO UI Virtual Service (lko.sbertroika.ru)
```yaml
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: pro-ui
  namespace: pro-prod
spec:
  hosts:
  - lko.sbertroika.ru
  gateways:
  - istio-ingressgateway/pro-ui-gateway
  http:
  - match:
    - uri:
        prefix: /api/v1/pro/
    route:
    - destination:
        host: pro-controller.pro-prod.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /api/v1/abt/
    route:
    - destination:
        host: abt-controller.abt-prod.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /api/v1/tms/
    route:
    - destination:
        host: tms-controller.tms-prod.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: pro-ui.pro-prod.svc.cluster.local
        port:
          number: 80
```

### Преимущества единой точки входа
- **Централизованное управление**: Все внешние запросы проходят через один компонент
- **TLS терминация**: Централизованное управление сертификатами
- **Load Balancing**: Равномерное распределение нагрузки
- **Безопасность**: Единая точка для применения security policies
- **Мониторинг**: Централизованный сбор метрик внешнего трафика

## Конфигурация внешних сервисов

### Kafka
- **Внешний кластер** с 3 брокерами
- **Адреса**: kafka1-prod-new.tkp2.prod:9092, kafka2-prod-new.tkp2.prod:9092, kafka3-prod-new.tkp2.prod:9092
- **Replication Factor**: 3
- **Управляется** вне Kubernetes

### Zookeeper
- **Внешний ансамбль** с 3 узлами
- **Адреса**: kafka-zoo-01.st.corp:2181, kafka-zoo-02.st.corp:2181, kafka-zoo-03.st.corp:2181
- **Назначение**: Координация Kafka и управление метаданными
- **Управляется** вне Kubernetes

### PostgreSQL
- **Внешняя база данных** с высокой доступностью
- **IP адрес**: ************
- **Порт**: 5432
- **База**: pro
- **Управляется** вне Kubernetes

### RabbitMQ
- **Внешний сервис** очередей сообщений
- **IP адрес**: ************
- **Порт**: 5672 (AMQP)
- **Управляется** вне Kubernetes

### ClickHouse
- **Внешняя аналитическая БД**
- **Host**: click-0.tkp2.prod
- **Порт**: 8123 (HTTP), 9000 (Native)
- **Управляется** вне Kubernetes

### Keycloak
- **Realm URL**: https://auth.sbertroika.ru/realms/release_asop
- **Client ID**: crm-ui
- **Внешний сервис** аутентификации



## Развертывание

### Kubernetes
Основная платформа развертывания с использованием Helm charts:
- **pro-ui** - Развертывание Frontend
- **pro-controller** - Развертывание API Controller
- **pro-processing** - Развертывание Processing сервиса
- **pro-manifest-processing** - Развертывание Manifest Processing
- **pro-gate** - Развертывание External API Gateway
- **pro-gate-private** - Развертывание Private API Gateway

### Service Mesh
- **Istio Ingress Gateway** - Единая точка входа для всех внешних запросов
- **Istio Gateway** - Правила маршрутизации трафика
- **Istio Virtual Service** - Внутренняя маршрутизация между сервисами

### Мониторинг и логирование
- **Prometheus + Grafana** - Сбор и визуализация метрик
- **ELK Stack** - Централизованное логирование
- **Health Checks** - Проверка состояния сервисов

### Переменные окружения
Основные переменные для конфигурации:
- `DB_URL`, `DB_USER`, `DB_PASSWORD` - Настройки postgres
- `KAFKA_SERVERS` - Настройки Kafka (kafka1-prod-new.tkp2.prod:9092;kafka2-prod-new.tkp2.prod:9092;kafka3-prod-new.tkp2.prod:9092)
- `ZOOKEEPER_NODES` - Настройки Zookeeper (kafka-zoo-01.st.corp:2181,kafka-zoo-02.st.corp:2181,kafka-zoo-03.st.corp:2181)
- `RABBITMQ_HOST` - Настройки RabbitMQ (************:5672)
- `CLICKHOUSE_HOST` - Настройки ClickHouse (click-0.tkp2.prod:8123)
- `KEYCLOAK_*` - Настройки аутентификации

## Безопасность
- Аутентификация через Keycloak OAuth2/JWT
- TLS шифрование для всех внешних соединений
- Управление секретами через Vault
- Network Policies для контроля трафика
- RBAC для управления доступом

## Локальная разработка
Для локальной разработки используется `docker-compose.yml`, который включает:
- Все необходимые сервисы для разработки
- Локальные порты для доступа
- Упрощенная конфигурация без продакшен настроек

## Мониторинг
- Health checks на портах 8080 для всех сервисов
- Метрики Spring Boot Actuator
- Логирование в структурированном формате (ECS)
- Трейсинг через Istio
- Алерты и дашборды в Grafana
