@startuml PRO Production Deployment Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle

title PRO System - Production Deployment Architecture

package "External Systems" {
    [External Clients] as clients
    [Keycloak Auth] as keycloak_auth
    [Stop List Service] as stop_list
    [<PERSON>lickHouse] as clickhouse
    [Vault] as vault
    [PostgreSQL] as postgres
    note right of postgres
        External Database
        Port: 5432
        Database: pro
        High Availability
        Backup & Recovery
        
        IP Address:
        ************
    end note
    
    [Zookeeper Ensemble] as zookeeper
    note right of zookeeper
        External Service
        Port: 2181
        Nodes: kafka-zoo-01.st.corp, kafka-zoo-02.st.corp, kafka-zoo-03.st.corp
        Kafka coordination
    end note
    
    [RabbitMQ] as rabbitmq
    note right of rabbitmq
        External Service
        Port: 5672 (AMQP)
        IP: ************
        Message queues
    end note
}

package "Load Balancer / Gateway" {
    [Istio Ingress Gateway] as ingress_gateway
    note right of ingress_gateway
        Single Entry Point
        External ports:
        - 443 (HTTPS)
        - 80 (HTTP redirect)
        All external traffic
        
        URLs & TLS:
        - https://lko.sbertroika.ru (TLS: wildcard-sbertroika-ru)
    end note
    
    [Istio Gateway] as gateway
    note right of gateway
        Traffic routing rules
        Hosts: lko.sbertroika.ru
        Protocol selection
        
        Gateway Configuration:
        
        1. pro-ui-gateway (lko.sbertroika.ru):
        - name: pro-ui-gateway
        - port: 443 (HTTPS)
        - protocol: HTTPS
        - tls: SIMPLE
        - credentialName: wildcard-sbertroika-ru
    end note
    
    [Istio Virtual Service] as virtual_service
    note right of virtual_service
        Internal routing rules
        Load balancing strategies
        Retry policies
        Circuit breaker
        Timeout configuration
        Traffic splitting
        
        Routing Rules:
        - /api/v1/pro/ → pro-controller.pro-prod.svc.cluster.local:8080
        - /api/v1/abt/ → abt-controller.abt-prod.svc.cluster.local:8080
        - /api/v1/tms/ → tms-controller.tms-prod.svc.cluster.local:8080
        - / → pro-ui.pro-prod.svc.cluster.local:80
    end note
}

package "Frontend Layer" {
    [PRO UI] as pro_ui
    note right of pro_ui
        Port: 80 (HTTP)
        Container: nginx:stable-alpine
        Build: Vue.js + Vite
        Replicas: 1
        Resources: 500m CPU, 256Mi RAM
        Purpose: Production Resource Management UI
    end note
}

package "API Gateway Layer" {
    [PRO Controller] as pro_controller
    note right of pro_controller
        Ports:
        - 8080 (HTTP/REST)
        Container: Kotlin Spring Boot
        JVM: JDK 17
        Replicas: 1
        Resources: 1000m CPU, 512Mi RAM
        Health: /actuator/health
        
        Kubernetes Service:
        pro-controller.pro-prod.svc.cluster.local
        - Port 8080: HTTP/REST API
    end note
}

package "Processing Layer" {
    [PRO Processing] as pro_processing
    note right of pro_processing
        Ports:
        - 8080 (HTTP)
        Container: Kotlin Spring Boot
        JVM: JDK 17
        Replicas: 1
        Resources: 1000m CPU, 512Mi RAM
        Health: /actuator/health
        
        Kubernetes Service:
        pro-processing.pro-prod.svc.cluster.local
        - Port 8080: HTTP API
    end note
    
    [PRO Manifest Processing] as pro_manifest_processing
    note right of pro_manifest_processing
        Ports:
        - 8080 (HTTP)
        Container: Kotlin Spring Boot
        JVM: JDK 17
        Replicas: 1
        Resources: 1000m CPU, 512Mi RAM
        Health: /actuator/health
        
        Kubernetes Service:
        pro-manifest-processing.pro-prod.svc.cluster.local
        - Port 8080: HTTP API
    end note
}

package "Gate Services" {
    [PRO Gate] as pro_gate
    note right of pro_gate
        Ports:
        - 8080 (HTTP/REST)
        Container: Kotlin Spring Boot
        JVM: JDK 17
        Replicas: 1
        Resources: 1000m CPU, 512Mi RAM
        Health: /actuator/health
        
        Kubernetes Service:
        pro-gate.pro-prod.svc.cluster.local
        - Port 8080: HTTP/REST API
    end note
    
    [PRO Gate Private] as pro_gate_private
    note right of pro_gate_private
        Ports:
        - 8080 (HTTP/REST)
        Container: Kotlin Spring Boot
        JVM: JDK 17
        Replicas: 1
        Resources: 1000m CPU, 512Mi RAM
        Health: /actuator/health
        
        Kubernetes Service:
        pro-gate-private.pro-prod.svc.cluster.local
        - Port 8080: HTTP/REST API
    end note
}

package "Infrastructure Services" {
    [Kafka Cluster] as kafka
    note right of kafka
        Port: 9092
        External Brokers:
        kafka1-prod-new.tkp2.prod:9092
        kafka2-prod-new.tkp2.prod:9092
        kafka3-prod-new.tkp2.prod:9092
        Replication Factor: 3
        Topics Management
    end note
    
    [OBS S3] as s3
    note right of s3
        Port: 9000
        External Service
        Multi-node setup
        Bucket: tkp3-manifest
        High Availability
    end note
}

package "Authentication & Security" {
    [Keycloak] as keycloak
    note right of keycloak
        Port: 8080
        High Availability
        Database: postgres
        OAuth2/JWT
        Realm: https://auth.sbertroika.ru/realms/release_asop
    end note
    
    [Vault] as vault_prod
    note right of vault_prod
        Port: 8200
        Secrets Management
        Certificate Management
        High Availability
    end note
}

' External connections - ALL traffic goes through istio-ingressgateway first
clients --> ingress_gateway : HTTPS (443)
clients --> ingress_gateway : HTTP (80)
keycloak_auth --> keycloak : OAuth2/JWT

' Traffic flow through Istio components
ingress_gateway --> gateway
gateway --> virtual_service
virtual_service --> pro_controller : HTTP (8080)
virtual_service --> pro_ui : HTTP (80)

' Frontend connections
pro_ui --> pro_controller : HTTP (8080)
pro_ui --> pro_processing : HTTP (8080)
pro_ui --> pro_manifest_processing : HTTP (8080)

' API connections
pro_controller --> postgres : JDBC/R2DBC (5432)
pro_controller --> kafka : Producer/Consumer (9092)
pro_controller --> zookeeper : Coordination (2181)
pro_controller --> s3 : S3 API (9000)
pro_controller --> keycloak : OAuth2 Client (8080)
pro_controller --> stop_list : gRPC (5000)
pro_controller --> vault_prod : Secrets (8200)
pro_controller --> clickhouse : R2DBC (8123)
pro_controller --> rabbitmq : AMQP (5672)

' Processing connections
pro_processing --> postgres : JDBC/R2DBC (5432)
pro_processing --> kafka : Consumer (9092)
pro_processing --> zookeeper : Coordination (2181)
pro_processing --> s3 : S3 API (9000)

' Manifest processing connections
pro_manifest_processing --> postgres : JDBC/R2DBC (5432)
pro_manifest_processing --> kafka : Consumer (9092)
pro_manifest_processing --> zookeeper : Coordination (2181)
pro_manifest_processing --> s3 : S3 API (9000)

' Gate connections
pro_gate --> postgres : JDBC/R2DBC (5432)
pro_gate --> kafka : Producer/Consumer (9092)
pro_gate --> keycloak : OAuth2 Client (8080)
pro_gate --> vault_prod : Secrets (8200)

pro_gate_private --> postgres : JDBC/R2DBC (5432)
pro_gate_private --> kafka : Producer/Consumer (9092)
pro_gate_private --> keycloak : OAuth2 Client (8080)
pro_gate_private --> vault_prod : Secrets (8200)

' Infrastructure connections
keycloak --> postgres : JDBC (5432)

' Kafka topics
note top of kafka
    Production Topics:
    - PRO.OPERATION.IN
    - PRO.OPERATION.SHIFT.IN
    - PRO.MANIFEST.IN
    - PRO.PROCESSING.IN
    - PRO.EVENT.FAIL.OUT
    - PRO.JOURNAL.OUT
    - PRO.ANALYTICS.OUT
end note

' Virtual Service routing rules
note top of virtual_service
    Virtual Service Rules:
    
    1. PRO UI (lko.sbertroika.ru):
    - Host: lko.sbertroika.ru
    - Gateway: istio-ingressgateway/pro-ui-gateway
    - Path: /api/v1/pro/ → pro-controller.pro-prod.svc.cluster.local:8080
    - Path: /api/v1/abt/ → abt-controller.abt-prod.svc.cluster.local:8080
    - Path: /api/v1/tms/ → tms-controller.tms-prod.svc.cluster.local:8080
    - Path: / → pro-ui.pro-prod.svc.cluster.local:80
end note

' Kubernetes configuration
note bottom of pro_controller
    Kubernetes Deployment:
    - Helm Charts: pro-ui, pro-controller, pro-processing, pro-manifest-processing, pro-gate, pro-gate-private
    - Service Mesh: Istio
    - Entry Point: istio-ingressgateway (single entry)
    - Traffic Flow: Ingress → Gateway → Virtual Service → Services
    - Monitoring: Prometheus + Grafana
    - Logging: ELK Stack
    - Secrets: Kubernetes Secrets + Vault
    
    External Services:
    - PostgreSQL: External Database (************:5432)
    - Kafka: External Cluster (3 brokers)
    - Zookeeper: External Ensemble (3 nodes)
    - RabbitMQ: External Service (************:5672)
    - ClickHouse: External Analytics DB
    - OBS S3: External Object Storage
end note



@enduml
