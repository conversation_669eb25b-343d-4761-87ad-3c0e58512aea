@startuml PRO Production Ports and Protocols
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle

title PRO System - Production Ports and Protocols

package "External Network (Internet)" {
    [External Clients] as clients
    note right of clients
        Browser/API Clients
        Ports: Dynamic (1024-65535)
        Protocols: HTTPS, HTTP
        TLS: Required for production
    end note
}

package "Load Balancer Layer" {
    [Istio Ingress Gateway] as ingress_gateway
    note right of ingress_gateway
        External Ports:
        - 80: HTTP (redirect to HTTPS)
        - 443: HTTPS (TLS termination)
        
        Internal Ports:
        - 8080: HTTP (Istio internal)
        - 8443: HTTPS (Istio internal)
        
        Functions:
        - TLS termination
        - Load balancing
        - SSL offloading
        - Rate limiting
        - DDoS protection
        
        URLs:
        - https://lko.sbertroika.ru
        - TLS: wildcard-sbertroika-ru
    end note
}

package "Istio Service Mesh" {
    [Istio Gateway] as gateway
    note right of gateway
        Configuration:
        - name: pro-ui-gateway
        - namespace: istio-ingressgateway
        - port: 443
        - protocol: HTTPS
        - tls.mode: SIMPLE
        - credentialName: wildcard-sbertroika-ru
        
        Traffic Rules:
        - Host: lko.sbertroika.ru
        - Protocol: HTTPS
        - TLS: SIMPLE
    end note
    
    [Istio Virtual Service] as virtual_service
    note right of virtual_service
        Routing Rules:
        
        1. /api/v1/pro/ → pro-controller.pro-prod.svc.cluster.local:8080
        2. /api/v1/abt/ → abt-controller.abt-prod.svc.cluster.local:8080
        3. /api/v1/tms/ → tms-controller.tms-prod.svc.cluster.local:8080
        4. / → pro-ui.pro-prod.svc.cluster.local:80
        
        Load Balancing:
        - Strategy: Round Robin
        - Retry: 3 attempts
        - Timeout: 30s
        - Circuit Breaker: Enabled
    end note
}

package "Frontend Services" {
    [PRO UI] as pro_ui
    note right of pro_ui
        Container Ports:
        - 80: HTTP (nginx)
        
        Kubernetes Service:
        - Port: 80
        - Target Port: 80
        - Type: ClusterIP
        
        Resources:
        - CPU: 500m
        - Memory: 256Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: Disabled (Vue.js app)
        - Readiness: Disabled (Vue.js app)
        
        Technology:
        - Vue.js + Vite
        - nginx:stable-alpine
        - Build: npm run build
    end note
}

package "API Services" {
    [PRO Controller] as pro_controller
    note right of pro_controller
        Container Ports:
        - 8080: HTTP (Spring Boot)
        
        Kubernetes Service:
        - Port: 8080
        - Target Port: 8080
        - Type: ClusterIP
        
        Resources:
        - CPU: 1000m
        - Memory: 512Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: /actuator/health/liveness
        - Readiness: /actuator/health/readiness
        - Port: 8080
        
        Technology:
        - Kotlin + Spring Boot
        - JDK 17
        - JVM Options: -Xmx512m
    end note
    
    [PRO Processing] as pro_processing
    note right of pro_processing
        Container Ports:
        - 8080: HTTP (Spring Boot)
        
        Kubernetes Service:
        - Port: 8080
        - Target Port: 8080
        - Type: ClusterIP
        
        Resources:
        - CPU: 1000m
        - Memory: 512Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: /actuator/health/liveness
        - Readiness: /actuator/health/readiness
        - Port: 8080
        
        Technology:
        - Kotlin + Spring Boot
        - JDK 17
        - JVM Options: -Xmx512m
    end note
    
    [PRO Manifest Processing] as pro_manifest_processing
    note right of pro_manifest_processing
        Container Ports:
        - 8080: HTTP (Spring Boot)
        
        Kubernetes Service:
        - Port: 8080
        - Target Port: 8080
        - Type: ClusterIP
        
        Resources:
        - CPU: 1000m
        - Memory: 512Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: /actuator/health/liveness
        - Readiness: /actuator/health/readiness
        - Port: 8080
        
        Technology:
        - Kotlin + Spring Boot
        - JDK 17
        - JVM Options: -Xmx512m
    end note
}

package "Gate Services" {
    [PRO Gate] as pro_gate
    note right of pro_gate
        Container Ports:
        - 8080: HTTP (Spring Boot)
        
        Kubernetes Service:
        - Port: 8080
        - Target Port: 8080
        - Type: ClusterIP
        
        Resources:
        - CPU: 1000m
        - Memory: 512Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: /actuator/health/liveness
        - Readiness: /actuator/health/readiness
        - Port: 8080
        
        Technology:
        - Kotlin + Spring Boot
        - JDK 17
        - JVM Options: -Xmx512m
    end note
    
    [PRO Gate Private] as pro_gate_private
    note right of pro_gate_private
        Container Ports:
        - 8080: HTTP (Spring Boot)
        
        Kubernetes Service:
        - Port: 8080
        - Target Port: 8080
        - Type: ClusterIP
        
        Resources:
        - CPU: 1000m
        - Memory: 512Mi
        - Replicas: 1
        
        Health Checks:
        - Liveness: /actuator/health/liveness
        - Readiness: /actuator/health/readiness
        - Port: 8080
        
        Technology:
        - Kotlin + Spring Boot
        - JDK 17
        - JVM Options: -Xmx512m
    end note
}

package "External Infrastructure" {
    [PostgreSQL] as postgres
    note right of postgres
        External Database
        Port: 5432
        Protocol: TCP
        
        IP Address:
        - ************:5432
        

        
        Connection:
        - JDBC/R2DBC
        - Connection Pool: HikariCP
        - Max Connections: 20
        - Timeout: 30s
    end note
    
    [Kafka Cluster] as kafka
    note right of kafka
        External Message Broker
        Port: 9092
        Protocol: TCP
        
        Brokers:
        - kafka1-prod-new.tkp2.prod:9092
        - kafka2-prod-new.tkp2.prod:9092
        - kafka3-prod-new.tkp2.prod:9092
        

        
        Configuration:
        - Replication Factor: 3
        - Producer: acks=all
        - Consumer: auto.offset.reset=earliest
    end note
    
    [Zookeeper Ensemble] as zookeeper
    note right of zookeeper
        External Coordination Service
        Port: 2181
        Protocol: TCP
        
        Nodes:
        - kafka-zoo-01.st.corp:2181
        - kafka-zoo-02.st.corp:2181
        - kafka-zoo-03.st.corp:2181
        

        
        Configuration:
        - Session Timeout: 6000ms
        - Connection Timeout: 6000ms
    end note
    
    [RabbitMQ] as rabbitmq
    note right of rabbitmq
        External Message Queue
        Port: 5672
        Protocol: AMQP
        
        IP Address: ************
        

        
        Configuration:
        - Virtual Host: /
        - Heartbeat: 60s
        - Connection Timeout: 30s
        - Channel Pool: 10
    end note
    
    [ClickHouse] as clickhouse
    note right of clickhouse
        External Analytics Database
        Ports:
        - 8123: HTTP
        - 9000: Native
        
        Host: click-0.tkp2.prod
        

        
        Configuration:
        - HTTP Port: 8123
        - Native Port: 9000
        - Connection Pool: 5
        - Query Timeout: 60s
    end note
}

package "Authentication & Security" {
    [Keycloak] as keycloak
    note right of keycloak
        External Identity Provider
        Port: 8080
        Protocol: HTTPS
        
        Configuration:
        - Realm: release_asop
        - URL: https://auth.sbertroika.ru
        - Client ID: crm-ui
        - Protocol: OAuth2/OIDC
        
        Security:
        - TLS: Required
        - JWT Signing: RS256
        - Access Token TTL: 5m
        - Refresh Token TTL: 30m
    end note
    
    [Vault] as vault
    note right of vault
        External Secrets Manager
        Port: 8200
        Protocol: HTTPS
        
        Configuration:
        - TLS: Required
        - Auth Method: Kubernetes
        - Secrets Engine: KV v2
        - Lease TTL: 1h
        
        Access:
        - Service Account: pro-system
        - Namespace: pro-prod
        - Policies: pro-secrets-read
    end note
}

package "Storage Services" {
    [OBS S3] as s3
    note right of s3
        External Object Storage
        Port: 9000
        Protocol: HTTPS
        
        Configuration:
        - Endpoint: https://s3.sbertroika.ru
        - Bucket: tkp3-manifest
        - Region: ru-central1
        
        Access:
        - Access Key ID: Environment variable
        - Secret Access Key: Vault
        - Session Token: Vault
        
        Operations:
        - Upload: Multipart (100MB chunks)
        - Download: Streaming
        - Delete: Soft delete
    end note
}

' Network Flow - External to Internal
clients --> ingress_gateway : HTTPS (443) / HTTP (80)
ingress_gateway --> gateway : Internal routing
gateway --> virtual_service : Traffic distribution
virtual_service --> pro_ui : HTTP (80)
virtual_service --> pro_controller : HTTP (8080)
virtual_service --> pro_processing : HTTP (8080)
virtual_service --> pro_manifest_processing : HTTP (8080)

' Internal Service Communication
pro_ui --> pro_controller : HTTP (8080)
pro_ui --> pro_processing : HTTP (8080)
pro_ui --> pro_manifest_processing : HTTP (8080)

' External Service Connections
pro_controller --> postgres : TCP (5432)
pro_controller --> kafka : TCP (9092)
pro_controller --> zookeeper : TCP (2181)
pro_controller --> rabbitmq : AMQP (5672)
pro_controller --> clickhouse : HTTP (8123)
pro_controller --> s3 : HTTPS (9000)
pro_controller --> keycloak : HTTPS (8080)
pro_controller --> vault : HTTPS (8200)

pro_processing --> postgres : TCP (5432)
pro_processing --> kafka : TCP (9092)
pro_processing --> zookeeper : TCP (2181)
pro_processing --> s3 : HTTPS (9000)

pro_manifest_processing --> postgres : TCP (5432)
pro_manifest_processing --> kafka : TCP (9092)
pro_manifest_processing --> zookeeper : TCP (2181)
pro_manifest_processing --> s3 : HTTPS (9000)

pro_gate --> postgres : TCP (5432)
pro_gate --> kafka : TCP (9092)
pro_gate --> keycloak : HTTPS (8080)
pro_gate --> vault : HTTPS (8200)

pro_gate_private --> postgres : TCP (5432)
pro_gate_private --> kafka : TCP (9092)
pro_gate_private --> keycloak : HTTPS (8080)
pro_gate_private --> vault : HTTPS (8200)

' Infrastructure Connections
keycloak --> postgres : TCP (5432)

' Port Summary
note bottom of ingress_gateway
    External Ports Summary:
    
    Internet → Istio Ingress Gateway:
    - 80: HTTP (redirect to 443)
    - 443: HTTPS (TLS termination)
    
    Internal Service Ports:
    - 8080: All Spring Boot services
    - 80: PRO UI (nginx)
    
    External Service Ports:
    - 5432: PostgreSQL
    - 9092: Kafka (3 brokers)
    - 2181: Zookeeper (3 nodes)
    - 5672: RabbitMQ
    - 8123: ClickHouse HTTP
    - 9000: ClickHouse Native
    - 8080: Keycloak
    - 8200: Vault
    - 9000: OBS S3
end note

' Security Notes
note top of keycloak
    Security Configuration:
    
    TLS Requirements:
    - All external connections: TLS 1.2+
    - Internal cluster: mTLS (Istio)
    - Certificate validation: Required
    
    Authentication:
    - Keycloak OAuth2/OIDC
    - JWT tokens with RS256 signing
    - Service-to-service: mTLS
    
    Authorization:
    - RBAC in Kubernetes
    - Service account permissions
    - Network policies
end note

' Monitoring Notes
note right of virtual_service
    Monitoring & Observability:
    
    Metrics:
    - Prometheus endpoints: /actuator/prometheus
    - Custom metrics: Business KPIs
    - Istio metrics: Traffic flow
    
    Logging:
    - Structured logging (ECS format)
    - Centralized: ELK Stack
    - Log levels: INFO, WARN, ERROR
    
    Tracing:
    - Istio distributed tracing
    - Jaeger integration
    - Trace sampling: 10%
    
    Health Checks:
    - Liveness: /actuator/health/liveness
    - Readiness: /actuator/health/readiness
    - Startup: /actuator/health/startup
end note

@enduml
