-- Добавление поля is_delete в таблицу tariff_constraint
ALTER TABLE tariff_constraint ADD COLUMN tc_is_delete BOOLEAN DEFAULT FALSE NOT NULL;

-- Добавление поля is_delete в таблицу tariff_constraint_ex
ALTER TABLE tariff_constraint_ex ADD COLUMN tce_is_delete BOOLEAN DEFAULT FALSE NOT NULL;

-- Создание индексов для улучшения производительности запросов
CREATE INDEX idx_tariff_constraint_is_delete ON tariff_constraint(tc_is_delete);
CREATE INDEX idx_tariff_constraint_ex_is_delete ON tariff_constraint_ex(tce_is_delete);

-- Добавление комментариев к полям
COMMENT ON COLUMN tariff_constraint.tc_is_delete IS 'Флаг логического удаления ограничения тарифа';
COMMENT ON COLUMN tariff_constraint_ex.tce_is_delete IS 'Флаг логического удаления исключения ограничения тарифа'; 