package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import ru.sbertroika.tkp3.pro.manifest.processing.util.timestampNow
import ru.sbertroika.tkp3.pro.model.*
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

interface RouteCrudRepository : CoroutineCrudRepository<Route, RoutePK> {

    @Query("""
        SELECT *
        FROM route r
        INNER JOIN (
            SELECT r_id, MAX(r_version) vers
            FROM route
            GROUP BY r_id
        ) r2 ON r.r_id = r2.r_id AND r.r_version = r2.vers and r.r_project_id = :projectId
    """)
    suspend fun findAllByProjectId(projectId: UUID): Flow<Route>
}

interface RouteConstraintRepository : CoroutineCrudRepository<RouteConstraint, RouteConstraintPK> {

    @Query("""
        SELECT *
        FROM route_constraint r
        INNER JOIN (
            SELECT rc_id, MAX(rc_version) vers
            FROM route_constraint
            GROUP BY rc_id
        ) r2 ON r.rc_id = r2.rc_id AND r.rc_version = r2.vers and r.r_id = :routeId
    """)
    suspend fun findAllByRouteId(routeId: UUID): Flow<RouteConstraint>
}

interface RouteConstraintExceptionRepository : CoroutineCrudRepository<RouteConstraintException, RouteConstraintExceptionPK> {

    @Query("""
        SELECT *
        FROM route_constraint_ex r
        INNER JOIN (
            SELECT rce_id, MAX(rce_version) vers
            FROM route_constraint_ex
            GROUP BY rce_id
        ) r2 ON r.rce_id = r2.rce_id AND r.rce_version = r2.vers and r.rc_id = :routeConstraintId
    """)
    suspend fun findAllByRouteConstraintId(routeConstraintId: UUID): Flow<RouteConstraintException>
}

interface RouteStationRepository : CoroutineCrudRepository<RouteStation, RouteStationPK> {

    @Query("""
        SELECT *
        FROM route_station r
        INNER JOIN (
            SELECT rs_id, MAX(rs_version) vers
            FROM route_station
            GROUP BY rs_id
        ) r2 ON r.rs_id = r2.rs_id AND r.rs_version = r2.vers and r.r_id = :routeId
    """)
    suspend fun findAllByRouteId(routeId: UUID): Flow<RouteStation>
}

interface DispatchingRouteOrganizationRepository : CoroutineCrudRepository<DispatchingRouteOrganization, DispatchingRouteOrganizationPK> {
    suspend fun findAllByRouteId(routeId: UUID): Flow<DispatchingRouteOrganization>
}


@Component
class RouteRepository(
    override val dbClient: DatabaseClient,
    override val repository: RouteCrudRepository
): AbstractSettingsRepository<Route, RoutePK>(dbClient, repository){
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM route o\n" +
            "INNER JOIN (\n" +
            "    SELECT r_id, MAX(r_version) vers\n" +
            "    FROM route\n" +
            "    GROUP BY r_id\n" +
            ") o2 ON o.r_id = o2.r_id AND o.r_version = o2.vers AND o.r_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Route(
        id = t.get("r_id") as UUID,
        version = t.get("r_version") as Int,
        name = t.get("r_name") as String,
        tags = t.get("tags") as String?,
        status = t.get("r_status") as RouteStatus,
        activeFrom = if (t.get("r_active_from") == null) null else Timestamp.valueOf(t.get("r_active_from") as LocalDateTime),
        activeTill = if (t.get("r_active_till") == null) null else Timestamp.valueOf(t.get("r_active_till") as LocalDateTime),
        index = t.get("r_index") as Int?,
        number = t.get("r_number") as String?,
        scheme = if (t.get("r_scheme") == null) null else t.get("r_scheme") as RouteScheme
    )

    override suspend fun findById(id: String): Route? {
        return dbClient.sql("${getQuery()} AND o.r_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Route> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Route> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    fun addNumberFilter(query: String, number : String) = "$query AND o.r_number = '$number'"
    fun addOrgIdFilter(query: String, orgId : String) = "$query AND o.r_org_id = '%$orgId%'"
    fun addNameFilter(query: String, name : String) = "$query AND o.r_name LIKE '%$name%'"


    fun findAllByRequest(query: String): Flow<Route> {
        return dbClient.sql(query)
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != RouteStatus.IS_DELETED) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = RouteStatus.IS_DELETED
            ))
        }
    }

} 