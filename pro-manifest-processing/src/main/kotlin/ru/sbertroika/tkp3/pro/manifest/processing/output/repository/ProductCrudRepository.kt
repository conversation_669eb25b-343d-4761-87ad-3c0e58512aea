package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import ru.sbertroika.tkp3.pro.manifest.processing.util.timestampNow
import ru.sbertroika.tkp3.pro.model.*
import java.util.*

interface ProductCrudRepository : CoroutineCrudRepository<Product, ProductPK> {

    @Query("""
        SELECT *
        FROM product p
        INNER JOIN (
            SELECT p_id, MAX(p_version) vers
            FROM product
            GROUP BY p_id
        ) p2 ON p.p_id = p2.p_id AND p.p_version = p2.vers and p.p_project_id = :projectId
    """)
    suspend fun findAllByProjectId(projectId: UUID): Flow<Product>
}

interface ProductDictRowRepository : CoroutineCrudRepository<ProductDictRow, ProductDictRowPK> {

    @Query("""
        SELECT *
        FROM product_dict_row o
        INNER JOIN (
            SELECT pdr_id, MAX(pdr_version) vers
            FROM product_dict_row
            GROUP BY pdr_id
        ) o2 ON o.pdr_id = o2.pdr_id AND o.pdr_version = o2.vers and o.pdr_project_id = :projectId
    """)
    suspend fun findAllByProjectId(projectId: UUID): Flow<ProductDictRow>
}

interface ProductDictRowTariffMatrixPriceRepository : CoroutineCrudRepository<ProductDictRowTariffMatrixPrice, ProductDictRowTariffMatrixPricePK> {

    @Query("""
        SELECT *
        FROM product_dict_row_tariff_matrix_price o
        INNER JOIN (
            SELECT pdrtmp_id, MAX(pdrtmp_version) vers
            FROM product_dict_row_tariff_matrix_price
            GROUP BY pdrtmp_id
        ) o2 ON o.pdrtmp_id = o2.pdrtmp_id AND o.pdrtmp_version = o2.vers and o.pdr_id = :productDicRowId
    """)
    suspend fun findAllByProductDicRowId(productDicRowId: UUID): Flow<ProductDictRowTariffMatrixPrice>
}

@Component
class ProductRepository(
    override val dbClient: DatabaseClient,
    override val repository: ProductCrudRepository
): AbstractSettingsRepository<Product, ProductPK>(dbClient, repository){
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM product o\n" +
            "INNER JOIN (\n" +
            "    SELECT p_id, MAX(p_version) vers\n" +
            "    FROM product\n" +
            "    GROUP BY p_id\n" +
            ") o2 ON o.p_id = o2.p_id AND o.p_version = o2.vers AND o.p_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Product(
        id = t.get("p_id") as UUID,
        version = t.get("p_version") as Int,
        name = t.get("p_name") as String,
        tags = t.get("tags") as String?,
        status = t.get("p_status") as String
    )

    override suspend fun findById(id: String): Product? {
        return dbClient.sql("${getQuery()} AND o.p_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Product> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Product> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != ProductStatus.IS_DELETED.name) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = ProductStatus.IS_DELETED.name
            ))
        }
    }

} 