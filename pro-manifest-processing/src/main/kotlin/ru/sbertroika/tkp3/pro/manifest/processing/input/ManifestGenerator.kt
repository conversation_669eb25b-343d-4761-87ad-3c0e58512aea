package ru.sbertroika.tkp3.pro.manifest.processing.input

import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.manifest.model.pro.JournalOperationType
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.model.pro.OperationStatus
import ru.sbertroika.tkp3.pro.manifest.processing.model.GenManifestJob
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.ProjectRepository
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.JournalService
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.ManifestGeneratorService
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.MqService
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID

@Component
class ManifestGenerator(
    private val projectRepository: ProjectRepository,
    private val manifestGeneratorService: ManifestGeneratorService,
    private val journalService: JournalService,
    private val mqService: MqService
) {
    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    @Scheduled(cron = "0 5 0 * * *")
//    @Scheduled(cron = "0 * * * * *")
    fun run() = runBlocking {
        val currDate = LocalDate.now()
        logger.info("Start generate manifest for the current day: ${currDate.format(fmt)}")
        val projectList = try {
            projectRepository.findAllActive()
        } catch (e: Exception) {
            logger.error("Error request all active projects", e)
            throw e
        }

        if (projectList.isEmpty()) {
            logger.info("Project list is empty")
        }

        projectList.forEach { project ->
            val jobId = UUID.randomUUID()
            logger.info(
                "Start generate manifest for the current day={} with project={}",
                currDate.format(fmt),
                project.id
            )
            journalService.logOperation(
                ManifestJournal(
                    jobId = jobId,
                    type = JournalOperationType.GEN_DAY_MANIFEST_JOB,
                    projectId = project.id,
                    projectVersion = project.version,
                    manifestDate = currDate,
                    manifestVersion = 1,
                    status = OperationStatus.NEW_JOB
                )
            )

            manifestGeneratorService.generateManifest(project, currDate, jobId = jobId).fold(
                {
                    logger.error(
                        "Manifest generation the current day={} with project={} failed: {}",
                        currDate.format(fmt),
                        project.id,
                        it
                    )
                    mqService.addJob(
                        GenManifestJob(
                            jobId = jobId,
                            projectId = project.id!!,
                            projectVersion = project.version,
                            date = currDate
                        )
                    )
                }, {
                    logger.info(
                        "Finish generate manifest for the current day={} with project={}",
                        currDate.format(fmt),
                        project.id
                    )
                }
            )
        }

        logger.info("Finish all process generate manifest for the current day: ${currDate.format(fmt)}")
    }

    companion object {
        private val fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    }
}