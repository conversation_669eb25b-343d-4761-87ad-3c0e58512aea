package ru.sbertroika.tkp3.pro.manifest.processing.input

import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.manifest.model.pro.JournalOperationType
import ru.sbertroika.tkp3.manifest.model.pro.ManifestJournal
import ru.sbertroika.tkp3.manifest.model.pro.OperationStatus
import ru.sbertroika.tkp3.manifest.starter.ManifestChangeListener
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.pro.manifest.processing.model.GenManifestJob
import ru.sbertroika.tkp3.pro.manifest.processing.output.repository.ProjectRepository
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.JournalService
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.MqService
import java.time.LocalDate
import java.util.*
import javax.annotation.PostConstruct

@Component
class ManifestChangeConsumer(
    private val manifestService: ManifestService,
    private val journalService: JournalService,
    private val mqService: MqService,
    private val projectRepository: ProjectRepository
) {

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    private val listener = object : ManifestChangeListener {
        override fun onChange(path: String, data: String?) {
            GlobalScope.launch {
                if (data.isNullOrEmpty()) {
                    log.info(this.javaClass.name, "Request refresh manifest by projectId $path")
                    val jobId = UUID.randomUUID()
                    val projectId = UUID.fromString(path)
                    journalService.logOperation(
                        ManifestJournal(
                            jobId = jobId,
                            type = JournalOperationType.CHANGE_MANIFEST_UPDATE_JOB,
                            projectId = projectId,
                            status = OperationStatus.NEW_JOB
                        )
                    )

                    val project = projectRepository.findAllById(projectId).maxByOrNull { it.version!! }
                    if (project == null) {
                        journalService.logOperation(
                            ManifestJournal(
                                jobId = jobId,
                                type = JournalOperationType.CHANGE_MANIFEST_UPDATE_JOB,
                                projectId = projectId,
                                status = OperationStatus.FAIL,
                                errors = listOf("Project $projectId not found")
                            )
                        )
                    } else {
                        mqService.addJob(
                            GenManifestJob(
                                jobId = jobId,
                                projectId = projectId,
                                projectVersion = project.version,
                                date = LocalDate.now(),
                                isUpdateVersion = true
                            )
                        ).fold(
                            {
                                log.error("Error create job for project $projectId", it)
                                journalService.logOperation(
                                    ManifestJournal(
                                        jobId = jobId,
                                        type = JournalOperationType.CHANGE_MANIFEST_UPDATE_JOB,
                                        projectId = projectId,
                                        status = OperationStatus.FAIL,
                                        errors = listOf("Error create job for project $projectId: ${it.message}")
                                    )
                                )
                            },
                            {
                                log.info("New create job for project $projectId")
                            }
                        )

                    }
                }
            }
        }
    }

    @PostConstruct
    fun setup() {
        manifestService.runWatcher(listener)

        //Test
//        GlobalScope.launch {
//            println("-----")
//            manifestService.getManifest("43db9de7-72ec-4eae-8978-8aef1c46873a-2024-01-17", 1).fold(
//                {
//                    it.printStackTrace()
//                },
//                {
//                    println(it)
//                }
//            )
//            println("-----")
//            manifestService.getManifestByProject("43db9de7-72ec-4eae-8978-8aef1c46873a").fold(
//                {
//                    it.printStackTrace()
//                },
//                {
//                    println(it)
//                }
//            )
//            println("-----")
//        }
    }
}