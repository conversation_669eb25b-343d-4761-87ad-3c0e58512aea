package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import ru.sbertroika.tkp3.pro.manifest.processing.util.timestampNow
import ru.sbertroika.tkp3.pro.model.*
import java.util.*

interface VehicleCrudRepository : CoroutineCrudRepository<Vehicle, VehiclePK> {

    @Query("""
        SELECT *
        FROM vehicle v
        INNER JOIN (
            SELECT vh_id, MAX(vh_version) vers
            FROM vehicle
            GROUP BY vh_id
        ) v2 ON v.vh_id = v2.vh_id AND v.vh_version = v2.vers and v.v_project_id = :projectId
    """)
    suspend fun findAllByProjectId(projectId: UUID): Flow<Vehicle>
}

interface VehicleConstraintRepository : CoroutineCrudRepository<VehicleConstraint, VehicleConstraintPK> {

    @Query("""
        SELECT *
        FROM vehicle_constraint v
        INNER JOIN (
            SELECT vhc_id, MAX(vhc_version) vers
            FROM vehicle_constraint
            GROUP BY vhc_id
        ) v2 ON v.vhc_id = v2.vhc_id AND v.vhc_version = v2.vers and v.vh_id = :vehicleId
    """)
    suspend fun findAllByVehicleId(vehicleId: UUID): Flow<VehicleConstraint>
}

interface VehicleConstraintExceptionRepository : CoroutineCrudRepository<VehicleConstraintException, VehicleConstraintExceptionPK> {

    @Query("""
        SELECT *
        FROM vehicle_constraint_ex v
        INNER JOIN (
            SELECT vhce_id, MAX(vhce_version) vers
            FROM vehicle_constraint_ex
            GROUP BY vhce_id
        ) v2 ON v.vhce_id = v2.vhce_id AND v.vhce_version = v2.vers and v.vhc_id = :vehicleConstraintId
    """)
    suspend fun findAllByVehicleConstraintId(vehicleConstraintId: UUID): Flow<VehicleConstraintException>
}


@Component
class VehicleRepository(
    override val dbClient: DatabaseClient,
    override val repository: VehicleCrudRepository
): AbstractSettingsRepository<Vehicle, VehiclePK>(dbClient, repository){
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM vehicle o\n" +
            "INNER JOIN (\n" +
            "    SELECT vh_id, MAX(vh_version) vers\n" +
            "    FROM vehicle\n" +
            "    GROUP BY vh_id\n" +
            ") o2 ON o.vh_id = o2.vh_id AND o.vh_version = o2.vers AND o.vh_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Vehicle(
        id = t.get("vh_id") as UUID,
        version = t.get("vh_version") as Int,
        tags = t.get("tags") as String?,
        status = t.get("vh_status") as VehicleStatus,
        number = t.get("vh_number") as String,
        type = t.get("vh_type") as VehicleType
    )

    override suspend fun findById(id: String): Vehicle? {
        return dbClient.sql("${getQuery()} AND o.vh_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Vehicle> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Vehicle> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    fun addNumberFilter(query: String, number : String) = "$query AND o.vh_number = '$number'"

    fun findAllByRequest(query: String): Flow<Vehicle> {
        return dbClient.sql(query)
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != VehicleStatus.IS_DELETED) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = VehicleStatus.IS_DELETED
            ))
        }
    }

} 