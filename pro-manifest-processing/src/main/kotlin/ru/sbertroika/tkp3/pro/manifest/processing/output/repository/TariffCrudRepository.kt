package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import ru.sbertroika.tkp3.pro.manifest.processing.util.timestampNow
import ru.sbertroika.tkp3.pro.model.*
import java.util.*

interface TariffCrudRepository : CoroutineCrudRepository<Tariff, TariffPK> {

    @Query("""
        SELECT *
        FROM tariff t
        INNER JOIN (
            SELECT t_id, MAX(t_version) vers
            FROM tariff
            GROUP BY t_id
        ) t2 ON t.t_id = t2.t_id AND t.t_version = t2.vers and t.t_project_id = :projectId
    """)
    suspend fun findAllByProjectId(projectId: UUID): Flow<Tariff>
}

interface TariffConstraintRepository : CoroutineCrudRepository<TariffConstraint, TariffConstraintPK> {

    @Query("""
        SELECT *
        FROM tariff_constraint t
        INNER JOIN (
            SELECT tc_id, MAX(tc_version) vers
            FROM tariff_constraint
            GROUP BY tc_id
        ) t2 ON t.tc_id = t2.tc_id AND t.tc_version = t2.vers and t.t_id = :tariffId
    """)
    suspend fun findAllByTariffId(tariffId: UUID): Flow<TariffConstraint>
}

interface TariffConstraintExceptionRepository : CoroutineCrudRepository<TariffConstraintException, TariffConstraintExceptionPK> {

    @Query("""
        SELECT *
        FROM tariff_constraint_ex t
        INNER JOIN (
            SELECT tce_id, MAX(tce_version) vers
            FROM tariff_constraint_ex
            GROUP BY tce_id
        ) t2 ON t.tce_id = t2.tce_id AND t.tce_version = t2.vers and t.tc_id = :tariffConstraintId
    """)
    suspend fun findAllByTariffConstraintId(tariffConstraintId: UUID): Flow<TariffConstraintException>
}


@Component
class TariffRepository(
    override val dbClient: DatabaseClient,
    override val repository: TariffCrudRepository
): AbstractSettingsRepository<Tariff, TariffPK>(dbClient, repository){
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM tariff o\n" +
            "INNER JOIN (\n" +
            "    SELECT t_id, MAX(t_version) vers\n" +
            "    FROM tariff\n" +
            "    GROUP BY t_id\n" +
            ") o2 ON o.t_id = o2.t_id AND o.t_version = o2.vers AND o.t_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Tariff(
        id = t.get("t_id") as UUID,
        version = t.get("t_version") as Int,
        name = t.get("t_name") as String,
        tags = t.get("tags") as String?,
        status = t.get("t_status") as TariffStatus
    )
    override suspend fun findById(id: String): Tariff? {
        return dbClient.sql("${getQuery()} AND o.t_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Tariff> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Tariff> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != TariffStatus.IS_DELETED) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = TariffStatus.IS_DELETED
            ))
        }
    }

} 