package ru.sbertroika.tkp3.pro.manifest.processing.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.model.ProjectFunction
import ru.sbertroika.tkp3.pro.model.ProjectFunctionPK
import ru.sbertroika.tkp3.pro.model.ProjectPK
import java.util.*

interface ProjectRepository : CoroutineCrudRepository<Project, ProjectPK> {

    @Query("SELECT p1.*\n" +
            "FROM project p1\n" +
            "WHERE p1.pr_version = (\n" +
            "   SELECT MAX(p2.pr_version)\n" +
            "   FROM project p2\n" +
            "   WHERE p2.pr_id = p1.pr_id\n" +
            ") and pr_status in ('ACTIVE','DEMO','TEST')")
    suspend fun findAllActive(): List<Project>

    suspend fun findAllById(id: UUID): List<Project>

    suspend fun findAllByIdAndVersion(id: UUID, version: Int): Project?
}

interface ProjectFunctionRepository : CoroutineCrudRepository<ProjectFunction, ProjectFunctionPK> {

    suspend fun findAllByProjectIdAndVersion(projectId: UUID, version: Int): Flow<ProjectFunction>
}