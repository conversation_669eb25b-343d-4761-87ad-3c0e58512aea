package ru.sbertroika.tkp3.pro.manifest.processing.output.service

import arrow.core.Either
import io.grpc.ManagedChannelBuilder
import okio.IOException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.common.manifest.v1.manifestRequest
import ru.sbertroika.pasiv.gate.v1.PASIVGateServiceGrpcKt
import ru.sbertroika.tkp3.manifest.model.FeatureState
import ru.sbertroika.tkp3.manifest.model.ManifestPasiv
import ru.sbertroika.tkp3.manifest.model.ManifestPasivDict
import ru.sbertroika.tkp3.manifest.model.TkpFeature
import ru.sbertroika.tkp3.manifest.model.pasiv.Organization
import java.util.*
import java.util.concurrent.TimeUnit

@Service
class PasivServiceImpl(
    @Value("\${pasiv_gateway_url}")
    private val gateUrl: String
) : PasivService {
    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    private val channel = ManagedChannelBuilder.forTarget(gateUrl)
        .usePlaintext()
        .idleTimeout(60000, TimeUnit.MILLISECONDS)
        .keepAliveTimeout(60000, TimeUnit.MILLISECONDS)
        .enableRetry()
        .maxRetryAttempts(3)
        .build()

    val client = PASIVGateServiceGrpcKt.PASIVGateServiceCoroutineStub(channel)

    override suspend fun getManifest(contractId: String, jobId: UUID): Either<Throwable, ManifestPasiv> = Either.catch {


        try {
            val response = client.getManifest(
                manifestRequest {
                    this.projectId = contractId
                }
            )

            if (response.hasError()) {
                logger.error("Error during GET request: ${response.error}, jobId=$jobId")
                throw Error(response.error.message)
            }

            ManifestPasiv(
                features = response.manifest.featuresList.map {
                    TkpFeature(
                        name = it.name,
                        state = FeatureState.valueOf(it.state.name)
                    )
                }.toList(),
                dict = ManifestPasivDict(organization = response.manifest.dict.organizationList.map { org ->
                    Organization(
                        id = UUID.fromString(org.id),
                        name = org.name,
                        shortName = org.shortName,
                        inn = org.inn,
                        kpp = org.kpp,
                        address = org.address,
                        paymentPlace = org.paymentPlace
                    )
                }.toList())
            )
        } catch (e: Exception) {
            if (e is IOException) {
                logger.error("No connection with pasiv, jobId=$jobId", e)
            } else {
                logger.error("Error make pasiv manifest, jobId=$jobId", e)
            }
            throw e
        }
    }
}