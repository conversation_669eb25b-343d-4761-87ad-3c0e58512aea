spring:
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:**********:9092;***********:9092;**********:9092}
#    bootstrap-servers: ${KAFKA_SERVERS:************:9092,***********:9092,************:9092} #prod

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:**************************************************************/develop_pro}

  rabbitmq:
    host: ${RABBITMQ_HOST:**********}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:manifest}
    password: ${RABBITMQ_PASSWORD:f23K39ucbnDD}
    virtual-host: ${RABBITMQ_VHOST:manifest}
    processing_queue_name: ${QUEUE_PROCESSING_NAME:crm-manifest-gen-jobs}
    processing_delayed_execution_queue_name: ${QUEUE_PROCESSING_DELAYED_EXECUTION_NAME:crm-manifest-gen-jobs-delayed-execution}
    listener:
      direct:
        acknowledge-mode: manual
      simple:
        acknowledge-mode: manual

server:
  port: 0

s3:
  url: ${S3_URL:https://obs.ru-moscow-1.hc.sbercloud.ru}
  access_key_id: ${S3_ACCESS_KEY_ID:XSRWBOPX5DDYCOYFQJUZ}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:CSUFFlymFtfovFI0hlyEyyyqX5isVcYFHvGKsHjV}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest-dev}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:***********:2181,**********:2181,***********:2181}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3-dev}

abt_gateway_url: ${ABT_GATEWAY_URL:localhost:5010}
pasiv_gateway_url: ${PASIV_GATEWAY_URL:localhost:5012}