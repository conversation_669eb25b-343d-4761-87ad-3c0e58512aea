spring:
  application:
    name: pro-manifest-processing
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    operation_journal_topic: ${MANIFEST_JOURNAL_TOPIC:PRO.MANIFEST.JOURNAL}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:f23K39ucbnDD}
    virtual-host: ${RABBITMQ_VHOST:/}
    processing_queue_name: ${QUEUE_PROCESSING_NAME:crm-manifest-gen-jobs}
    processing_delayed_execution_queue_name: ${QUEUE_PROCESSING_DELAYED_EXECUTION_NAME:crm-manifest-gen-jobs-delayed-execution}
    listener:
      direct:
        acknowledge-mode: manual
      simple:
        acknowledge-mode: manual

s3:
  url: ${S3_URL}
  access_key_id: ${S3_ACCESS_KEY_ID}
  secret_access_key: ${S3_SECRET_ACCESS_KEY}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181,localhost:2182,localhost:2183}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

server:
  port: 8080

abt_gateway_url: ${ABT_GATEWAY_URL:abt-gate.abt.svc.cluster.local:5000}
pasiv_gateway_url: ${PASIV_GATEWAY_URL:pasiv-gate.pasiv.svc.cluster.local:5000}
system_user_id: ${SYSTEM_USER_ID:a223116c-49b4-43eb-8a47-c76a68a4225d}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'
