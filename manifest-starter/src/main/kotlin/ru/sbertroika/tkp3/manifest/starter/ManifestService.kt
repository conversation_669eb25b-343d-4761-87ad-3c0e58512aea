package ru.sbertroika.tkp3.manifest.starter

import arrow.core.Either
import ru.sbertroika.tkp3.manifest.model.Manifest

interface ManifestService {

    fun runWatcher(listener: ManifestChangeListener)

    suspend fun setManifestPath(manifestPath: String, projectId: String, withCash: Boolean = true)

    suspend fun resetManifest(projectId: String)

    suspend fun getManifestPath(projectId: String): Either<Throwable, String>

    suspend fun getManifest(manifestPath: String): Either<Throwable, Manifest>

    suspend fun getManifest(id: String, version: Int): Either<Throwable, Manifest>

    suspend fun getManifestByProject(projectId: String): Either<Throwable, Manifest?>

    suspend fun saveManifest(manifest: Manifest): Either<Throwable, String>
}